package com.renda.core.service;

import java.util.List;
import com.renda.core.domain.RdNoticeFeedback;
import com.renda.core.domain.vo.FeedbackInfo2VO;
import com.renda.system.domain.SysNotice;

/**
 * 工作通知反馈Service接口
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
public interface IRdNoticeFeedbackService
{
    /**
     * 查询工作通知反馈
     *
     * @param id 工作通知反馈主键
     * @return 工作通知反馈
     */
    public RdNoticeFeedback selectRdNoticeFeedbackById(Long id);

    /**
     * 查询工作通知反馈列表
     *
     * @param rdNoticeFeedback 工作通知反馈
     * @return 工作通知反馈集合
     */
    public List<RdNoticeFeedback> selectRdNoticeFeedbackList(RdNoticeFeedback rdNoticeFeedback);

    /**
     * 新增工作通知反馈
     *
     * @param rdNoticeFeedback 工作通知反馈
     * @return 结果
     */
    public int insertRdNoticeFeedback(RdNoticeFeedback rdNoticeFeedback);

    /**
     * 修改工作通知反馈
     *
     * @param rdNoticeFeedback 工作通知反馈
     * @return 结果
     */
    public int updateRdNoticeFeedback(RdNoticeFeedback rdNoticeFeedback);

    /**
     * 批量删除工作通知反馈
     *
     * @param ids 需要删除的工作通知反馈主键集合
     * @return 结果
     */
    public int deleteRdNoticeFeedbackByIds(Long[] ids);

    /**
     * 删除工作通知反馈信息
     *
     * @param id 工作通知反馈主键
     * @return 结果
     */
    public int deleteRdNoticeFeedbackById(Long id);

    /***
     * 提交工作通知反馈接口
     * @param feedbackInfo2VO 反馈信息
     * @return 提交结果
     */
    void saveNoticeFeedback(FeedbackInfo2VO feedbackInfo2VO);

    /***
     * 获取工作通知反馈接口
     * @param notice 工作通知信息
     * @return 反馈信息
     */
    List<RdNoticeFeedback> getNoticeFeedback(SysNotice notice);
}
