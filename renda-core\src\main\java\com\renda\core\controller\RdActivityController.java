package com.renda.core.controller;

import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.renda.core.domain.RdActivityRegistration;
import com.renda.core.service.IRdActivityRegistrationService;
import com.renda.framework.web.service.SysLoginService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.renda.common.annotation.Log;
import com.renda.common.core.controller.BaseController;
import com.renda.common.core.domain.AjaxResult;
import com.renda.common.enums.BusinessType;
import com.renda.core.domain.RdActivity;
import com.renda.core.service.IRdActivityService;
import com.renda.common.utils.poi.ExcelUtil;
import com.renda.common.core.page.TableDataInfo;

/**
 * 活动Controller
 *
 * <AUTHOR>
 * @date 2023-11-13
 */
@RestController
@RequestMapping("/renda/activity")
public class RdActivityController extends BaseController
{
    @Autowired
    private IRdActivityService rdActivityService;

    @Autowired
    private IRdActivityRegistrationService rdActivityRegistrationService;

    /**
     * 查询活动列表
     */
    @PreAuthorize("@ss.hasPermi('renda:activity:list')")
    @GetMapping("/list")
    public TableDataInfo list(RdActivity rdActivity)
    {
        startPage();
        List<RdActivity> list = rdActivityService.selectRdActivityList(rdActivity);
        return getDataTable(list);
    }

    /**
     * 导出活动列表
     */
    @PreAuthorize("@ss.hasPermi('renda:activity:export')")
    @Log(title = "活动", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RdActivity rdActivity)
    {
        List<RdActivity> list = rdActivityService.selectRdActivityList(rdActivity);
        ExcelUtil<RdActivity> util = new ExcelUtil<RdActivity>(RdActivity.class);
        util.exportExcel(response, list, "活动数据");
    }

    /**
     * 获取活动详细信息
     */
    @PreAuthorize("@ss.hasPermi('renda:activity:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rdActivityService.selectRdActivityById(id));
    }

    /**
     * 新增活动
     */
    @PreAuthorize("@ss.hasPermi('renda:activity:add')")
    @Log(title = "活动", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RdActivity rdActivity)
    {
        return toAjax(rdActivityService.insertRdActivity(rdActivity));
    }

    /**
     * 修改活动
     */
    @PreAuthorize("@ss.hasPermi('renda:activity:edit')")
    @Log(title = "活动", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RdActivity rdActivity)
    {
        return toAjax(rdActivityService.updateRdActivity(rdActivity));
    }

    /**
     * 删除活动
     */
    @PreAuthorize("@ss.hasPermi('renda:activity:remove')")
    @Log(title = "活动", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rdActivityService.deleteRdActivityByIds(ids));
    }

    /***
     * 能否报名状态修改
     */
    @PreAuthorize("@ss.hasPermi('renda:activity:edit')")
    @Log(title = "活动", businessType = BusinessType.UPDATE)
    @PutMapping("/changeEnabled")
    public AjaxResult changeEnabled(@RequestBody RdActivity rdActivity)
    {
        rdActivity.setUpdateBy(getUsername());
        rdActivity.setUpdateTime(new Date());
        return toAjax(rdActivityService.updateRdActivity(rdActivity));
    }

    /***
     * 活动状态修改
     */
    @PreAuthorize("@ss.hasPermi('renda:activity:edit')")
    @Log(title = "活动", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RdActivity rdActivity)
    {
        rdActivity.setUpdateBy(getUsername());
        rdActivity.setUpdateTime(new Date());
        return toAjax(rdActivityService.updateRdActivity(rdActivity));
    }

    /***
     * 活动报名列表
     */
    @PreAuthorize("@ss.hasPermi('renda:activity:list')")
    @GetMapping("/listActivityRegistration")
    public TableDataInfo listActivityRegistration(RdActivityRegistration rdActivityRegistration)
    {
        startPage();
        List<RdActivityRegistration> list = rdActivityRegistrationService.selectRdActivityRegistrationList(rdActivityRegistration);
        return getDataTable(list);
    }

    /**
     * 删除报名代表
     */
    @PreAuthorize("@ss.hasPermi('renda:activity:edit')")
    @Log(title = "删除报名代表", businessType = BusinessType.DELETE)
    @DeleteMapping("/delActivityRegistration/{ids}")
    public AjaxResult delActivityRegistration(@PathVariable Long[] ids)
    {
        return toAjax(rdActivityRegistrationService.deleteRdActivityRegistrationByIds(ids));
    }

    /**
     * 单发短信
     */
    @PreAuthorize("@ss.hasPermi('renda:activity:add')")
    @Log(title = "单发短信", businessType = BusinessType.INSERT)
    @PostMapping("/sendSms")
    public AjaxResult sendSms(@RequestBody RdActivityRegistration rdActivityRegistration)
    {
        return toAjax(rdActivityService.sendSms(rdActivityRegistration));
    }

    /**
     * 群发短信
     */
    @PreAuthorize("@ss.hasPermi('renda:activity:add')")
    @Log(title = "群发短信", businessType = BusinessType.INSERT)
    @PostMapping("/batchSendSms")
    public AjaxResult batchSendSms(@RequestBody RdActivity rdActivity)
    {
        return toAjax(rdActivityService.batchSendSms(rdActivity));
    }

    /**
     * 导出活动报名列表
     */
    @PreAuthorize("@ss.hasPermi('renda:activity:export')")
    @Log(title = "导出活动报名列表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportRegistration")
    public void exportRegistration(HttpServletResponse response, RdActivityRegistration rdActivityRegistration)
    {
        List<RdActivityRegistration> list = rdActivityRegistrationService.selectRdActivityRegistrationList(rdActivityRegistration);
        ExcelUtil<RdActivityRegistration> util = new ExcelUtil<RdActivityRegistration>(RdActivityRegistration.class);
        util.exportExcel(response, list, "活动报名数据");
    }

}
