package com.renda.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.renda.common.annotation.Log;
import com.renda.common.core.controller.BaseController;
import com.renda.common.core.domain.AjaxResult;
import com.renda.common.enums.BusinessType;
import com.renda.core.domain.RdJob;
import com.renda.core.service.IRdJobService;
import com.renda.common.utils.poi.ExcelUtil;
import com.renda.common.core.page.TableDataInfo;

/**
 * 履职工作Controller
 *
 * <AUTHOR>
 * @date 2023-11-20
 */
@RestController
@RequestMapping("/renda/Job")
public class RdJobController extends BaseController
{
    @Autowired
    private IRdJobService rdJobService;

    /**
     * 查询履职工作列表
     */
    @PreAuthorize("@ss.hasPermi('renda:Job:list')")
    @GetMapping("/list")
    public TableDataInfo list(RdJob rdJob)
    {
        startPage();
        List<RdJob> list = rdJobService.selectRdJobList(rdJob);
        return getDataTable(list);
    }

    /**
     * 导出履职工作列表
     */
    @PreAuthorize("@ss.hasPermi('renda:Job:export')")
    @Log(title = "履职工作", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RdJob rdJob)
    {
        List<RdJob> list = rdJobService.selectRdJobList(rdJob);
        ExcelUtil<RdJob> util = new ExcelUtil<RdJob>(RdJob.class);
        util.exportExcel(response, list, "履职工作数据");
    }

    /**
     * 获取履职工作详细信息
     */
    @PreAuthorize("@ss.hasPermi('renda:Job:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rdJobService.selectRdJobById(id));
    }

    /**
     * 获取履职工作详细信息
     */
    @PreAuthorize("@ss.hasPermi('renda:Job:query')")
    @GetMapping(value = "/Ext/{id}")
    public AjaxResult getExtInfo(@PathVariable("id") Long id)
    {
        return success(rdJobService.selectJobExtById(id));
    }

    /**
     * 新增履职工作
     */
    @PreAuthorize("@ss.hasPermi('renda:Job:add')")
    @Log(title = "履职工作", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RdJob rdJob)
    {
        return toAjax(rdJobService.insertRdJob(rdJob));
    }

    /**
     * 修改履职工作
     */
    @PreAuthorize("@ss.hasPermi('renda:Job:edit')")
    @Log(title = "履职工作", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RdJob rdJob)
    {
        return toAjax(rdJobService.updateRdJob(rdJob));
    }

    /**
     * 删除履职工作
     */
    @PreAuthorize("@ss.hasPermi('renda:Job:remove')")
    @Log(title = "履职工作", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rdJobService.deleteRdJobByIds(ids));
    }

    /**
     * 获取代表履职统计信息
     */
    @PreAuthorize("@ss.hasPermi('renda:deputy:list')")
    @GetMapping(value = "/stat/")
    public AjaxResult getJobStat()
    {
        return success(rdJobService.getJobStat());
    }

}
