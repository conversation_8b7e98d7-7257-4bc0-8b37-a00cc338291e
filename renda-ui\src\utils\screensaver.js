/**
 * 屏保检测器
 *
 * 功能：
 * - 在指定路径（/stationscreen/*）下监听用户活动
 * - 指定时间无活动后自动进入屏保页面
 * - 点击屏保页面可返回之前的页面
 *
 * 配置说明：
 * 1. 在系统管理 -> 参数设置中添加以下配置：
 *    - 参数名称：屏保延迟时间
 *    - 参数键名：rd.screensaver.delay
 *    - 参数键值：300（单位：秒，如 300 表示 5分钟）
 *    - 系统内置：否
 *    - 备注：联络站大屏屏保延迟时间，单位秒
 *
 * 2. 如果未配置该参数，将使用默认值：300秒（5分钟）
 *
 * 3. 运行时动态更新配置：
 *    - 可调用 reloadScreensaverConfig() 方法重新加载配置
 *    - 可调用 updateScreensaverDelay(delay) 方法直接设置延迟时间（毫秒）
 *    - 可调用 getScreensaverDelay() 方法获取当前延迟时间（毫秒）
 *
 * 使用示例：
 * ```javascript
 * import { getScreensaverDelay, updateScreensaverDelay, reloadScreensaverConfig } from '@/utils/screensaver'
 *
 * // 获取当前延迟时间
 * const delay = getScreensaverDelay()
 *
 * // 设置延迟时间为10分钟
 * updateScreensaverDelay(10 * 60 * 1000)
 *
 * // 重新从后台加载配置
 * await reloadScreensaverConfig()
 * ```
 */

import { getConfigKey } from "@/api/system/config";

let screensaverTimer = null
let isScreensaverActive = false
let lastActiveTime = Date.now()
let currentRouter = null
let beforeScreensaverRoute = null

// 屏保配置
let SCREENSAVER_DELAY = 300000 // 默认5分钟无活动进入屏保，可通过后台配置动态更新
const SCREENSAVER_ROUTE = '/stationscreen/DataDashboard' // 屏保页面路由
const CONFIG_KEY_SCREENSAVER_DELAY = 'rd.screensaver.delay' // 后台配置键名

// 允许启用屏保的路径前缀
const ALLOWED_PATH_PREFIX = '/stationscreen/'

// 需要排除的具体页面（即使在允许的路径下也不启用屏保）
const EXCLUDED_PATHS = [
  '/stationscreen/DataDashboard' // 屏保页面本身
]

/**
 * 从后台获取屏保延迟配置
 */
async function loadScreensaverConfig() {
  try {
    const response = await getConfigKey(CONFIG_KEY_SCREENSAVER_DELAY)

    if (response && response.code === 200 && response.msg) {
      const configValue = parseInt(response.msg)

      if (!isNaN(configValue) && configValue > 0) {
        const newDelay = configValue * 1000 // 配置值单位为秒，转换为毫秒
        SCREENSAVER_DELAY = newDelay
        return true
      }
    }
  } catch (error) {
    console.log('🔧 配置获取失败:', error)
  }
  return false
}

/**
 * 获取当前屏保延迟时间（毫秒）
 */
export function getScreensaverDelay() {
  return SCREENSAVER_DELAY
}

/**
 * 更新屏保延迟时间（毫秒）
 */
export function updateScreensaverDelay(delay) {
  if (typeof delay === 'number' && delay > 0) {
    SCREENSAVER_DELAY = delay
    // 如果当前有活动计时器，重置它
    if (screensaverTimer && !isScreensaverActive) {
      resetScreensaverTimer()
    }
    return true
  }
  return false
}

/**
 * 初始化屏保检测器
 * @param {Object} router Vue Router 实例
 */
export async function initScreensaver(router) {
  currentRouter = router

  // 加载屏保配置
  await loadScreensaverConfig()

  // 监听路由变化
  router.beforeEach((to, from, next) => {
    // 如果是从屏保页面离开，清除屏保状态
    if (from.path === SCREENSAVER_ROUTE) {
      isScreensaverActive = false
    }

    // 如果要进入屏保页面，记录之前的路由
    if (to.path === SCREENSAVER_ROUTE && from.path !== SCREENSAVER_ROUTE) {
      beforeScreensaverRoute = from.fullPath
    }

    next()
  })

  // 开始监听用户活动
  startActivityListening()
}

/**
 * 重新加载屏保配置
 */
export async function reloadScreensaverConfig() {
  const loaded = await loadScreensaverConfig()
  if (loaded && screensaverTimer && !isScreensaverActive) {
    // 配置更新后重置计时器
    resetScreensaverTimer()
  }
  return loaded
}

/**
 * 开始监听用户活动
 */
function startActivityListening() {
  // 监听的事件类型
  const events = [
    'mousedown',
    'mousemove',
    'keypress',
    'scroll',
    'touchstart',
    'click'
  ]

  // 添加事件监听器
  events.forEach(event => {
    document.addEventListener(event, handleUserActivity, true)
  })

  // 开始计时
  resetScreensaverTimer()
}

/**
 * 处理用户活动
 */
function handleUserActivity() {
  lastActiveTime = Date.now()

  // 如果当前在屏保页面，不重置计时器
  if (isScreensaverActive || currentRouter?.currentRoute?.path === SCREENSAVER_ROUTE) {
    return
  }

  // 重置屏保计时器
  resetScreensaverTimer()
}

/**
 * 重置屏保计时器
 */
function resetScreensaverTimer() {
  // 清除之前的计时器
  if (screensaverTimer) {
    clearTimeout(screensaverTimer)
  }

  // 检查是否应该启用屏保
  if (!shouldEnableScreensaver()) {
    return
  }

  // 设置新的计时器
  screensaverTimer = setTimeout(() => {
    activateScreensaver()
  }, SCREENSAVER_DELAY)
}

/**
 * 检查是否应该启用屏保
 */
function shouldEnableScreensaver() {
  if (!currentRouter) return false

  const currentPath = currentRouter.currentRoute?.path
  if (!currentPath) return false

  // 只有在 /stationscreen/ 路径下才启用屏保
  if (!currentPath.startsWith(ALLOWED_PATH_PREFIX)) {
    return false
  }

  // 检查是否在排除列表中
  return !EXCLUDED_PATHS.some(path => currentPath.startsWith(path))
}

/**
 * 激活屏保
 */
function activateScreensaver() {
  if (!currentRouter || isScreensaverActive) {
    return
  }

  const currentRoute = currentRouter.currentRoute
  if (!currentRoute || currentRoute.path === SCREENSAVER_ROUTE) {
    return
  }

  // 记录进入屏保前的路由
  beforeScreensaverRoute = currentRoute.fullPath
  isScreensaverActive = true

  // 跳转到屏保页面（使用 replace 替换当前页面，保持路由栈干净）
  currentRouter.replace({
    path: SCREENSAVER_ROUTE,
    query: { screensaver: 'true' }
  }).catch(err => {
    isScreensaverActive = false
  })
}

/**
 * 退出屏保（返回之前的页面或默认页面）
 */
export function exitScreensaver() {
  if (!currentRouter) {
    return
  }

  isScreensaverActive = false

  // 清除计时器
  if (screensaverTimer) {
    clearTimeout(screensaverTimer)
    screensaverTimer = null
  }

  // 决定跳转目标
  let targetRoute = null

  // 如果有之前的路由且不是屏保页面本身，则返回之前的页面
  if (beforeScreensaverRoute && beforeScreensaverRoute !== SCREENSAVER_ROUTE) {
    targetRoute = beforeScreensaverRoute
  }

  // 清除记录的路由
  beforeScreensaverRoute = null

  // 跳转页面
  if (targetRoute) {
    // 使用 replace 替换当前屏保页面，从路由栈中移除屏保页
    currentRouter.replace(targetRoute).catch(err => {
      // 如果跳转失败，尝试跳转到默认首页
      goToDefaultHome()
    })
  } else {
    // 没有之前的页面，跳转到默认首页
    goToDefaultHome()
  }
}

/**
 * 跳转到默认首页
 */
function goToDefaultHome() {
  if (!currentRouter) return

  // 获取当前路由的查询参数，特别是 stationId
  const currentQuery = currentRouter.currentRoute.query || {}

  // 使用 replace 替换当前屏保页面，保持路由栈干净
  currentRouter.replace({
    name: 'StationScreenHome',
    query: currentQuery
  }).catch(err => {
    // 如果 StationScreenHome 不存在，尝试直接使用路径
    currentRouter.replace({
      path: '/renda/stationscreen/Home',
      query: currentQuery
    }).catch(err2 => {
      // 静默处理失败
    })
  })
}

/**
 * 销毁屏保检测器
 */
export function destroyScreensaver() {
  // 清除计时器
  if (screensaverTimer) {
    clearTimeout(screensaverTimer)
    screensaverTimer = null
  }

  // 移除事件监听器
  const events = [
    'mousedown',
    'mousemove',
    'keypress',
    'scroll',
    'touchstart',
    'click'
  ]

  events.forEach(event => {
    document.removeEventListener(event, handleUserActivity, true)
  })

  // 重置状态
  isScreensaverActive = false
  currentRouter = null
  beforeScreensaverRoute = null
}
