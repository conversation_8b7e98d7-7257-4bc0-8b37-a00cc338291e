package com.renda.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.renda.core.mapper.RdActivityRegistrationMapper;
import com.renda.core.domain.RdActivityRegistration;
import com.renda.core.service.IRdActivityRegistrationService;

/**
 * 活动报名Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-14
 */
@Service
public class RdActivityRegistrationServiceImpl implements IRdActivityRegistrationService
{
    @Autowired
    private RdActivityRegistrationMapper rdActivityRegistrationMapper;

    /**
     * 查询活动报名
     *
     * @param id 活动报名主键
     * @return 活动报名
     */
    @Override
    public RdActivityRegistration selectRdActivityRegistrationById(Long id)
    {
        return rdActivityRegistrationMapper.selectRdActivityRegistrationById(id);
    }

    /**
     * 查询活动报名列表
     *
     * @param rdActivityRegistration 活动报名
     * @return 活动报名
     */
    @Override
    public List<RdActivityRegistration> selectRdActivityRegistrationList(RdActivityRegistration rdActivityRegistration)
    {
        return rdActivityRegistrationMapper.selectRdActivityRegistrationList(rdActivityRegistration);
    }

    /**
     * 新增活动报名
     *
     * @param rdActivityRegistration 活动报名
     * @return 结果
     */
    @Override
    public int insertRdActivityRegistration(RdActivityRegistration rdActivityRegistration)
    {
        return rdActivityRegistrationMapper.insertRdActivityRegistration(rdActivityRegistration);
    }

    /**
     * 修改活动报名
     *
     * @param rdActivityRegistration 活动报名
     * @return 结果
     */
    @Override
    public int updateRdActivityRegistration(RdActivityRegistration rdActivityRegistration)
    {
        return rdActivityRegistrationMapper.updateRdActivityRegistration(rdActivityRegistration);
    }

    /**
     * 批量删除活动报名
     *
     * @param ids 需要删除的活动报名主键
     * @return 结果
     */
    @Override
    public int deleteRdActivityRegistrationByIds(Long[] ids)
    {
        return rdActivityRegistrationMapper.deleteRdActivityRegistrationByIds(ids);
    }

    /**
     * 删除活动报名信息
     *
     * @param id 活动报名主键
     * @return 结果
     */
    @Override
    public int deleteRdActivityRegistrationById(Long id)
    {
        return rdActivityRegistrationMapper.deleteRdActivityRegistrationById(id);
    }

    /***
     * 根据活动id和用户id查询报名信息
     * @param activityRegistration 活动信息
     * @return
     */
    @Override
    public RdActivityRegistration selectRdActivityRegistrationByDeputyId(RdActivityRegistration activityRegistration) {
        return rdActivityRegistrationMapper.selectRdActivityRegistrationByDeputyId(activityRegistration);
    }

    /***
     * 删除活动报名信息（已报名或已请假的报名信息不删除）
     * @param activityId
     */
    @Override
    public void deleteRdActivityRegistrationByActivityId(Long activityId, String registrationType) {
        rdActivityRegistrationMapper.deleteRdActivityRegistrationByActivityId(activityId, registrationType);
    }

    /***
     * 根据活动id和用户id查询报名信息
     * @param activityId 活动id
     * @param deputyId 用户id
     * @return 活动报名信息
     */
    @Override
    public RdActivityRegistration selectRdActivityRegistrationByActivityIdAndDeputyId(Long activityId, Long deputyId) {
        return rdActivityRegistrationMapper.selectRdActivityRegistrationByActivityIdAndDeputyId(activityId, deputyId);
    }


}
