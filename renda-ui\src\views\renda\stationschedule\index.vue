<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="联络站" prop="stationId">
        <treeselect
          v-model="queryParams.stationId"
          :options="stationOptions"
          :show-count="true"
          placeholder="请选择联络站"
          :searchable="true"
          :clearable="true"
          style="width: 200px;"
        />
      </el-form-item>
      <el-form-item label="排班日期" prop="scheduleDate">
        <el-date-picker clearable
          v-model="queryParams.scheduleDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择排班日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="值班代表" prop="deputyName">
        <el-input
          v-model="queryParams.deputyName"
          placeholder="请输入值班代表姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工作人员" prop="staffName">
        <el-input
          v-model="queryParams.staffName"
          placeholder="请输入值班工作人员姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['renda:stationschedule:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['renda:stationschedule:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['renda:stationschedule:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['renda:stationschedule:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="stationscheduleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键ID" align="center" prop="id" />
      <el-table-column label="联络站" align="center" prop="stationName" />
      <el-table-column label="排班日期" align="center" prop="scheduleDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.scheduleDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="值班代表" align="center" prop="deputyName" />
      <el-table-column label="值班工作人员" align="center" prop="staffName" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['renda:stationschedule:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['renda:stationschedule:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改排班表管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="联络站" prop="stationId">
          <treeselect
            v-model="form.stationId"
            :options="stationOptions"
            :show-count="true"
            placeholder="请选择联络站"
            :searchable="true"
            :clearable="true"
            @input="handleStationChange"
          />
        </el-form-item>
        <el-form-item label="排班日期" prop="scheduleDate">
          <el-date-picker clearable
            v-model="form.scheduleDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择排班日期"
            @change="handleScheduleDateChange">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="值班代表" prop="deputyId">
          <el-select
            v-model="form.deputyId"
            placeholder="请选择值班代表"
            clearable
            filterable
            :loading="deputyLoading"
          >
            <el-option
              v-for="deputy in deputyOptions"
              :key="deputy.id"
              :label="deputy.name"
              :value="deputy.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工作人员" prop="staffId">
          <el-select
            v-model="form.staffId"
            placeholder="请选择值班工作人员"
            clearable
            filterable
            :loading="staffLoading"
          >
            <el-option
              v-for="staff in staffOptions"
              :key="staff.id"
              :label="staff.name"
              :value="staff.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listStationschedule, getStationschedule, delStationschedule, addStationschedule, updateStationschedule, checkStationScheduleExists } from "@/api/renda/stationschedule";
import { listDeputy } from "@/api/renda/deputy";
import { listStationstaff } from "@/api/renda/stationstaff";
import { stationTreeSelect } from "@/api/renda/station";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Stationschedule",
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 排班表管理表格数据
      stationscheduleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 站点树选项
      stationOptions: [],
      // 值班代表选项
      deputyOptions: [],
      // 值班工作人员选项
      staffOptions: [],
      // 代表加载状态
      deputyLoading: false,
      // 工作人员加载状态
      staffLoading: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        stationId: null,
        scheduleDate: null,
        deputyName: null,
        staffName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        stationId: [
          { required: true, message: "联络站不能为空", trigger: "change" }
        ],
        scheduleDate: [
          { required: true, message: "排班日期不能为空", trigger: "blur" },
          { validator: this.validateScheduleDate, trigger: "blur" }
        ],
        deputyId: [
          { required: true, message: "值班代表不能为空", trigger: "change" }
        ],
        staffId: [
          { required: true, message: "值班工作人员不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
    this.getStationTree();
  },
  methods: {
    /** 查询排班表管理列表 */
    getList() {
      this.loading = true;
      listStationschedule(this.queryParams).then(response => {
        this.stationscheduleList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询站点树数据 */
    getStationTree() {
      stationTreeSelect().then(response => {
        this.stationOptions = response.data;
      });
    },
    /** 根据站点ID获取代表列表 */
    getDeputyByStation(stationId) {
      this.deputyLoading = true;
      listDeputy({ isStationDeputy: 1, pageSize: 1000 }).then(response => {
        this.deputyOptions = response.rows;
        this.deputyLoading = false;
      }).catch(() => {
        this.deputyLoading = false;
      });
    },
    /** 根据站点ID获取工作人员列表 */
    getStaffByStation(stationId) {
      if (!stationId) {
        this.staffOptions = [];
        return;
      }
      this.staffLoading = true;
      listStationstaff({ stationId: stationId, pageSize: 1000, status: '0' }).then(response => {
        this.staffOptions = response.rows;
        this.staffLoading = false;
      }).catch(() => {
        this.staffLoading = false;
      });
    },
    /** 加载代表选项列表（返回Promise） */
    loadDeputyOptions(stationId) {
      if (!stationId) {
        this.deputyOptions = [];
        return Promise.resolve();
      }
      this.deputyLoading = true;
      return listDeputy({ isStationDeputy: 1, pageSize: 1000 }).then(response => {
        this.deputyOptions = response.rows;
        this.deputyLoading = false;
        return response;
      }).catch(error => {
        this.deputyLoading = false;
        return Promise.reject(error);
      });
    },
    /** 加载工作人员选项列表（返回Promise） */
    loadStaffOptions(stationId) {
      if (!stationId) {
        this.staffOptions = [];
        return Promise.resolve();
      }
      this.staffLoading = true;
      return listStationstaff({ stationId: stationId, pageSize: 1000, status: '0' }).then(response => {
        this.staffOptions = response.rows;
        this.staffLoading = false;
        return response;
      }).catch(error => {
        this.staffLoading = false;
        return Promise.reject(error);
      });
    },
    /** 站点变化处理 */
    handleStationChange(stationId) {
      this.form.deputyId = null;
      this.form.staffId = null;
      this.getDeputyByStation(stationId);
      this.getStaffByStation(stationId);

      // 站点变化后重新验证排班日期
      if (this.form.scheduleDate) {
        this.$nextTick(() => {
          this.$refs.form.validateField('scheduleDate');
        });
      }
    },
    /** 自定义验证排班日期 */
    validateScheduleDate(rule, value, callback) {
      if (!value || !this.form.stationId) {
        callback();
        return;
      }

      // 检查是否已存在相同联络站相同日期的排班记录
      const checkParams = {
        stationId: this.form.stationId,
        scheduleDate: value,
        id: this.form.id || null
      };

      checkStationScheduleExists(checkParams).then(response => {
        if (response.data) {
          callback(new Error('该联络站在指定日期已存在排班记录，每个联络站每天只能有一条排班信息'));
        } else {
          callback();
        }
      }).catch(() => {
        callback();
      });
    },
    /** 排班日期变化处理 */
    handleScheduleDateChange(value) {
      if (value && this.form.stationId) {
        this.$nextTick(() => {
          this.$refs.form.validateField('scheduleDate');
        });
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        stationId: null,
        scheduleDate: null,
        deputyId: null,
        staffId: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.deputyOptions = [];
      this.staffOptions = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加排班表管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getStationschedule(id).then(response => {
        this.form = response.data;
        // 如果有站点ID，先加载对应的代表和工作人员，然后再设置选中值
        if (this.form.stationId) {
          const deputyId = this.form.deputyId;
          const staffId = this.form.staffId;

          // 并行加载代表和工作人员列表
          Promise.all([
            this.loadDeputyOptions(this.form.stationId),
            this.loadStaffOptions(this.form.stationId)
          ]).then(() => {
            // 选项加载完成后，重新设置选中值
            this.form.deputyId = deputyId;
            this.form.staffId = staffId;
          });
        }
        this.open = true;
        this.title = "修改排班表管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateStationschedule(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addStationschedule(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除排班表管理编号为"' + ids + '"的数据项？').then(function() {
        return delStationschedule(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('renda/stationschedule/export', {
        ...this.queryParams
      }, `stationschedule_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
