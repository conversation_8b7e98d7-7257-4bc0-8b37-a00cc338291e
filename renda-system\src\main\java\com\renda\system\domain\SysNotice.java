package com.renda.system.domain;

import lombok.Data;
import com.renda.common.core.domain.BaseEntity;

/**
 * 通知公告表 sys_notice
 *
 * <AUTHOR>
 */
@Data
public class SysNotice extends BaseEntity
{

    /** 动态ID */
    private Long noticeId;

    /** 动态标题 */
    private String noticeTitle;

    /** 发布人 */
    private String noticePublisher;

    /** 动态类型（1轮播动态 2分类动态 3卡片动态 4一般动态） */
    private String noticeType;

    /** 动态类别（1代表动态 2政策宣传） */
    private String noticeCategory;

    /** 动态内容 */
    private String noticeContent;

    /** 封面图片 */
    private String coverUrl;

    /** 置顶状态（1置顶 0非置顶） */
    private String isTop;

    /** 动态状态（0正常 1关闭） */
    private String status;

    /** 查看次数 */
    private Long viewCount;

    /** 查看次数 */
    private Long allowFeedback;

    /** 反馈条数 */
    private Long feedbackCount;

}
