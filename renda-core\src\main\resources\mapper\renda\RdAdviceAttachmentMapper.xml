<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renda.core.mapper.RdAdviceAttachmentMapper">

    <resultMap type="RdAdviceAttachment" id="RdAdviceAttachmentResult">
        <result property="id"    column="id"    />
        <result property="adviceId"    column="advice_id"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileUrl"    column="file_url"    />
    </resultMap>

    <sql id="selectRdAdviceAttachmentVo">
        select id, advice_id, file_type, file_name, file_url from rd_advice_attachment
    </sql>

    <select id="selectRdAdviceAttachmentList" parameterType="RdAdviceAttachment" resultMap="RdAdviceAttachmentResult">
        <include refid="selectRdAdviceAttachmentVo"/>
        <where>
            <if test="adviceId != null "> and advice_id = #{adviceId}</if>
            <if test="fileType != null "> and file_type = #{fileType}</if>
            <if test="fileName != null  and fileName != ''"> and file_name = #{fileName}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
        </where>
    </select>

    <select id="selectRdAdviceAttachmentById" parameterType="Long" resultMap="RdAdviceAttachmentResult">
        <include refid="selectRdAdviceAttachmentVo"/>
        where id = #{id}
    </select>

    <insert id="insertRdAdviceAttachment" parameterType="RdAdviceAttachment" useGeneratedKeys="true" keyProperty="id">
        insert into rd_advice_attachment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="adviceId != null">advice_id,</if>
            <if test="fileType != null">file_type,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="adviceId != null">#{adviceId},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
         </trim>
    </insert>

    <update id="updateRdAdviceAttachment" parameterType="RdAdviceAttachment">
        update rd_advice_attachment
        <trim prefix="SET" suffixOverrides=",">
            <if test="adviceId != null">advice_id = #{adviceId},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRdAdviceAttachmentById" parameterType="Long">
        delete from rd_advice_attachment where id = #{id}
    </delete>

    <delete id="deleteRdAdviceAttachmentByIds" parameterType="String">
        delete from rd_advice_attachment where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
