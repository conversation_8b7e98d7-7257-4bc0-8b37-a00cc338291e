<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renda.core.mapper.RdMassMapper">

    <resultMap type="RdMass" id="RdMassResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="phone"    column="phone"    />
        <result property="avatar"    column="avatar"    />
        <result property="openid"    column="openid"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectRdMassVo">
        select id, name, phone, avatar, openid, create_time, update_time from rd_mass
    </sql>

    <select id="selectRdMassList" parameterType="RdMass" resultMap="RdMassResult">
        <include refid="selectRdMassVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
        </where>
    </select>

    <select id="selectRdMassById" parameterType="Long" resultMap="RdMassResult">
        <include refid="selectRdMassVo"/>
        where id = #{id}
    </select>

    <select id="selectRdMassByOpenid" resultType="com.renda.core.domain.RdMass">
        <include refid="selectRdMassVo"/>
        where openid = #{openid}
    </select>

    <select id="getMassStatistics" resultType="com.renda.core.domain.vo.MassStatisticsInfoVO">
        select (select count(DISTINCT deputy_id) from rd_advice where mass_id = #{id}) as follow,
               (select count(1) from rd_advice where mass_id = #{id}) as adviceCount,
               (select count(DISTINCT advice_id) from rd_feedback where advice_id in (select id from rd_advice where mass_id = #{id})) as feedbackCount
    </select>

    <select id="getMassAdviceList" resultType="com.renda.core.domain.vo.MassAdviceInfoVO">
        select id, deputy_id as deputyId,
               (select avatar from rd_deputy where id = a.deputy_id) as avatar,
               title, content, create_time as createTime,
               (select count(*) from rd_feedback where advice_id = a.id) as feedbackCount
        from rd_advice a
        where mass_id = #{id}
        order by create_time desc
    </select>

    <insert id="insertRdMass" parameterType="RdMass" useGeneratedKeys="true" keyProperty="id">
        insert into rd_mass
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="avatar != null">avatar,</if>
            <if test="openid != null">openid,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="openid != null">#{openid},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRdMass" parameterType="RdMass">
        update rd_mass
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="openid != null">openid = #{openid},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRdMassById" parameterType="Long">
        delete from rd_mass where id = #{id}
    </delete>

    <delete id="deleteRdMassByIds" parameterType="String">
        delete from rd_mass where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
