import request from '@/utils/request'

// 查询排班表管理列表
export function listStationschedule(query) {
  return request({
    url: '/renda/stationschedule/list',
    method: 'get',
    params: query
  })
}

// 查询排班表管理详细
export function getStationschedule(id) {
  return request({
    url: '/renda/stationschedule/' + id,
    method: 'get'
  })
}

// 新增排班表管理
export function addStationschedule(data) {
  return request({
    url: '/renda/stationschedule',
    method: 'post',
    data: data
  })
}

// 修改排班表管理
export function updateStationschedule(data) {
  return request({
    url: '/renda/stationschedule',
    method: 'put',
    data: data
  })
}

// 删除排班表管理
export function delStationschedule(id) {
  return request({
    url: '/renda/stationschedule/' + id,
    method: 'delete'
  })
}

// 检查指定联络站和日期是否已存在排班记录
export function checkStationScheduleExists(query) {
  return request({
    url: '/renda/stationschedule/checkExists',
    method: 'get',
    params: query
  })
}
