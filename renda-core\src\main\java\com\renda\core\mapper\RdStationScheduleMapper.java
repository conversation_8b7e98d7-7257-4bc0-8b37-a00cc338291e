package com.renda.core.mapper;

import java.util.List;
import java.util.Date;
import com.renda.core.domain.RdStationSchedule;
import org.apache.ibatis.annotations.Param;

/**
 * 排班表管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface RdStationScheduleMapper 
{
    /**
     * 查询排班表管理
     * 
     * @param id 排班表管理主键
     * @return 排班表管理
     */
    public RdStationSchedule selectRdStationScheduleById(Long id);

    /**
     * 查询排班表管理列表
     * 
     * @param rdStationSchedule 排班表管理
     * @return 排班表管理集合
     */
    public List<RdStationSchedule> selectRdStationScheduleList(RdStationSchedule rdStationSchedule);

    /**
     * 检查指定联络站和日期是否已存在排班记录
     * 
     * @param stationId 联络站ID
     * @param scheduleDate 排班日期
     * @param excludeId 排除的记录ID（修改时使用）
     * @return 记录数量
     */
    public int checkStationScheduleExists(@Param("stationId") Long stationId, 
                                         @Param("scheduleDate") Date scheduleDate, 
                                         @Param("excludeId") Long excludeId);

    /**
     * 新增排班表管理
     * 
     * @param rdStationSchedule 排班表管理
     * @return 结果
     */
    public int insertRdStationSchedule(RdStationSchedule rdStationSchedule);

    /**
     * 修改排班表管理
     * 
     * @param rdStationSchedule 排班表管理
     * @return 结果
     */
    public int updateRdStationSchedule(RdStationSchedule rdStationSchedule);

    /**
     * 删除排班表管理
     * 
     * @param id 排班表管理主键
     * @return 结果
     */
    public int deleteRdStationScheduleById(Long id);

    /**
     * 批量删除排班表管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRdStationScheduleByIds(Long[] ids);
}
