package com.renda.core.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.renda.common.annotation.Excel;
import com.renda.common.core.domain.BaseEntity;

/**
 * 联络站动态对象 rd_station_news
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
public class RdStationNews extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 联络站ID */
    @Excel(name = "联络站ID")
    private Long stationId;

    /** 联络站名称 */
    @Excel(name = "联络站名称")
    private String stationName;

    /** 动态标题 */
    @Excel(name = "动态标题")
    private String title;

    /** 动态摘要 */
    @Excel(name = "动态摘要")
    private String summary;

    /** 动态内容 */
    @Excel(name = "动态内容")
    private String content;

    /** 动态类型 */
    @Excel(name = "动态类型")
    private Integer type;

    /** 封面图片 */
    @Excel(name = "封面图片")
    private String coverUrl;

    /** 发布人 */
    @Excel(name = "发布人")
    private String publisher;

    /** 发布时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发布时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date publishTime;

    /** 是否置顶（0-不置顶；1-置顶） */
    @Excel(name = "是否置顶", readConverterExp = "0=-不置顶；1-置顶")
    private String isTop;

    /** 状态（0-下线；1-上线） */
    @Excel(name = "状态", readConverterExp = "0=-下线；1-上线")
    private Integer status;

    /** 浏览次数 */
    @Excel(name = "浏览次数")
    private Integer viewCount;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setStationId(Long stationId)
    {
        this.stationId = stationId;
    }

    public Long getStationId()
    {
        return stationId;
    }

    public void setStationName(String stationName)
    {
        this.stationName = stationName;
    }

    public String getStationName()
    {
        return stationName;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getTitle()
    {
        return title;
    }
    public void setSummary(String summary)
    {
        this.summary = summary;
    }

    public String getSummary()
    {
        return summary;
    }
    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }
    public void setType(Integer type)
    {
        this.type = type;
    }

    public Integer getType()
    {
        return type;
    }
    public void setCoverUrl(String coverUrl)
    {
        this.coverUrl = coverUrl;
    }

    public String getCoverUrl()
    {
        return coverUrl;
    }
    public void setPublisher(String publisher)
    {
        this.publisher = publisher;
    }

    public String getPublisher()
    {
        return publisher;
    }
    public void setPublishTime(Date publishTime)
    {
        this.publishTime = publishTime;
    }

    public Date getPublishTime()
    {
        return publishTime;
    }
    public void setIsTop(String isTop)
    {
        this.isTop = isTop;
    }

    public String getIsTop()
    {
        return isTop;
    }
    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public Integer getStatus()
    {
        return status;
    }
    public void setViewCount(Integer viewCount)
    {
        this.viewCount = viewCount;
    }

    public Integer getViewCount()
    {
        return viewCount;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("stationId", getStationId())
            .append("stationName", getStationName())
            .append("title", getTitle())
            .append("summary", getSummary())
            .append("content", getContent())
            .append("type", getType())
            .append("coverUrl", getCoverUrl())
            .append("publisher", getPublisher())
            .append("publishTime", getPublishTime())
            .append("isTop", getIsTop())
            .append("status", getStatus())
            .append("viewCount", getViewCount())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
