import request from '@/utils/request'

// 查询工作通知反馈意见附件列表
export function listNoticefeedbackattachment(query) {
  return request({
    url: '/renda/noticefeedbackattachment/list',
    method: 'get',
    params: query
  })
}

// 查询工作通知反馈意见附件详细
export function getNoticefeedbackattachment(id) {
  return request({
    url: '/renda/noticefeedbackattachment/' + id,
    method: 'get'
  })
}

// 新增工作通知反馈意见附件
export function addNoticefeedbackattachment(data) {
  return request({
    url: '/renda/noticefeedbackattachment',
    method: 'post',
    data: data
  })
}

// 修改工作通知反馈意见附件
export function updateNoticefeedbackattachment(data) {
  return request({
    url: '/renda/noticefeedbackattachment',
    method: 'put',
    data: data
  })
}

// 删除工作通知反馈意见附件
export function delNoticefeedbackattachment(id) {
  return request({
    url: '/renda/noticefeedbackattachment/' + id,
    method: 'delete'
  })
}
