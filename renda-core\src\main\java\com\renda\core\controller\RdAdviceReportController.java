package com.renda.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.renda.common.annotation.Log;
import com.renda.common.core.controller.BaseController;
import com.renda.common.core.domain.AjaxResult;
import com.renda.common.enums.BusinessType;
import com.renda.core.domain.RdAdviceReport;
import com.renda.core.service.IRdAdviceReportService;
import com.renda.common.utils.poi.ExcelUtil;
import com.renda.common.core.page.TableDataInfo;

/**
 * 建议上报Controller
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@RestController
@RequestMapping("/renda/report")
public class RdAdviceReportController extends BaseController
{
    @Autowired
    private IRdAdviceReportService rdAdviceReportService;

    /**
     * 查询建议上报列表
     */
    @PreAuthorize("@ss.hasPermi('renda:report:list')")
    @GetMapping("/list")
    public TableDataInfo list(RdAdviceReport rdAdviceReport)
    {
        startPage();
        List<RdAdviceReport> list = rdAdviceReportService.selectRdAdviceReportList(rdAdviceReport);
        return getDataTable(list);
    }

    /**
     * 导出建议上报列表
     */
    @PreAuthorize("@ss.hasPermi('renda:report:export')")
    @Log(title = "建议上报", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RdAdviceReport rdAdviceReport)
    {
        List<RdAdviceReport> list = rdAdviceReportService.selectRdAdviceReportList(rdAdviceReport);
        ExcelUtil<RdAdviceReport> util = new ExcelUtil<RdAdviceReport>(RdAdviceReport.class);
        util.exportExcel(response, list, "建议上报数据");
    }

    /**
     * 获取建议上报详细信息
     */
    @PreAuthorize("@ss.hasPermi('renda:report:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rdAdviceReportService.selectRdAdviceReportById(id));
    }

    /**
     * 新增建议上报
     */
    @PreAuthorize("@ss.hasPermi('renda:report:add')")
    @Log(title = "建议上报", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RdAdviceReport rdAdviceReport)
    {
        return toAjax(rdAdviceReportService.insertRdAdviceReport(rdAdviceReport));
    }

    /**
     * 修改建议上报
     */
    @PreAuthorize("@ss.hasPermi('renda:report:edit')")
    @Log(title = "建议上报", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RdAdviceReport rdAdviceReport)
    {
        return toAjax(rdAdviceReportService.updateRdAdviceReport(rdAdviceReport));
    }

    /**
     * 删除建议上报
     */
    @PreAuthorize("@ss.hasPermi('renda:report:remove')")
    @Log(title = "建议上报", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rdAdviceReportService.deleteRdAdviceReportByIds(ids));
    }
}
