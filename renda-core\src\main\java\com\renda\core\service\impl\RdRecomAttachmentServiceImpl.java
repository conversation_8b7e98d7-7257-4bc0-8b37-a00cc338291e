package com.renda.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.renda.core.mapper.RdRecomAttachmentMapper;
import com.renda.core.domain.RdRecomAttachment;
import com.renda.core.service.IRdRecomAttachmentService;

/**
 * 代建议附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-23
 */
@Service
public class RdRecomAttachmentServiceImpl implements IRdRecomAttachmentService
{
    @Autowired
    private RdRecomAttachmentMapper rdRecomAttachmentMapper;

    /**
     * 查询代建议附件
     *
     * @param id 代建议附件主键
     * @return 代建议附件
     */
    @Override
    public RdRecomAttachment selectRdRecomAttachmentById(Long id)
    {
        return rdRecomAttachmentMapper.selectRdRecomAttachmentById(id);
    }

    /**
     * 查询代建议附件列表
     *
     * @param rdRecomAttachment 代建议附件
     * @return 代建议附件
     */
    @Override
    public List<RdRecomAttachment> selectRdRecomAttachmentList(RdRecomAttachment rdRecomAttachment)
    {
        return rdRecomAttachmentMapper.selectRdRecomAttachmentList(rdRecomAttachment);
    }

    /**
     * 新增代建议附件
     *
     * @param rdRecomAttachment 代建议附件
     * @return 结果
     */
    @Override
    public int insertRdRecomAttachment(RdRecomAttachment rdRecomAttachment)
    {
        return rdRecomAttachmentMapper.insertRdRecomAttachment(rdRecomAttachment);
    }

    /**
     * 修改代建议附件
     *
     * @param rdRecomAttachment 代建议附件
     * @return 结果
     */
    @Override
    public int updateRdRecomAttachment(RdRecomAttachment rdRecomAttachment)
    {
        return rdRecomAttachmentMapper.updateRdRecomAttachment(rdRecomAttachment);
    }

    /**
     * 批量删除代建议附件
     *
     * @param ids 需要删除的代建议附件主键
     * @return 结果
     */
    @Override
    public int deleteRdRecomAttachmentByIds(Long[] ids)
    {
        return rdRecomAttachmentMapper.deleteRdRecomAttachmentByIds(ids);
    }

    /**
     * 删除代建议附件信息
     *
     * @param id 代建议附件主键
     * @return 结果
     */
    @Override
    public int deleteRdRecomAttachmentById(Long id)
    {
        return rdRecomAttachmentMapper.deleteRdRecomAttachmentById(id);
    }

    /**
     * 删除建议附件信息
     *
     * @param recomId 建议主键
     * @return 结果
     */
    @Override
    public int deleteRdRecomAttachmentByRecomId(Long recomId) {
        return rdRecomAttachmentMapper.deleteRdRecomAttachmentByRecomId(recomId);
    }

    /**
     * 删除建议附件信息
     *
     * @param ids 建议主键
     * @return 结果
     */
    @Override
    public int deleteRdRecomAttachmentByRecomIds(Long[] ids) {
        return rdRecomAttachmentMapper.deleteRdRecomAttachmentByRecomIds(ids);
    }

}
