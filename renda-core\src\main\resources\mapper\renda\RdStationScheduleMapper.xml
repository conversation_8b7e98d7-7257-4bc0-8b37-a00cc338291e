<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renda.core.mapper.RdStationScheduleMapper">
    
    <resultMap type="RdStationSchedule" id="RdStationScheduleResult">
        <result property="id"    column="id"    />
        <result property="stationId"    column="station_id"    />
        <result property="stationName"    column="station_name"    />
        <result property="scheduleDate"    column="schedule_date"    />
        <result property="deputyId"    column="deputy_id"    />
        <result property="deputyName"    column="deputy_name"    />
        <result property="staffId"    column="staff_id"    />
        <result property="staffName"    column="staff_name"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectRdStationScheduleVo">
        select s.id, s.station_id, st.station_name, s.schedule_date, 
               s.deputy_id, d.name as deputy_name, 
               s.staff_id, sf.name as staff_name,
               s.remark, s.create_by, s.create_time, s.update_by, s.update_time 
        from rd_station_schedule s
        left join rd_station st on s.station_id = st.station_id
        left join rd_deputy d on s.deputy_id = d.id
        left join rd_station_staff sf on s.staff_id = sf.id
    </sql>

    <select id="selectRdStationScheduleList" parameterType="RdStationSchedule" resultMap="RdStationScheduleResult">
        <include refid="selectRdStationScheduleVo"/>
        <where>  
            <if test="stationId != null "> and s.station_id = #{stationId}</if>
            <if test="scheduleDate != null "> and s.schedule_date = #{scheduleDate}</if>
            <if test="deputyId != null "> and s.deputy_id = #{deputyId}</if>
            <if test="staffId != null "> and s.staff_id = #{staffId}</if>
            <if test="deputyName != null and deputyName != ''"> and d.name like concat('%', #{deputyName}, '%')</if>
            <if test="staffName != null and staffName != ''"> and sf.name like concat('%', #{staffName}, '%')</if>
        </where>
        order by s.schedule_date desc, s.create_time desc
    </select>
    
    <select id="selectRdStationScheduleById" parameterType="Long" resultMap="RdStationScheduleResult">
        <include refid="selectRdStationScheduleVo"/>
        where s.id = #{id}
    </select>

    <select id="checkStationScheduleExists" resultType="int">
        select count(*) 
        from rd_station_schedule 
        where station_id = #{stationId} 
          and schedule_date = #{scheduleDate}
          <if test="excludeId != null">
              and id != #{excludeId}
          </if>
    </select>
        
    <insert id="insertRdStationSchedule" parameterType="RdStationSchedule" useGeneratedKeys="true" keyProperty="id">
        insert into rd_station_schedule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stationId != null">station_id,</if>
            <if test="scheduleDate != null">schedule_date,</if>
            <if test="deputyId != null">deputy_id,</if>
            <if test="staffId != null">staff_id,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stationId != null">#{stationId},</if>
            <if test="scheduleDate != null">#{scheduleDate},</if>
            <if test="deputyId != null">#{deputyId},</if>
            <if test="staffId != null">#{staffId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRdStationSchedule" parameterType="RdStationSchedule">
        update rd_station_schedule
        <trim prefix="SET" suffixOverrides=",">
            <if test="stationId != null">station_id = #{stationId},</if>
            <if test="scheduleDate != null">schedule_date = #{scheduleDate},</if>
            <if test="deputyId != null">deputy_id = #{deputyId},</if>
            <if test="staffId != null">staff_id = #{staffId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRdStationScheduleById" parameterType="Long">
        delete from rd_station_schedule where id = #{id}
    </delete>

    <delete id="deleteRdStationScheduleByIds" parameterType="String">
        delete from rd_station_schedule where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>