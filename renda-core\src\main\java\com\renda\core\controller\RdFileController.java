package com.renda.core.controller;

import com.renda.common.annotation.Log;
import com.renda.common.config.RendaConfig;
import com.renda.common.core.controller.BaseController;
import com.renda.common.core.domain.AjaxResult;
import com.renda.common.core.page.TableDataInfo;
import com.renda.common.enums.BusinessType;
import com.renda.common.utils.SecurityUtils;
import com.renda.common.utils.file.FileUploadUtils;
import com.renda.common.utils.poi.ExcelUtil;
import com.renda.core.domain.RdFile;
import com.renda.core.service.IRdFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 制度文件Controller
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
@RestController
@RequestMapping("/renda/rendafile")
public class RdFileController extends BaseController
{
    @Autowired
    private IRdFileService erFileService;

    /**
     * 查询制度文件列表
     */
    @PreAuthorize("@ss.hasPermi('renda:rendafile:list')")
    @GetMapping("/list")
    public TableDataInfo list(RdFile erFile)
    {
        startPage();
        List<RdFile> list = erFileService.selectRdFileList(erFile);
        return getDataTable(list);
    }

    /**
     * 导出制度文件列表
     */
    @PreAuthorize("@ss.hasPermi('renda:rendafile:export')")
    @Log(title = "制度文件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RdFile erFile)
    {
        List<RdFile> list = erFileService.selectRdFileList(erFile);
        ExcelUtil<RdFile> util = new ExcelUtil<RdFile>(RdFile.class);
        util.exportExcel(response, list, "制度文件数据");
    }

    /**
     * 获取制度文件详细信息
     */
    @PreAuthorize("@ss.hasPermi('renda:rendafile:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(erFileService.selectRdFileById(id));
    }

    /**
     * 新增制度文件
     */
    @PreAuthorize("@ss.hasPermi('renda:rendafile:add')")
    @Log(title = "制度文件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RdFile erFile)
    {
        return toAjax(erFileService.insertRdFile(erFile));
    }

    /**
     * 修改制度文件
     */
    @PreAuthorize("@ss.hasPermi('renda:rendafile:edit')")
    @Log(title = "制度文件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RdFile erFile)
    {
        return toAjax(erFileService.updateRdFile(erFile));
    }

    /**
     * 删除制度文件
     */
    @PreAuthorize("@ss.hasPermi('renda:rendafile:remove')")
    @Log(title = "制度文件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(erFileService.deleteRdFileByIds(ids));
    }

    /***
     * 文件上传接口
     * @param file 文件
     * @return 文件名
     */
    @PreAuthorize("@ss.hasPermi('renda:rendafile:add')")
    @PostMapping(value = "/fileUpload")
    public AjaxResult uploadFile(@RequestParam("file") MultipartFile file) {
        try {
            // 保存文件
            String filename = FileUploadUtils.upload(RendaConfig.getUploadPath() + "/file", file);
            return AjaxResult.success(filename);
        } catch (IOException e) {
            e.printStackTrace();
            return AjaxResult.error(e.getMessage());
        }
    }

    /***
     * 删除上传的文件
     * @param filename 文件名
     * @return 结果
     * @throws Exception 异常
     */
    @PreAuthorize("@ss.hasPermi('renda:rendafile:add')")
    @PostMapping("/deleteUploadedFile")
    public AjaxResult deleteUploadedFile(@RequestBody String filename) throws Exception
    {
        erFileService.deleteRdFileByFilename(filename);
        return success();
    }

}
