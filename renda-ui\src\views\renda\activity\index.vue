<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="活动标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入活动标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="活动时间">
        <el-date-picker
          v-model="daterangeActivityDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="活动地点" prop="address">
        <el-input
          v-model="queryParams.address"
          placeholder="请输入活动地点"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发布单位" prop="publisher">
        <el-input
          v-model="queryParams.publisher"
          placeholder="请输入发布单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系电话" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入联系电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['renda:activity:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['renda:activity:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['renda:activity:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['renda:activity:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="activityList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="活动标题" align="center" prop="title">
        <template slot-scope="scope">
          <span class="title" @click="showDetail(scope.row)">{{scope.row.title}}</span>
        </template>
      </el-table-column>
      <el-table-column label="活动时间" align="center" prop="activityDate" width="100">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.activityDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="活动地点" align="center" prop="address" />
<!--      <el-table-column label="发布单位" align="center" prop="publisher" />-->
<!--      <el-table-column label="联系电话" align="center" prop="phone" />-->
      <el-table-column label="报名模式" align="center" prop="registrationMode" width="110">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.rd_registration_mode" :value="scope.row.registrationMode"/>
        </template>
      </el-table-column>
      <el-table-column label="总人数" align="center" prop="total" width="70" />
      <el-table-column label="报名" align="center" prop="registeredCount" width="70" />
      <el-table-column label="请假" align="center" prop="leaveCount" width="70" />
      <el-table-column label="未报名" align="center" prop="notRegisteredCount" width="70" />
      <el-table-column label="允许报名" align="center" key="enabled" width="80">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.enabled"
            active-value="1"
            inactive-value="0"
            @change="handleEnabledChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="上线状态" align="center" key="status" width="80">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="1"
            inactive-value="0"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="发布人" align="center" prop="updateBy" width="100" />
      <el-table-column label="发布时间" align="center" prop="updateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="100">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['renda:activity:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['renda:activity:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改活动对话框 -->
    <el-dialog :title="detailDialogTitle" :visible.sync="showDetailDialog" width="1200px" append-to-body @closed="getList">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-phone-outline"
            size="mini"
            @click="handleBatchSendSms"
            v-hasPermi="['renda:activity:edit']"
          >群发短信</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExportList"
            v-hasPermi="['renda:activity:export']"
          >导出</el-button>
        </el-col>
      </el-row>
      <el-table v-loading="loadingDetail" :data="activityRegistrationList">
        <el-table-column label="序号" type="index" align="center">
          <template slot-scope="scope">
            <span>{{(detailPageNum - 1) * detailPageSize + scope.$index + 1}}</span>
          </template>
        </el-table-column>
        <el-table-column label="姓名" align="center" prop="deputyName" />
        <el-table-column label="电话" align="center" prop="phone" width="120" />
        <el-table-column label="报名状态" align="center" prop="registrationType" width="80">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.registration_type" :value="scope.row.registrationType"/>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" width="120" />
        <el-table-column label="报名时间" align="center" prop="createTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="短信次数" align="center" prop="smsSendCount" width="80" />
        <el-table-column label="短信时间" align="center" prop="smsSendTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.smsSendTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="短信结果" align="center" prop="smsSendStatus" width="80">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.rd_sms_status" :value="scope.row.smsSendStatus"/>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-message"
              @click="handleSendSms(scope.row)"
              v-hasPermi="['renda:activity:edit']"
            >发送短信</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDeleteDeputy(scope.row)"
              v-hasPermi="['renda:activity:edit']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="detailTotal>0"
        :total="detailTotal"
        :page.sync="queryDetailParams.pageNum"
        :limit.sync="queryDetailParams.pageSize"
        @pagination="getDetailList"
      />
    </el-dialog>

    <!-- 添加或修改活动对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body @closed="getList">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="活动标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入活动标题" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="活动时间" prop="activityDate">
              <el-date-picker clearable
                              v-model="form.activityDate"
                              type="datetime"
                              value-format="yyyy-MM-dd HH:mm:ss"
                              placeholder="请选择活动时间"
                              default-time="10:00:00">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="活动地点" prop="address">
              <el-input v-model="form.address" placeholder="请输入活动地点" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="发布单位" prop="publisher">
              <el-input v-model="form.publisher" placeholder="请输入发布单位" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="活动内容" prop="content">
              <el-input v-model="form.content" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="其它要求" prop="other">
              <el-input v-model="form.other" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="报名模式" prop="registrationMode">
              <el-select v-model="form.registrationMode" placeholder="请选择" @change="registrationModeChanged">
                <el-option
                  v-for="dict in dict.type.rd_registration_mode"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="form.registrationMode == '1'">
            <el-form-item label="选择方式" prop="sendSms">
              <el-select v-model="treeMode" placeholder="请选择" @change="treeModeChanged" >
                <el-option key="0" label="按代表团选择" value="0" />
                <el-option key="1" label="按站点选择" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="form.registrationMode == '1'">
            <el-form-item label="发送短信" prop="sendSms">
              <el-switch
                v-model="form.sendSms"
                active-value="1"
                inactive-value="0"
                active-text="发送"
                inactive-text="不发送"
                active-color="#13ce66"
                inactive-color="#ff4949">
              </el-switch>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.registrationMode == '1'">
          <el-col :span="24">
            <tree-transfer
              :title="transTitle"
              :from_data="fromData"
              :to_data="toData"
              :defaultProps="{label:'label'}"
              :mode="mode"
              height="300px"
              filter>
            </tree-transfer>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listActivity,
  getActivity,
  delActivity,
  addActivity,
  updateActivity,
  changeActivityEnabled,
  changeActivityStatus,
  listActivityRegistration,
  delActivityRegistration,
  sendSms,
  batchSendSms
} from '@/api/renda/activity'
import treeTransfer from 'el-tree-transfer';
import { stationTreeSelectWithDeputy, groupTreeSelectWithDeputy } from '@/api/renda/station'

export default {
  name: "Activity",
  components: { treeTransfer },
  dicts: ['registration_type', 'rd_registration_mode', 'rd_sms_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 活动表格数据
      activityList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 其它要求时间范围
      daterangeActivityDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        activityDate: null,
        address: null,
        publisher: null,
        phone: null,
        content: null,
        other: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          { required: true, message: "活动标题不能为空", trigger: "blur" }
        ],
        activityDate: [
          { required: true, message: "活动时间不能为空", trigger: "blur" }
        ],
        address: [
          { required: true, message: "活动地点不能为空", trigger: "blur" }
        ],
        publisher: [
          { required: true, message: "发布单位不能为空", trigger: "blur" }
        ],
        phone: [
          { required: true, message: "联系电话不能为空", trigger: "blur" }
        ],
        content: [
          { required: true, message: "活动内容不能为空", trigger: "blur" }
        ],
      },
      transTitle: ["待选","已选"],
      ordFromData: [], // 代表树
      fromData: [], // 可选择代表树
      toData: [], // 已选择代表树
      mode: "transfer", // transfer addressList
      detailId: null, // 报名详情活动id
      showDetailDialog: false, // 是否显示报名详情窗口
      detailDialogTitle: '', // 报名详情窗口标题
      activityRegistrationList: [], // 报名信息
      loadingDetail: false, // 报名信息加载中
      detailPageNum: 1, // 报名信息页码
      detailPageSize: 10, // 报名信息每页条数
      detailTotal: 0, // 报名信息总条数
      queryDetailParams: {
        pageNum: 1,
        pageSize: 10,
        activityId: null,
      },
      treeMode: '0', // 选择报名人员组织方式：0-按代表团形式；1-按所属站点形式
    };
  },
  created() {
    this.getList();
    this.getDeputyTree();
  },
  methods: {
    getDeputyTree() {
      this.ordFromData = [];
      if (this.treeMode === '0') {
        groupTreeSelectWithDeputy().then(response => {
          this.ordFromData = response.data;
        });
      } else {
        stationTreeSelectWithDeputy().then(response => {
          this.ordFromData = response.data;
        });
      }
    },
    /** 查询活动列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeActivityDate && '' != this.daterangeActivityDate) {
        this.queryParams.params["beginActivityDate"] = this.daterangeActivityDate[0];
        this.queryParams.params["endActivityDate"] = this.daterangeActivityDate[1];
      }
      listActivity(this.queryParams).then(response => {
        this.activityList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        title: null,
        activityDate: null,
        address: null,
        publisher: null,
        phone: null,
        content: null,
        other: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        registrationMode: '0', // 默认为指定报名
        sendSms: '0', // 默认不发送短信
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeActivityDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.fromData = JSON.parse(JSON.stringify(this.ordFromData));
      this.toData = [];
      this.reset();
      this.open = true;
      this.title = "添加活动";
    },
    filterTree(tree, idList) {
      function traverse(node) {
        if (node.children && node.children.length > 0) {
          node.children = node.children.filter(traverse);
          if (node.children.length > 0) {
            return true;
          } else {
            return false;
          }
        }
        if (node.leaf && idSet.has(node.id)) {
          return true
        } else {
          return false
        }
      }

      const idSet = new Set(idList);
      tree = tree.filter(traverse);
      return tree;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.fromData = JSON.parse(JSON.stringify(this.ordFromData));
      this.reset();
      const id = row.id || this.ids
      getActivity(id).then(response => {
        this.form = response.data;
        if (this.form.registrationList && this.form.registrationList.length > 0) {
          let tree = JSON.parse(JSON.stringify(this.ordFromData));
          let idList = this.form.registrationList.map(item => item.deputyId);
          let result = this.filterTree(tree, idList);
          this.toData = result;
        } else {
          this.toData = [];
        }
        this.open = true;
        this.title = "修改活动";
      });
    },
    getRegistrations(tree) {
      let leafNodes = [];
      function traverse(node) {
        if (node.leaf) {
          leafNodes.push({ deputyId: node.id });
        } else if (Array.isArray(node.children)) {
          for (const child of node.children) {
            traverse(child);
          }
        }
      }
      for (const node of tree) {
        traverse(node);
      }
      return leafNodes;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.registrationMode === '0') {
            // 自有报名模式下，清空已选择的代表
            this.toData = [];
            this.form.registrationList = [];
          } else {
            // 获取已选择的代表
            let registrations = this.getRegistrations(this.toData);
            if (registrations == null || registrations.length == 0) {
              this.$modal.msgError("请选择代表");
              return;
            }
            this.form.registrationList = registrations;
          }
          // 保存活动信息
          if (this.form.id != null) {
            updateActivity(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addActivity(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除活动？').then(function() {
        return delActivity(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('renda/activity/export', {
        ...this.queryParams
      }, `activity_${new Date().getTime()}.xlsx`)
    },
    /** 能否报名状态修改 */
    handleEnabledChange(row) {
      let text = row.enabled === "1" ? "开启" : "关闭";
      this.$modal.confirm('确认要【' + text + '】本活动报名通道吗？').then(function() {
        return changeActivityEnabled(row.id, row.enabled);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.enabled = row.enabled === "0" ? "1" : "0";
      });
    },
    /** 活动状态修改 */
    handleStatusChange(row) {
      let text = row.status === "1" ? "开启" : "关闭";
      this.$modal.confirm('确认要【' + text + '】本活动吗？').then(function() {
        return changeActivityStatus(row.id, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.status = row.status === "0" ? "1" : "0";
      });
    },
    registrationModeChanged(e) {
      // 如果是自由报名模式，则不发送短信
      if (e == '0') {
        this.form.sendSms = '0'
      }
    },
    showDetail(activity) {
      this.detailDialogTitle = activity.title;
      this.detailId = activity.id;
      this.getDetailList();
      this.showDetailDialog = true;
    },
    getDetailList() {
      this.loadingDetail = true;
      this.queryDetailParams["activityId"] = this.detailId;
      listActivityRegistration(this.queryDetailParams).then(response => {
        this.activityRegistrationList = response.rows;
        this.detailTotal = response.total;
        this.loadingDetail = false;
      });
    },
    handleSendSms(row) {
      this.$modal.confirm('是否确认发送短信？').then(function() {
        return sendSms(row);
      }).then(() => {
        this.getDetailList();
        this.$modal.msgSuccess("发送成功");
      }).catch(() => {});
    },
    handleDeleteDeputy(row) {
      this.$modal.confirm('是否确认删除代表？').then(function() {
        return delActivityRegistration(row.id);
      }).then(() => {
        this.getDetailList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出报名列表 */
    handleExportList() {
      this.download('renda/activity/exportRegistration', {
        activityId: this.detailId
      }, `活动报名_${new Date().getTime()}.xlsx`)
    },
    treeModeChanged() {
      this.ordFromData = [];
      if (this.treeMode === '0') {
        groupTreeSelectWithDeputy().then(response => {
          this.ordFromData = response.data;
          this.fromData = JSON.parse(JSON.stringify(this.ordFromData));
          this.toData = [];
        });
      } else {
        stationTreeSelectWithDeputy().then(response => {
          this.ordFromData = response.data;
          this.fromData = JSON.parse(JSON.stringify(this.ordFromData));
          this.toData = [];
        });
      }

    },
    handleBatchSendSms() {
      const that = this;
      this.$modal.confirm('是否确认群发短信？').then(function() {
        return batchSendSms({ id: that.detailId });
      }).then(() => {
        this.$modal.msgSuccess("群发成功");
      }).catch(() => {});
    }
  }
};
</script>

<style lang="scss" scoped>
  .title {
    color: #337ab7;
    text-decoration: none;
  }
  .title:hover {
    color: red;
    text-decoration: underline;
    cursor: pointer;
  }
</style>
