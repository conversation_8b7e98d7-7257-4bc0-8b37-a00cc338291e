<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renda.core.mapper.RdFileMapper">

    <resultMap type="RdFile" id="RdFileResult">
        <result property="id"    column="id"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileSize"    column="file_size"    />
        <result property="orderNum"    column="order_num"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectRdFileVo">
        select id, file_name, file_url, file_size, order_num,
               create_by, create_time, update_by, update_time, remark
          from rd_file f
    </sql>

    <select id="selectRdFileList" parameterType="RdFile" resultMap="RdFileResult">
        <include refid="selectRdFileVo"/>
        <where>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="remark != null  and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
        </where>
        order by order_num, id desc
    </select>

    <select id="selectRdFileById" parameterType="Long" resultMap="RdFileResult">
        <include refid="selectRdFileVo"/>
        where id = #{id}
    </select>

    <insert id="insertRdFile" parameterType="RdFile" useGeneratedKeys="true" keyProperty="id">
        insert into rd_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
            <if test="fileSize != null">file_size,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
            <if test="fileSize != null">#{fileSize},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateRdFile" parameterType="RdFile">
        update rd_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
            <if test="fileSize != null">file_size = #{fileSize},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRdFileById" parameterType="Long">
        delete from rd_file where id = #{id}
    </delete>

    <delete id="deleteRdFileByIds" parameterType="String">
        delete from rd_file where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
