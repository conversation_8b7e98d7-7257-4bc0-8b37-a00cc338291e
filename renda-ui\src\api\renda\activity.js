import request from '@/utils/request'

// 查询活动列表
export function listActivity(query) {
  return request({
    url: '/renda/activity/list',
    method: 'get',
    params: query
  })
}

// 查询活动详细
export function getActivity(id) {
  return request({
    url: '/renda/activity/' + id,
    method: 'get'
  })
}

// 新增活动
export function addActivity(data) {
  return request({
    url: '/renda/activity',
    method: 'post',
    data: data
  })
}

// 修改活动
export function updateActivity(data) {
  return request({
    url: '/renda/activity',
    method: 'put',
    data: data
  })
}

// 删除活动
export function delActivity(id) {
  return request({
    url: '/renda/activity/' + id,
    method: 'delete'
  })
}

// 能否报名状态修改
export function changeActivityEnabled(id, enabled) {
  const data = {
    id,
    enabled
  }
  return request({
    url: '/renda/activity/changeEnabled',
    method: 'put',
    data: data
  })
}

// 活动状态修改
export function changeActivityStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
    url: '/renda/activity/changeStatus',
    method: 'put',
    data: data
  })
}

// 活动报名列表
export function listActivityRegistration(query) {
  return request({
    url: '/renda/activity/listActivityRegistration',
    method: 'get',
    params: query
  })
}

// 删除报名代表
export function delActivityRegistration(id) {
  return request({
    url: '/renda/activity/delActivityRegistration/' + id,
    method: 'delete'
  })
}

// 单发短信
export function sendSms(data) {
  return request({
    url: '/renda/activity/sendSms',
    method: 'post',
    data: data
  })
}

// 群发短信
export function batchSendSms(data) {
  return request({
    url: '/renda/activity/batchSendSms',
    method: 'post',
    data: data
  })
}
