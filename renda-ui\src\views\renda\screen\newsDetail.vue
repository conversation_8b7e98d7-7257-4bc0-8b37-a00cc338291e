<template>
  <div class="app-container bg">
    <ScreenHeader ref="headerComp" />
    <div class="deputy-screen">
      <ScreenTitle caption="动态详情" more="" />
      <div class="deputy-info-content">
        <div class="job-container">
          <div class="job-title">{{notice.noticeTitle}}</div>
          <div class="job-desc">发布人：{{notice.noticePublisher}}<span class="space" />时间：{{formatReadableDate(notice.createTime)}}</div>
          <div class="content" ref="content"></div>
        </div>
      </div>
    </div>
    <el-image class="return-btn" :src="require('@/assets/images/screen/return.png')" @click="onBack"></el-image>
  </div>
</template>

<script>

import ScreenHeader from './components/screenHeader';
import ScreenTitle from './components/screenTitle';
import { formatReadableDate } from '@/api/renda/utils';
import { getNotice } from '@/api/renda/screen';

export default {
  name: "NewsDetail",
  components: { ScreenHeader, ScreenTitle },
  data() {
    return {
      defaultAvatar: require("@/assets/images/avatar.png"), // 默认头像
      baseURL: process.env.VUE_APP_BASE_API, // 图片上传地址头
      noticeId: 0, // 人大代表id
      notice: {}, // 人大代表信息
    };
  },
  activated() {
    this.noticeId = this.$route.query.noticeId;
    this.loadData();
  },
  mounted() {
    this.$nextTick(() => {
      document.oncontextmenu = function() {
        return false;
      };
    });
  },
  methods: {
    loadData() {
      getNotice(this.noticeId).then(res => {
        this.notice = res.data;
        this.$refs.content.innerHTML = this.notice.noticeContent;
      });
    },
    formatReadableDate(date) {
      return formatReadableDate(date)
    },
    onBack() {
      this.$refs.headerComp.onClick();
    },
  }
};
</script>

<style lang="scss" scoped>

@import '../../../common/font/font.css';

.deputy-screen {
  margin: 250px 850px 100px 850px;
  padding: 50px 50px;
  height: 1826px;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .deputy-info-content {
    margin-top: 20px;
    height: 1600px;
    padding: 100px 400px;
    background: #FFF;
    box-shadow: 0px 0px 2px 0px rgba(0,0,0,0.1);

    .job-container {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: scroll;

      .job-title {
        margin: 48px;
        font-family: AL-B;
        font-size: 44px;
        text-align: center;
      }
      .job-desc {
        margin: 24px;
        font-family: AL-L;
        font-size: 26px;
        text-align: center;
        color: #504B4A;
      }
      .content {
        font-size: 24px;
        line-height: 1.5;
      }
    }
    .job-container::-webkit-scrollbar {
      display:none;
    }

  }
}

</style>
