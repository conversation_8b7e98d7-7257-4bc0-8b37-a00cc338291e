<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="联络站" prop="stationId">
        <treeselect
          v-model="queryParams.stationId"
          :options="stationOptions"
          :normalizer="normalizer"
          placeholder="请选择联络站"
          clearable
          style="width: 300px;"
        />
      </el-form-item>
      <el-form-item label="动态标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入动态标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="动态摘要" prop="summary">
        <el-input
          v-model="queryParams.summary"
          placeholder="请输入动态摘要"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发布人" prop="publisher">
        <el-input
          v-model="queryParams.publisher"
          placeholder="请输入发布人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发布时间" prop="publishTime">
        <el-date-picker clearable
          v-model="queryParams.publishTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择发布时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['renda:stationnews:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['renda:stationnews:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['renda:stationnews:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['renda:stationnews:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="stationnewsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="联络站" align="center" prop="stationName" />
      <el-table-column label="动态标题" align="center" prop="title" />
      <el-table-column label="动态摘要" align="center" prop="summary" />
      <el-table-column label="动态类型" align="center" prop="type" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.rd_news_type" :value="scope.row.type"/>
        </template>
      </el-table-column>
      <el-table-column label="发布人" align="center" prop="publisher" />
      <el-table-column label="发布时间" align="center" prop="publishTime" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.publishTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否置顶" align="center" width="100">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.isTop"
            active-value="1"
            inactive-value="0"
            @change="handleTopChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="100">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['renda:stationnews:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['renda:stationnews:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改联络站动态对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="联络站" prop="stationId">
              <treeselect
                v-model="form.stationId"
                :options="stationOptions"
                :normalizer="normalizer"
                placeholder="请选择联络站"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="动态标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入动态标题" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="动态摘要" prop="summary">
              <el-input v-model="form.summary" placeholder="请输入动态摘要" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="动态类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择动态类型" style="width: 100%;">
                <el-option
                  v-for="dict in dict.type.rd_news_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio :label="0">草稿</el-radio>
                <el-radio :label="1">发布</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="动态内容">
              <editor v-model="form.content" :min-height="260" :height="260"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="封面图片" prop="coverUrl">
              <el-upload
                class="cover-uploader"
                :headers="headers"
                :action="uploadUrl"
                :show-file-list="false"
                :on-success="handleCoverSuccess"
                :before-upload="beforeCoverUpload">
                <el-image v-if="form.coverUrl" class="cover-img" :src="baseURL + form.coverUrl" fit="cover"></el-image>
                <i v-else class="el-icon-plus cover-uploader-icon"></i>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发布人" prop="publisher">
              <el-input v-model="form.publisher" placeholder="请输入发布人" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="发布时间" prop="publishTime">
              <el-date-picker clearable
                v-model="form.publishTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择发布时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否置顶">
              <el-switch
                v-model="form.isTop"
                active-value="1"
                inactive-value="0"
              ></el-switch>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listStationnews, getStationnews, delStationnews, addStationnews, updateStationnews } from "@/api/renda/stationnews";
import { stationTreeSelect } from "@/api/renda/station";
import { getToken } from '@/utils/auth'
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Stationnews",
  dicts: ['rd_news_type'],
  components: { Treeselect },
  data() {
    return {
      baseURL: process.env.VUE_APP_BASE_API, // 图片上传地址头
      uploadUrl: process.env.VUE_APP_BASE_API + "/common/upload", // 上传的图片服务器地址
      headers: {
        Authorization: "Bearer " + getToken()
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 联络站动态表格数据
      stationnewsList: [],
      // 联络站树选项
      stationOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        stationId: null,
        title: null,
        summary: null,
        content: null,
        type: null,
        coverUrl: null,
        publisher: null,
        publishTime: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        stationId: [
          { required: true, message: "联络站不能为空", trigger: "change" }
        ],
        title: [
          { required: true, message: "动态标题不能为空", trigger: "blur" }
        ],
        summary: [
          { required: true, message: "动态摘要不能为空", trigger: "blur" }
        ],
        content: [
          { required: true, message: "动态内容不能为空", trigger: "blur" }
        ],
        type: [
          { required: true, message: "动态类型不能为空", trigger: "change" }
        ],
        publisher: [
          { required: true, message: "发布人不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
    this.getTreeselect();
  },
  methods: {
    /** 查询联络站动态列表 */
    getList() {
      this.loading = true;
      listStationnews(this.queryParams).then(response => {
        this.stationnewsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询联络站下拉树结构 */
    getTreeselect() {
      stationTreeSelect().then(response => {
        this.stationOptions = response.data;
      });
    },
    /** 转换联络站数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.label,
        children: node.children
      };
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        stationId: null,
        title: null,
        summary: null,
        content: null,
        type: null,
        coverUrl: null,
        publisher: null,
        publishTime: null,
        isTop: "0",
        status: 0,
        viewCount: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加联络站动态";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getStationnews(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改联络站动态";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateStationnews(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addStationnews(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除联络站动态编号为"' + ids + '"的数据项？').then(function() {
        return delStationnews(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('renda/stationnews/export', {
        ...this.queryParams
      }, `stationnews_${new Date().getTime()}.xlsx`)
    },
    /** 置顶状态修改 */
    handleTopChange(row) {
      let text = row.isTop === "1" ? "置顶" : "取消置顶";
      this.$modal.confirm('确认要【' + text + '】该动态吗？').then(() => {
        return updateStationnews(row);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
        this.getList();
      }).catch(() => {
        row.isTop = row.isTop === "0" ? "1" : "0";
      });
    },
    /** 状态修改 */
    handleStatusChange(row) {
      let text = row.status === 1 ? "发布" : "下线";
      this.$modal.confirm('确认要【' + text + '】该动态吗？').then(() => {
        return updateStationnews(row);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
        this.getList();
      }).catch(() => {
        row.status = row.status === 0 ? 1 : 0;
      });
    },
    beforeCoverUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isJPG && !isPNG) {
        this.$message.error('上传图片只能是 JPG/PNG 格式!')
      }
      if (!isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!')
      }
      return (isJPG || isPNG) && isLt5M
    },
    handleCoverSuccess(res, file) {
      this.form.coverUrl = res.fileName
    }
  }
};
</script>

<style lang="scss" scoped>
  .cover-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 150px;
    height: 100px;
    line-height: 100px;
    text-align: center;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .cover-img {
    width: 150px;
    height: 100px;
    display: block;
    border-radius: 6px;
  }
</style>
