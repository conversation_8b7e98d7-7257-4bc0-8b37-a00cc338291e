<template>
  <div class="home">
    <app-header :showBackBtn="false" />

    <main class="content">
      <div class="background-elements">
        <div class="circle circle-1"></div>
        <div class="circle circle-2"></div>
        <div class="circle circle-3"></div>
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
      </div>

      <div class="platform-intro">
        <img src='@/assets/images/screen/gh256.png' alt="国徽" class="emblem" />
        <h2>欢迎使用哈密市人大代表智慧管理平台</h2>
        <p>高效·便捷·智能·协同</p>
      </div>

      <div class="module-container">
        <div class="module module-highlight" @click="goToOrganization">
          <div class="module-icon">
            <div class="icon-circle">
              <i class="el-icon-office-building" style="font-size: 100px;"></i>
            </div>
          </div>
          <div class="module-title">
            <h2>组织机构</h2>
            <span><PERSON>u Zhi Ji Gou</span>
            <p class="module-desc">人大组织架构，机构职能介绍</p>
          </div>
          <div class="module-arrow">
            <i class="arrow-icon">→</i>
          </div>
        </div>

        <div class="module" @click="goToPhotoWall">
          <div class="module-icon">
            <div class="icon-circle">
              <i class="el-icon-s-promotion" style="font-size: 100px;"></i>
              <!-- <img :src="$getImage('@/assets/icon-profile.png')" alt="代表直通车" /> -->
            </div>
          </div>
          <div class="module-title">
            <h2>代表直通车</h2>
            <span>Dai Biao Zhi Tong Che</span>
            <p class="module-desc">直达代表专区，提交意见建议</p>
          </div>
          <div class="module-arrow">
            <i class="arrow-icon">→</i>
          </div>
        </div>

        <div class="module" @click="goToOpinionCollection">
          <div class="module-icon">
            <div class="icon-circle">
              <i class="el-icon-edit-outline" style="font-size: 100px;"></i>
            </div>
          </div>
          <div class="module-title">
            <h2>民意征集站</h2>
            <span>Min Yi Zheng Ji Zhan</span>
            <p class="module-desc">征集群众建议，了解民情民声</p>
          </div>
          <div class="module-arrow">
            <i class="arrow-icon">→</i>
          </div>
        </div>

        <div class="module" @click="goToPolicyPublicity">
          <div class="module-icon">
            <div class="icon-circle">
              <i class="el-icon-reading" style="font-size: 100px;"></i>
              <!-- <img :src="$getImage('@/assets/icon-suggestion.png')" alt="政策咨询站" /> -->
            </div>
          </div>
          <div class="module-title">
            <h2>政策宣传栏</h2>
            <span>Zheng Ce Xuan Chuan Lan</span>
            <p class="module-desc">政策宣传解读，人大工作动态</p>
          </div>
          <div class="module-arrow">
            <i class="arrow-icon">→</i>
          </div>
        </div>
      </div>
    </main>

    <footer class="footer">
      <div class="footer-content">
        <p>哈密市人民代表大会常务委员会 © 2025</p>
        <div class="footer-links">
          <span><i class="el-icon-phone"/> 联系我们：{{ screenTel }}</span>
          <!-- <span>网站地图</span> -->
          <!-- <span>隐私政策</span> -->
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
import AppHeader from '@/components/StationScreen/AppHeader.vue'

export default {
  name: 'Home',
  components: {
    AppHeader
  },
  data() {
    return {
      stationId: 0,
      screenTel: ''
    }
  },
  methods: {
    // 处理键盘事件
    handleKeydown(event) {
      // ESC键返回首页
      if (event.key === 'Escape') {
        this.goToHome()
      }
    },

    goToHome() {
      this.$router.push('/index')
    },

    goToOrganization() {
      this.$router.push({
        name: 'StationOrganization',
        query: {
          stationId: this.stationId
        }
      })
    },

    goToPhotoWall() {
      this.$router.push({
        name: 'PhotoWall',
        query: {
          stationId: this.stationId
        }
      })
    },

    goToOpinionCollection() {
      this.$router.push({
        name: 'OpinionCollection',
        query: {
          stationId: this.stationId
        }
      })
    },

    goToPolicyPublicity() {
      this.$router.push({
        name: 'PolicyPublicity',
        query: {
          stationId: this.stationId
        }
      })
    }
  },
  mounted() {
    this.$nextTick(() => {
      // 禁用右键菜单
      document.oncontextmenu = function() {
        return false;
      };
    });

    // 获取联络站Id
    this.stationId = this.$route.query.stationId
    console.log('联络站ID:', this.stationId)

    this.getConfigKey("rd.screen.tel").then(response => {
      this.screenTel = response.msg;
    });

    document.addEventListener('keydown', this.handleKeydown)
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.handleKeydown)
  }
}
</script>

<style lang="scss" scoped>
.home {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  color: #fff;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  margin: 0;
  padding: 0;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #f8f8f8, #efefef);
  position: relative;
  overflow: hidden;

  .background-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 0;
    pointer-events: none;

    .circle {
      position: absolute;
      border-radius: 50%;
      opacity: 0.06;

      &.circle-1 {
        width: 600px;
        height: 600px;
        background: #d71718;
        top: -200px;
        left: -200px;
      }

      &.circle-2 {
        width: 500px;
        height: 500px;
        background: #d71718;
        bottom: 20%;
        right: 15%;
      }

      &.circle-3 {
        width: 300px;
        height: 300px;
        background: #d71718;
        bottom: 35%;
        left: 5%;
      }
    }

    .shape {
      position: absolute;
      opacity: 0.04;
      background: #d71718;

      &.shape-1 {
        width: 500px;
        height: 500px;
        transform: rotate(45deg);
        top: 15%;
        right: -300px;
      }

      &.shape-2 {
        width: 400px;
        height: 400px;
        transform: rotate(30deg);
        bottom: -50px;
        left: 30%;
      }
    }
  }

  .platform-intro {
    text-align: center;
    margin-bottom: 200px !important;
    position: relative;
    z-index: 1;

    .emblem {
      width: 500px;
      height: 500px;
    }

    h2 {
      color: #d71718;
      font-size: 96px !important;
      font-weight: 500;
      margin: 0 0 15px;
      letter-spacing: 3px;
      font-family: 'AL-L' !important;
    }

    p {
      color: #666;
      font-size: 48px !important;
      margin: 0;
      letter-spacing: 5px;
      font-family: 'AL-L' !important;
    }
  }

  .module-container {
    display: flex;
    justify-content: center;
    max-width: 3400px;
    gap: 40px;
    position: relative;
    z-index: 1;
  }

  .module {
    width: 750px !important;
    height: 320px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 24px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    color: #d71718;
    display: flex;
    position: relative;

    &:hover {
      transform: translateY(-15px);
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
    }

    .module-icon {
      margin: 0 30px;
      width: 170px;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .icon-circle {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: rgba(215, 23, 24, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s;

        img {
          width: 80px;
          height: 80px;
          object-fit: contain;
          transition: transform 0.3s;
        }

        i {
          font-size: 80px !important;
        }
      }
    }

    &:hover .module-icon .icon-circle {
      background: rgba(215, 23, 24, 0.15);
      transform: scale(1.05);
    }

    &:hover .module-icon img {
      transform: scale(1.1);
    }

    .module-title {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      text-align: left;
      padding-right: 30px;

      h2 {
        font-size: 38px;
        margin: 0;
        font-weight: 700;
        font-family: 'AL-BL' !important;
      }

      span {
        font-size: 16px;
        opacity: 0.6;
        margin-top: 8px;
      }

      .module-desc {
        font-size: 16px;
        color: #666;
        margin-top: 20px;
        line-height: 1.6;
      }
    }

    .module-arrow {
      position: absolute;
      right: 30px;
      bottom: 30px;
      width: 45px;
      height: 45px;
      display: flex;
      align-items: center;
      justify-content: center;

      .arrow-icon {
        font-size: 32px;
        opacity: 0.3;
        transition: all 0.3s;
        font-style: normal;
      }
    }

    &:hover .module-arrow .arrow-icon {
      opacity: 1;
      transform: translateX(5px);
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 10px;
      height: 100%;
      background: rgba(215, 23, 24, 0.7);
      transform: scaleY(0);
      transform-origin: top;
      transition: transform 0.3s;
    }

    &:hover::before {
      transform: scaleY(1);
    }
  }

  .module-highlight {
    background: linear-gradient(135deg, rgba(215, 23, 24, 0.95), rgba(180, 22, 22, 0.95));
    color: #fff;

    .module-title {
      .module-desc {
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .module-icon {
      .icon-circle {
        background: rgba(255, 255, 255, 0.15);
      }
    }

    &:hover .module-icon .icon-circle {
      background: rgba(255, 255, 255, 0.25);
    }

    .module-arrow {
      .arrow-icon {
        color: #fff;
      }
    }

    &::before {
      background: rgba(255, 255, 255, 0.8);
    }
  }
}

.footer {
  height: 70px;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 10;
  font-family: 'AL-L' !important;

  .footer-content {
    width: 100%;
    max-width: 1200px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 50px;

    p {
      font-size: 30px !important;
      opacity: 0.9;
      margin: 0;
    }

    .footer-links {
      display: flex;
      gap: 20px;

      span {
        font-size: 30px !important;
        opacity: 0.7;
        cursor: pointer;
        transition: opacity 0.2s;

        &:hover {
          opacity: 1;
        }
      }
    }
  }
}

@media (max-width: 3500px) {
  .content .module-container {
    flex-wrap: wrap;
    gap: 30px;
  }

  .content .module {
    width: calc(50% - 15px);
    min-width: 750px;
  }
}

@media (max-width: 1100px) {
  .content .module-container {
    flex-direction: column;
    align-items: center;
    gap: 30px;
  }

  .content .module {
    width: 90%;
    max-width: 750px;
  }

  .footer .footer-content {
    flex-direction: column;
    gap: 10px;
    padding: 10px 20px;
  }

  .footer {
    height: auto;
    padding: 15px 0;
  }
}

/* 4K分辨率适配 */
@media (min-width: 3000px) {
  .content {
    padding: 60px 40px;

    .platform-intro {
      margin-bottom: 100px;

      h2 {
        font-size: 72px;
        margin: 0 0 30px;
        letter-spacing: 5px;
        font-family: 'AL-B';
      }

      p {
        font-size: 36px;
        letter-spacing: 8px;
      }
    }

    .module-container {
      max-width: 3800px;
      gap: 120px;
    }

    .module {
      width: 900px;
      height: 450px;
      border-radius: 32px;
      box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);

      .module-icon {
        margin: 0 30px;
        width: 250px;

        .icon-circle {
          width: 200px;
          height: 200px;

          img {
            width: 130px;
            height: 130px;
          }

          i {
            font-size: 150px !important;
          }
        }
      }

      .module-title {
        padding-right: 60px;

        h2 {
          font-size: 64px;
        }

        span {
          font-size: 24px;
          margin-top: 15px;
        }

        .module-desc {
          font-size: 28px;
          margin-top: 35px;
        }
      }

      .module-arrow {
        right: 60px;
        bottom: 60px;
        width: 70px;
        height: 70px;

        .arrow-icon {
          font-size: 48px;
        }
      }

      &::before {
        width: 15px;
      }
    }
  }

  .footer {
    height: 100px;

    .footer-content {
      max-width: 2400px;
      padding: 0 80px;

      p {
        font-size: 22px;
      }

      .footer-links {
        gap: 40px;

        span {
          font-size: 20px;
        }
      }
    }
  }
}
</style>
