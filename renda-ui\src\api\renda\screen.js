import request from '@/utils/request'

// 查询群众建议详细
export function getAdvice(id) {
  return request({
    url: '/renda/screen/advice/' + id,
    method: 'get'
  })
}

// 查询群众建议列表
export function listAdviceWithFeedback(query) {
  return request({
    url: '/renda/screen/advice/listWithFeedback',
    method: 'get',
    params: query
  })
}

// 查询人大代表管理详细
export function getDeputy(id) {
  return request({
    url: '/renda/screen/deputy/' + id,
    method: 'get'
  })
}

// 获取人大代表履职统计信息
export function getDeputyStat(id) {
  return request({
    url: '/renda/screen/deputy/deputyStat/' + id,
    method: 'get'
  })
}

// 查询代表建议列表
export function listRecom(query) {
  return request({
    url: '/renda/screen/recom/list',
    method: 'get',
    params: query
  })
}

// 查询履职工作列表
export function listJob(query) {
  return request({
    url: '/renda/screen/Job/list',
    method: 'get',
    params: query
  })
}

// 查询人大代表管理列表
export function listDeputy2(query) {
  return request({
    url: '/renda/screen/deputy/list2',
    method: 'get',
    params: query
  })
}

// 查询站点下拉树结构
export function stationTreeSelect() {
  return request({
    url: '/renda/screen/station/stationTree',
    method: 'get'
  })
}

// 查询履职工作详细
export function getJobExt(id) {
  return request({
    url: '/renda/screen/Job/Ext/' + id,
    method: 'get'
  })
}

// 获取代表履职统计信息
export function getJobStat(id) {
  return request({
    url: '/renda/screen/Job/stat/',
    method: 'get'
  })
}

// 查询公告列表
export function listNotice(query) {
  return request({
    url: '/renda/screen/notice/list',
    method: 'get',
    params: query
  })
}

// 查询公告详细
export function getNotice(noticeId) {
  return request({
    url: '/renda/screen/notice/' + noticeId,
    method: 'get'
  })
}
