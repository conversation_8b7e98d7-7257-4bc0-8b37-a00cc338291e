{"remainingRequest": "D:\\renda\\renda-back\\renda-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\renda\\renda-back\\renda-ui\\src\\views\\renda\\advice\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\renda\\renda-back\\renda-ui\\src\\views\\renda\\advice\\index.vue", "mtime": 1752730489373}, {"path": "D:\\renda\\renda-back\\renda-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\renda\\renda-back\\renda-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\renda\\renda-back\\renda-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\renda\\renda-back\\renda-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0QWR2aWNlLCBnZXRBZHZpY2UsIGRlbEFkdmljZSwgdXBkYXRlQWR2aWNlIH0gZnJvbSAnQC9hcGkvcmVuZGEvYWR2aWNlJw0KaW1wb3J0IHsgZ2V0RmVlZGJhY2ssIHVwZGF0ZUZlZWRiYWNrIH0gZnJvbSAnQC9hcGkvcmVuZGEvZmVlZGJhY2snDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkFkdmljZSIsDQogIGRpY3RzOiBbJ3JkX2FkdmljZV9jYXRlZ29yeScsICdyZF9hZHZpY2Vfc3RhdHVzJ10sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDnvqTkvJflu7rorq7ooajmoLzmlbDmja4NCiAgICAgIGFkdmljZUxpc3Q6IFtdLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIHRpdGxlOiBudWxsLA0KICAgICAgICBjb250ZW50OiBudWxsLA0KICAgICAgICBuYW1lOiBudWxsLA0KICAgICAgICBwaG9uZTogbnVsbCwNCiAgICAgIH0sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHt9LA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczogew0KICAgICAgICB0aXRsZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlu7rorq7moIfpopjkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICB7IG1pbjogMSwgbWF4OiAyMDAsIG1lc3NhZ2U6ICLmoIfpopjplb/luqblupTlnKgx5YiwMjAw5Liq5a2X56ym5LmL6Ze0IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgICAgeyB2YWxpZGF0b3I6IHRoaXMudmFsaWRhdGVUaXRsZSwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgY29udGVudDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlu7rorq7lhoXlrrnkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICB7IG1pbjogMSwgbWF4OiAyMDAwLCBtZXNzYWdlOiAi5YaF5a656ZW/5bqm5bqU5ZyoMeWIsDIwMDDkuKrlrZfnrKbkuYvpl7QiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICB7IHZhbGlkYXRvcjogdGhpcy52YWxpZGF0ZUNvbnRlbnQsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIGNyZWF0ZVRpbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5bu66K6u5pe26Ze05LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfSwNCiAgICAgICAgICB7IHZhbGlkYXRvcjogdGhpcy52YWxpZGF0ZUNyZWF0ZVRpbWUsIHRyaWdnZXI6ICJjaGFuZ2UiIH0NCiAgICAgICAgXSwNCiAgICAgICAgY2F0ZWdvcnk6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5bu66K6u57G75Yir5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfQ0KICAgICAgICBdLA0KICAgICAgICBzdGF0dXM6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5aSE55CG54q25oCB5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfQ0KICAgICAgICBdLA0KICAgICAgfSwNCiAgICAgIHNob3dBZHZpY2VEZXRhaWw6IGZhbHNlLCAvLyDmmL7npLrnvqTkvJflu7rorq7or6bmg4Xlr7nor53moYYNCiAgICAgIGFkdmljZUZvcm06IHt9LCAvLyDnvqTkvJflu7rorq7ooajljZUNCiAgICAgIGJhc2VVUkw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEksIC8vIOWbvueJh+S4iuS8oOWcsOWdgOWktA0KICAgICAgc3JjTGlzdDogW10sIC8vIOWbvueJh+mihOiniOWIl+ihqA0KICAgICAgLy8g5Y+N6aaI57yW6L6R55u45YWzDQogICAgICBmZWVkYmFja0VkaXRPcGVuOiBmYWxzZSwgLy8g5pi+56S65Y+N6aaI57yW6L6R5a+56K+d5qGGDQogICAgICBmZWVkYmFja0Zvcm06IHt9LCAvLyDlj43ppojooajljZUNCiAgICAgIGZlZWRiYWNrUnVsZXM6IHsNCiAgICAgICAgY29udGVudDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlj43ppojlhoXlrrnkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICB7IG1pbjogMSwgbWF4OiAyMDAwLCBtZXNzYWdlOiAi5YaF5a656ZW/5bqm5bqU5ZyoMeWIsDIwMDDkuKrlrZfnrKbkuYvpl7QiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICB7IHZhbGlkYXRvcjogdGhpcy52YWxpZGF0ZUZlZWRiYWNrQ29udGVudCwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXSwNCiAgICAgICAgY3JlYXRlVGltZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlj43ppojml7bpl7TkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9LA0KICAgICAgICAgIHsgdmFsaWRhdG9yOiB0aGlzLnZhbGlkYXRlRmVlZGJhY2tUaW1lLCB0cmlnZ2VyOiAiY2hhbmdlIiB9DQogICAgICAgIF0NCiAgICAgIH0NCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpeivoue+pOS8l+W7uuiuruWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdEFkdmljZSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5hZHZpY2VMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgY29uc29sZS5sb2codGhpcy5hZHZpY2VMaXN0KQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDlj5bmtojmjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBpZDogbnVsbCwNCiAgICAgICAgdGl0bGU6IG51bGwsDQogICAgICAgIGNvbnRlbnQ6IG51bGwsDQogICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsDQogICAgICAgIGNhdGVnb3J5OiBudWxsLA0KICAgICAgICBzdGF0dXM6IG51bGwsDQogICAgICAgIHNlcnZpY2VSYXRpbmc6IG51bGwsDQogICAgICB9Ow0KICAgICAgdGhpcy5yZXNldEZvcm0oImVkaXRGb3JtIik7DQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uaWQpDQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGghPT0xDQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBjb25zdCBpZCA9IHJvdy5pZCB8fCB0aGlzLmlkcw0KICAgICAgZ2V0QWR2aWNlKGlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnnvqTkvJflu7rorq4iOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImVkaXRGb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgICAgIHVwZGF0ZUFkdmljZSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+abtOaWsOW7uuiuruWksei0pTonLCBlcnJvcik7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5L+u5pS55aSx6LSl77yM6K+356iN5ZCO6YeN6K+VIik7DQogICAgICAgICAgfSkuZmluYWxseSgoKSA9PiB7DQogICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgaWRzID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn56Gu5a6a5Yig6Zmk5pys5p2h5bu66K6u77yfJykudGhlbihmdW5jdGlvbigpIHsNCiAgICAgICAgcmV0dXJuIGRlbEFkdmljZShpZHMpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKCdyZW5kYS9hZHZpY2UvZXhwb3J0Jywgew0KICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zDQogICAgICB9LCBgYWR2aWNlXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQ0KICAgIH0sDQogICAgLyoqIOafpeeci+ivpuaDhSAqLw0KICAgIGhhbmRsZURldGFpbChyb3cpIHsNCiAgICAgIGdldEFkdmljZShyb3cuaWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmFkdmljZUZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLnNyY0xpc3QgPSBbXTsNCiAgICAgICAgdGhpcy5hZHZpY2VGb3JtLmF0dGFjaG1lbnRMaXN0LmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgaXRlbS5maWxlVXJsID0gdGhpcy5iYXNlVVJMICsgaXRlbS5maWxlVXJsDQogICAgICAgICAgaWYgKGl0ZW0uZmlsZVR5cGUgPT09IDEpIHsNCiAgICAgICAgICAgIHRoaXMuc3JjTGlzdC5wdXNoKGl0ZW0uZmlsZVVybCkNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgICB0aGlzLmFkdmljZUZvcm0uZmVlZGJhY2tMaXN0LmZvckVhY2goZmJpdGVtID0+IHsNCiAgICAgICAgICBmYml0ZW0uYXR0YWNobWVudExpc3QuZm9yRWFjaChmYml0ZW1JbWcgPT57DQogICAgICAgICAgICBmYml0ZW1JbWcuZmlsZVVybCA9IHRoaXMuYmFzZVVSTCArIGZiaXRlbUltZy5maWxlVXJsDQogICAgICAgICAgfSkNCiAgICAgICAgfSkNCiAgICAgICAgdGhpcy5hZHZpY2VGb3JtLmF0dGFjaG1lbnRMaXN0LmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgaWYgKGl0ZW0uZmlsZVR5cGUgPT09IDIpIHsNCiAgICAgICAgICAgIC8vIOaWh+S7tg0KICAgICAgICAgICAgLy8g6I635Y+W5paH5Lu25omp5bGV5ZCNDQogICAgICAgICAgICBjb25zdCBleHQgPSBpdGVtLmZpbGVVcmwuc3Vic3RyaW5nKGl0ZW0uZmlsZVVybC5sYXN0SW5kZXhPZignLicpICsgMSkudG9Mb3dlckNhc2UoKTsNCiAgICAgICAgICAgIGlmIChleHQgPT09ICdwZGYnKSB7DQogICAgICAgICAgICAgIGl0ZW0uaWNvbiA9IHJlcXVpcmUoJ0AvYXNzZXRzL2ltYWdlcy9zY3JlZW4vZmlsZXR5cGUvcGRmLnBuZycpOw0KICAgICAgICAgICAgfSBlbHNlIGlmIChleHQgPT09ICdkb2MnIHx8IGV4dCA9PT0gJ2RvY3gnKSB7DQogICAgICAgICAgICAgIGl0ZW0uaWNvbiA9IHJlcXVpcmUoJ0AvYXNzZXRzL2ltYWdlcy9zY3JlZW4vZmlsZXR5cGUvZG9jLnBuZycpOw0KICAgICAgICAgICAgfSBlbHNlIGlmIChleHQgPT09ICd4bHMnIHx8IGV4dCA9PT0gJ3hsc3gnKSB7DQogICAgICAgICAgICAgIGl0ZW0uaWNvbiA9IHJlcXVpcmUoJ0AvYXNzZXRzL2ltYWdlcy9zY3JlZW4vZmlsZXR5cGUveGxzLnBuZycpOw0KICAgICAgICAgICAgfSBlbHNlIGlmIChleHQgPT09ICdwcHQnIHx8IGV4dCA9PT0gJ3BwdHgnKSB7DQogICAgICAgICAgICAgIGl0ZW0uaWNvbiA9IHJlcXVpcmUoJ0AvYXNzZXRzL2ltYWdlcy9zY3JlZW4vZmlsZXR5cGUvcHB0LnBuZycpOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgaXRlbS5pY29uID0gcmVxdWlyZSgnQC9hc3NldHMvaW1hZ2VzL3NjcmVlbi9maWxldHlwZS91bmtub3cucG5nJyk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgICAgdGhpcy5hZHZpY2VGb3JtLmZlZWRiYWNrTGlzdC5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgIGl0ZW0uYXR0YWNobWVudExpc3QuZm9yRWFjaChmZWVkYmFja0l0ZW0gPT4gew0KICAgICAgICAgICAgaWYgKGZlZWRiYWNrSXRlbS5maWxlVHlwZSA9PT0gMikgew0KICAgICAgICAgICAgICAvLyDmlofku7YNCiAgICAgICAgICAgICAgLy8g6I635Y+W5paH5Lu25omp5bGV5ZCNDQogICAgICAgICAgICAgIGNvbnN0IGV4dCA9IGZlZWRiYWNrSXRlbS5maWxlVXJsLnN1YnN0cmluZyhmZWVkYmFja0l0ZW0uZmlsZVVybC5sYXN0SW5kZXhPZignLicpICsgMSkudG9Mb3dlckNhc2UoKTsNCiAgICAgICAgICAgICAgaWYgKGV4dCA9PT0gJ3BkZicpIHsNCiAgICAgICAgICAgICAgICBmZWVkYmFja0l0ZW0uaWNvbiA9IHJlcXVpcmUoJ0AvYXNzZXRzL2ltYWdlcy9zY3JlZW4vZmlsZXR5cGUvcGRmLnBuZycpOw0KICAgICAgICAgICAgICB9IGVsc2UgaWYgKGV4dCA9PT0gJ2RvYycgfHwgZXh0ID09PSAnZG9jeCcpIHsNCiAgICAgICAgICAgICAgICBmZWVkYmFja0l0ZW0uaWNvbiA9IHJlcXVpcmUoJ0AvYXNzZXRzL2ltYWdlcy9zY3JlZW4vZmlsZXR5cGUvZG9jLnBuZycpOw0KICAgICAgICAgICAgICB9IGVsc2UgaWYgKGV4dCA9PT0gJ3hscycgfHwgZXh0ID09PSAneGxzeCcpIHsNCiAgICAgICAgICAgICAgICBmZWVkYmFja0l0ZW0uaWNvbiA9IHJlcXVpcmUoJ0AvYXNzZXRzL2ltYWdlcy9zY3JlZW4vZmlsZXR5cGUveGxzLnBuZycpOw0KICAgICAgICAgICAgICB9IGVsc2UgaWYgKGV4dCA9PT0gJ3BwdCcgfHwgZXh0ID09PSAncHB0eCcpIHsNCiAgICAgICAgICAgICAgICBmZWVkYmFja0l0ZW0uaWNvbiA9IHJlcXVpcmUoJ0AvYXNzZXRzL2ltYWdlcy9zY3JlZW4vZmlsZXR5cGUvcHB0LnBuZycpOw0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIGZlZWRiYWNrSXRlbS5pY29uID0gcmVxdWlyZSgnQC9hc3NldHMvaW1hZ2VzL3NjcmVlbi9maWxldHlwZS91bmtub3cucG5nJyk7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfSk7DQogICAgICAgIHRoaXMuc2hvd0FkdmljZURldGFpbCA9IHRydWU7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOWkp+WbvumihOiniO+8jOWunueOsOeCueWHu+W9k+WJjeWbvueJh+aYvuekuuW9k+WJjeWbvueJh+Wkp+Wbvu+8jOWPr+S7pemaj+acuuWIh+aNouWIsOWFtuS7luWbvueJh+i/m+ihjOWxleekug0KICAgIGdldFByZXZpZXdJbWdMaXN0OmZ1bmN0aW9uKGF0dGFjaG1lbnRMaXN0LCBpbmRleCkgew0KICAgICAgbGV0IGFyciA9IFtdDQogICAgICBhdHRhY2htZW50TGlzdC5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICBpZiAoaXRlbS5maWxlVHlwZSA9PSAxKSB7DQogICAgICAgICAgYXJyLnB1c2goaXRlbS5maWxlVXJsKQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgcmV0dXJuIGFycjsNCiAgICB9LA0KICAgIC8qKiDnvJbovpHlj43ppojmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFZGl0RmVlZGJhY2soZmVlZGJhY2spIHsNCiAgICAgIHRoaXMucmVzZXRGZWVkYmFjaygpOw0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGdldEZlZWRiYWNrKGZlZWRiYWNrLmlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mZWVkYmFja0Zvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLmZlZWRiYWNrRWRpdE9wZW4gPSB0cnVlOw0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICBjb25zb2xlLmVycm9yKCfojrflj5blj43ppojor6bmg4XlpLHotKU6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6I635Y+W5Y+N6aaI6K+m5oOF5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VIik7DQogICAgICB9KS5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOWPjemmiOihqOWNlemHjee9rg0KICAgIHJlc2V0RmVlZGJhY2soKSB7DQogICAgICB0aGlzLmZlZWRiYWNrRm9ybSA9IHsNCiAgICAgICAgaWQ6IG51bGwsDQogICAgICAgIGNvbnRlbnQ6IG51bGwsDQogICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsDQogICAgICAgIHNlcnZpY2VSYXRpbmc6IG51bGwsDQogICAgICB9Ow0KICAgICAgdGhpcy5yZXNldEZvcm0oImZlZWRiYWNrRWRpdEZvcm0iKTsNCiAgICB9LA0KICAgIC8vIOWPlua2iOWPjemmiOe8lui+kQ0KICAgIGNhbmNlbEZlZWRiYWNrKCkgew0KICAgICAgdGhpcy5mZWVkYmFja0VkaXRPcGVuID0gZmFsc2U7DQogICAgICB0aGlzLnJlc2V0RmVlZGJhY2soKTsNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTlj43ppojnvJbovpHooajljZUgKi8NCiAgICBzdWJtaXRGZWVkYmFja0Zvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzWyJmZWVkYmFja0VkaXRGb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgICAgIHVwZGF0ZUZlZWRiYWNrKHRoaXMuZmVlZGJhY2tGb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWPjemmiOS/ruaUueaIkOWKnyIpOw0KICAgICAgICAgICAgdGhpcy5mZWVkYmFja0VkaXRPcGVuID0gZmFsc2U7DQogICAgICAgICAgICAvLyDph43mlrDliqDovb3lu7rorq7or6bmg4Xku6XliLfmlrDlj43ppojliJfooagNCiAgICAgICAgICAgIGlmICh0aGlzLnNob3dBZHZpY2VEZXRhaWwgJiYgdGhpcy5hZHZpY2VGb3JtLmlkKSB7DQogICAgICAgICAgICAgIHRoaXMuaGFuZGxlRGV0YWlsKHtpZDogdGhpcy5hZHZpY2VGb3JtLmlkfSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICAgICAgY29uc29sZS5lcnJvcign5pu05paw5Y+N6aaI5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLlj43ppojkv67mlLnlpLHotKXvvIzor7fnqI3lkI7ph43or5UiKTsNCiAgICAgICAgICB9KS5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOiHquWumuS5iemqjOivgeaWueazlQ0KICAgIHZhbGlkYXRlVGl0bGUocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7DQogICAgICBpZiAodmFsdWUgJiYgdmFsdWUudHJpbSgpLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+W7uuiuruagh+mimOS4jeiDveS4uuepuueZveWtl+espicpKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNhbGxiYWNrKCk7DQogICAgICB9DQogICAgfSwNCiAgICB2YWxpZGF0ZUNvbnRlbnQocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7DQogICAgICBpZiAodmFsdWUgJiYgdmFsdWUudHJpbSgpLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+W7uuiuruWGheWuueS4jeiDveS4uuepuueZveWtl+espicpKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGNhbGxiYWNrKCk7DQogICAgICB9DQogICAgfSwNCiAgICB2YWxpZGF0ZUNyZWF0ZVRpbWUocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7DQogICAgICBpZiAodmFsdWUpIHsNCiAgICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTsNCiAgICAgICAgY29uc3Qgc2VsZWN0ZWRUaW1lID0gbmV3IERhdGUodmFsdWUpOw0KICAgICAgICBpZiAoc2VsZWN0ZWRUaW1lID4gbm93KSB7DQogICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCflu7rorq7ml7bpl7TkuI3og73mmZrkuo7lvZPliY3ml7bpl7QnKSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY2FsbGJhY2soKTsNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgY2FsbGJhY2soKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIHZhbGlkYXRlRmVlZGJhY2tDb250ZW50KHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgew0KICAgICAgaWYgKHZhbHVlICYmIHZhbHVlLnRyaW0oKS5sZW5ndGggPT09IDApIHsNCiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCflj43ppojlhoXlrrnkuI3og73kuLrnqbrnmb3lrZfnrKYnKSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBjYWxsYmFjaygpOw0KICAgICAgfQ0KICAgIH0sDQogICAgdmFsaWRhdGVGZWVkYmFja1RpbWUocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSB7DQogICAgICBpZiAodmFsdWUpIHsNCiAgICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTsNCiAgICAgICAgY29uc3Qgc2VsZWN0ZWRUaW1lID0gbmV3IERhdGUodmFsdWUpOw0KICAgICAgICBpZiAoc2VsZWN0ZWRUaW1lID4gbm93KSB7DQogICAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCflj43ppojml7bpl7TkuI3og73mmZrkuo7lvZPliY3ml7bpl7QnKSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY2FsbGJhY2soKTsNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgY2FsbGJhY2soKTsNCiAgICAgIH0NCiAgICB9LA0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqWA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/renda/advice", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"标题\" prop=\"title\">\r\n        <el-input\r\n          v-model=\"queryParams.title\"\r\n          placeholder=\"请输入标题\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"姓名\" prop=\"name\">\r\n        <el-input\r\n          v-model=\"queryParams.name\"\r\n          placeholder=\"请输入姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"电话\" prop=\"phone\">\r\n        <el-input\r\n          v-model=\"queryParams.phone\"\r\n          placeholder=\"请输入电话\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['renda:advice:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['renda:advice:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"adviceList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"代表单位\" align=\"left\" prop=\"deputyCompany\" />\r\n      <el-table-column label=\"代表\" align=\"left\" prop=\"deputyName\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{scope.row.deputyName}}({{scope.row.deputyDuty}},{{scope.row.deputyPhone}})</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"标题\" align=\"left\" prop=\"title\" />\r\n      <el-table-column label=\"群众姓名\" align=\"center\" prop=\"name\" />\r\n      <el-table-column label=\"群众电话\" align=\"center\" prop=\"phone\" />\r\n      <el-table-column label=\"建议类别\" align=\"center\" prop=\"category\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.rd_advice_category\" :value=\"scope.row.category\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"处理状态\" align=\"center\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.rd_advice_status\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"服务评分\" align=\"center\" prop=\"serviceRating\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.serviceRating\">{{scope.row.serviceRating}}分</span>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"提交时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleDetail(scope.row)\"\r\n            v-hasPermi=\"['renda:advice:list']\"\r\n          >详情</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['renda:advice:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['renda:advice:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 建议详情对话框 -->\r\n    <el-dialog title=\"群众建议详情\" :visible.sync=\"showAdviceDetail\" width=\"1000px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"adviceForm\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"建议标题\" prop=\"title\">\r\n              <el-input v-model=\"adviceForm.title\" readonly />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"建议时间\" prop=\"createTime\">\r\n              <el-date-picker clearable\r\n                              readonly\r\n                              v-model=\"adviceForm.createTime\"\r\n                              type=\"datetime\"\r\n                              value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                              placeholder=\"请选择活动时间\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"建议类别\" prop=\"category\">\r\n              <dict-tag :options=\"dict.type.rd_advice_category\" :value=\"adviceForm.category\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"处理状态\" prop=\"status\">\r\n              <dict-tag :options=\"dict.type.rd_advice_status\" :value=\"adviceForm.status\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"服务评分\" prop=\"serviceRating\">\r\n              <span v-if=\"adviceForm.serviceRating\">{{adviceForm.serviceRating}}分</span>\r\n              <span v-else>-</span>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"建议内容\" prop=\"content\">\r\n              <el-input v-model=\"adviceForm.content\" type=\"textarea\" :rows=\"10\" readonly />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row v-if=\"adviceForm && adviceForm.attachmentList && adviceForm.attachmentList.length > 0\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"附件\" prop=\"attachments\">\r\n              <div v-for=\"(item, index) of adviceForm.attachmentList\" :key=\"index\" >\r\n                <el-link v-if=\"item.fileType === 2\" type=\"primary\" icon=\"el-icon-document\" :href=\"item.fileUrl\" target=\"_blank\">{{item.fileName}}</el-link>\r\n              </div>\r\n              <div class=\"img-container\" >\r\n                <el-image v-for=\"(item, index) of adviceForm.attachmentList\"\r\n                          :key=\"index\"\r\n                          v-if=\"item.fileType === 1\"\r\n                          class=\"img-item\"\r\n                          :src=\"item.fileUrl\"\r\n                          :preview-src-list=\"getPreviewImgList(adviceForm.attachmentList, index)\"></el-image>\r\n              </div>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <div class=\"item\">\r\n              <div class=\"label\">答复</div>\r\n              <div class=\"content\">\r\n                <el-steps direction=\"vertical\" :active=\"3\">\r\n                  <el-step v-for=\"(item, index) of adviceForm.feedbackList\" :key=\"index\">\r\n                    <div slot=\"title\" class=\"step-label\">\r\n                      {{item.name}}({{item.createTime}})\r\n                      <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-edit\"\r\n                        @click=\"handleEditFeedback(item)\"\r\n                        v-hasPermi=\"['renda:feedback:edit']\"\r\n                        style=\"margin-left: 10px;\"\r\n                      >编辑</el-button>\r\n                    </div>\r\n                    <div slot=\"description\" class=\"step-desc\">\r\n                      <div class=\"feedback-content\">{{item.content}}</div>\r\n                      <div v-for=\"(fileItem, fileIndex) of item.attachmentList\" :key=\"fileIndex\" >\r\n                        <el-link v-if=\"fileItem.fileType === 2\" type=\"primary\" icon=\"el-icon-document\" :href=\"fileItem.fileUrl\" target=\"_blank\">{{fileItem.fileName}}</el-link>\r\n                      </div>\r\n                      <div class=\"img-container\" >\r\n                        <el-image v-for=\"(imgItem, imgIndex) of item.attachmentList\"\r\n                                  :key=\"imgIndex\"\r\n                                  v-if=\"imgItem.fileType === 1\"\r\n                                  class=\"img-item\"\r\n                                  :src=\"imgItem.fileUrl\"\r\n                                  :preview-src-list=\"getPreviewImgList(item.attachmentList, imgIndex)\"></el-image>\r\n                      </div>\r\n                    </div>\r\n                  </el-step>\r\n                </el-steps>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-edit\"\r\n          @click=\"handleUpdate(adviceForm)\"\r\n          v-hasPermi=\"['renda:advice:edit']\"\r\n        >编辑建议</el-button>\r\n        <el-button @click=\"showAdviceDetail = false\">关闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 修改建议对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"editForm\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"建议标题\" prop=\"title\">\r\n              <el-input v-model=\"form.title\" placeholder=\"请输入建议标题\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"建议内容\" prop=\"content\">\r\n              <el-input v-model=\"form.content\" type=\"textarea\" :rows=\"6\" placeholder=\"请输入建议内容\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"建议时间\" prop=\"createTime\">\r\n              <el-date-picker\r\n                v-model=\"form.createTime\"\r\n                type=\"datetime\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                placeholder=\"请选择建议时间\"\r\n                style=\"width: 100%\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"建议类别\" prop=\"category\">\r\n              <el-select v-model=\"form.category\" placeholder=\"请选择建议类别\" clearable style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"dict in dict.type.rd_advice_category\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"处理状态\" prop=\"status\">\r\n              <el-select v-model=\"form.status\" placeholder=\"请选择处理状态\" clearable style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"dict in dict.type.rd_advice_status\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"服务评分\" prop=\"serviceRating\">\r\n              <el-rate\r\n                v-model=\"form.serviceRating\"\r\n                :max=\"5\"\r\n                :colors=\"['#99A9BF', '#F7BA2A', '#FF9900']\"\r\n                :texts=\"['非常差', '差', '一般', '好', '非常好']\"\r\n                show-text>\r\n              </el-rate>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" :loading=\"loading\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 修改反馈对话框 -->\r\n    <el-dialog title=\"修改反馈\" :visible.sync=\"feedbackEditOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"feedbackEditForm\" :model=\"feedbackForm\" :rules=\"feedbackRules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"反馈内容\" prop=\"content\">\r\n              <el-input v-model=\"feedbackForm.content\" type=\"textarea\" :rows=\"6\" placeholder=\"请输入反馈内容\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"反馈时间\" prop=\"createTime\">\r\n              <el-date-picker\r\n                v-model=\"feedbackForm.createTime\"\r\n                type=\"datetime\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                placeholder=\"请选择反馈时间\"\r\n                style=\"width: 100%\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"服务评分\" prop=\"serviceRating\">\r\n              <el-rate\r\n                v-model=\"feedbackForm.serviceRating\"\r\n                :max=\"5\"\r\n                :colors=\"['#99A9BF', '#F7BA2A', '#FF9900']\"\r\n                :texts=\"['非常差', '差', '一般', '好', '非常好']\"\r\n                show-text>\r\n              </el-rate>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" :loading=\"loading\" @click=\"submitFeedbackForm\">确 定</el-button>\r\n        <el-button @click=\"cancelFeedback\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listAdvice, getAdvice, delAdvice, updateAdvice } from '@/api/renda/advice'\r\nimport { getFeedback, updateFeedback } from '@/api/renda/feedback'\r\n\r\nexport default {\r\n  name: \"Advice\",\r\n  dicts: ['rd_advice_category', 'rd_advice_status'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 群众建议表格数据\r\n      adviceList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        title: null,\r\n        content: null,\r\n        name: null,\r\n        phone: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        title: [\r\n          { required: true, message: \"建议标题不能为空\", trigger: \"blur\" },\r\n          { min: 1, max: 200, message: \"标题长度应在1到200个字符之间\", trigger: \"blur\" },\r\n          { validator: this.validateTitle, trigger: \"blur\" }\r\n        ],\r\n        content: [\r\n          { required: true, message: \"建议内容不能为空\", trigger: \"blur\" },\r\n          { min: 1, max: 2000, message: \"内容长度应在1到2000个字符之间\", trigger: \"blur\" },\r\n          { validator: this.validateContent, trigger: \"blur\" }\r\n        ],\r\n        createTime: [\r\n          { required: true, message: \"建议时间不能为空\", trigger: \"change\" },\r\n          { validator: this.validateCreateTime, trigger: \"change\" }\r\n        ],\r\n        category: [\r\n          { required: true, message: \"建议类别不能为空\", trigger: \"change\" }\r\n        ],\r\n        status: [\r\n          { required: true, message: \"处理状态不能为空\", trigger: \"change\" }\r\n        ],\r\n      },\r\n      showAdviceDetail: false, // 显示群众建议详情对话框\r\n      adviceForm: {}, // 群众建议表单\r\n      baseURL: process.env.VUE_APP_BASE_API, // 图片上传地址头\r\n      srcList: [], // 图片预览列表\r\n      // 反馈编辑相关\r\n      feedbackEditOpen: false, // 显示反馈编辑对话框\r\n      feedbackForm: {}, // 反馈表单\r\n      feedbackRules: {\r\n        content: [\r\n          { required: true, message: \"反馈内容不能为空\", trigger: \"blur\" },\r\n          { min: 1, max: 2000, message: \"内容长度应在1到2000个字符之间\", trigger: \"blur\" },\r\n          { validator: this.validateFeedbackContent, trigger: \"blur\" }\r\n        ],\r\n        createTime: [\r\n          { required: true, message: \"反馈时间不能为空\", trigger: \"change\" },\r\n          { validator: this.validateFeedbackTime, trigger: \"change\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询群众建议列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listAdvice(this.queryParams).then(response => {\r\n        this.adviceList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n        console.log(this.adviceList)\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        title: null,\r\n        content: null,\r\n        createTime: null,\r\n        category: null,\r\n        status: null,\r\n        serviceRating: null,\r\n      };\r\n      this.resetForm(\"editForm\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids\r\n      getAdvice(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改群众建议\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"editForm\"].validate(valid => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          updateAdvice(this.form).then(response => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.open = false;\r\n            this.getList();\r\n          }).catch(error => {\r\n            console.error('更新建议失败:', error);\r\n            this.$modal.msgError(\"修改失败，请稍后重试\");\r\n          }).finally(() => {\r\n            this.loading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal.confirm('确定删除本条建议？').then(function() {\r\n        return delAdvice(ids);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('renda/advice/export', {\r\n        ...this.queryParams\r\n      }, `advice_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 查看详情 */\r\n    handleDetail(row) {\r\n      getAdvice(row.id).then(response => {\r\n        this.adviceForm = response.data;\r\n        this.srcList = [];\r\n        this.adviceForm.attachmentList.forEach(item => {\r\n          item.fileUrl = this.baseURL + item.fileUrl\r\n          if (item.fileType === 1) {\r\n            this.srcList.push(item.fileUrl)\r\n          }\r\n        });\r\n        this.adviceForm.feedbackList.forEach(fbitem => {\r\n          fbitem.attachmentList.forEach(fbitemImg =>{\r\n            fbitemImg.fileUrl = this.baseURL + fbitemImg.fileUrl\r\n          })\r\n        })\r\n        this.adviceForm.attachmentList.forEach(item => {\r\n          if (item.fileType === 2) {\r\n            // 文件\r\n            // 获取文件扩展名\r\n            const ext = item.fileUrl.substring(item.fileUrl.lastIndexOf('.') + 1).toLowerCase();\r\n            if (ext === 'pdf') {\r\n              item.icon = require('@/assets/images/screen/filetype/pdf.png');\r\n            } else if (ext === 'doc' || ext === 'docx') {\r\n              item.icon = require('@/assets/images/screen/filetype/doc.png');\r\n            } else if (ext === 'xls' || ext === 'xlsx') {\r\n              item.icon = require('@/assets/images/screen/filetype/xls.png');\r\n            } else if (ext === 'ppt' || ext === 'pptx') {\r\n              item.icon = require('@/assets/images/screen/filetype/ppt.png');\r\n            } else {\r\n              item.icon = require('@/assets/images/screen/filetype/unknow.png');\r\n            }\r\n          }\r\n        });\r\n        this.adviceForm.feedbackList.forEach(item => {\r\n          item.attachmentList.forEach(feedbackItem => {\r\n            if (feedbackItem.fileType === 2) {\r\n              // 文件\r\n              // 获取文件扩展名\r\n              const ext = feedbackItem.fileUrl.substring(feedbackItem.fileUrl.lastIndexOf('.') + 1).toLowerCase();\r\n              if (ext === 'pdf') {\r\n                feedbackItem.icon = require('@/assets/images/screen/filetype/pdf.png');\r\n              } else if (ext === 'doc' || ext === 'docx') {\r\n                feedbackItem.icon = require('@/assets/images/screen/filetype/doc.png');\r\n              } else if (ext === 'xls' || ext === 'xlsx') {\r\n                feedbackItem.icon = require('@/assets/images/screen/filetype/xls.png');\r\n              } else if (ext === 'ppt' || ext === 'pptx') {\r\n                feedbackItem.icon = require('@/assets/images/screen/filetype/ppt.png');\r\n              } else {\r\n                feedbackItem.icon = require('@/assets/images/screen/filetype/unknow.png');\r\n              }\r\n            }\r\n          });\r\n        });\r\n        this.showAdviceDetail = true;\r\n      });\r\n    },\r\n    // 大图预览，实现点击当前图片显示当前图片大图，可以随机切换到其他图片进行展示\r\n    getPreviewImgList:function(attachmentList, index) {\r\n      let arr = []\r\n      attachmentList.forEach(item => {\r\n        if (item.fileType == 1) {\r\n          arr.push(item.fileUrl)\r\n        }\r\n      })\r\n      return arr;\r\n    },\r\n    /** 编辑反馈按钮操作 */\r\n    handleEditFeedback(feedback) {\r\n      this.resetFeedback();\r\n      this.loading = true;\r\n      getFeedback(feedback.id).then(response => {\r\n        this.feedbackForm = response.data;\r\n        this.feedbackEditOpen = true;\r\n      }).catch(error => {\r\n        console.error('获取反馈详情失败:', error);\r\n        this.$modal.msgError(\"获取反馈详情失败，请稍后重试\");\r\n      }).finally(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 反馈表单重置\r\n    resetFeedback() {\r\n      this.feedbackForm = {\r\n        id: null,\r\n        content: null,\r\n        createTime: null,\r\n        serviceRating: null,\r\n      };\r\n      this.resetForm(\"feedbackEditForm\");\r\n    },\r\n    // 取消反馈编辑\r\n    cancelFeedback() {\r\n      this.feedbackEditOpen = false;\r\n      this.resetFeedback();\r\n    },\r\n    /** 提交反馈编辑表单 */\r\n    submitFeedbackForm() {\r\n      this.$refs[\"feedbackEditForm\"].validate(valid => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          updateFeedback(this.feedbackForm).then(response => {\r\n            this.$modal.msgSuccess(\"反馈修改成功\");\r\n            this.feedbackEditOpen = false;\r\n            // 重新加载建议详情以刷新反馈列表\r\n            if (this.showAdviceDetail && this.adviceForm.id) {\r\n              this.handleDetail({id: this.adviceForm.id});\r\n            }\r\n          }).catch(error => {\r\n            console.error('更新反馈失败:', error);\r\n            this.$modal.msgError(\"反馈修改失败，请稍后重试\");\r\n          }).finally(() => {\r\n            this.loading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    // 自定义验证方法\r\n    validateTitle(rule, value, callback) {\r\n      if (value && value.trim().length === 0) {\r\n        callback(new Error('建议标题不能为空白字符'));\r\n      } else {\r\n        callback();\r\n      }\r\n    },\r\n    validateContent(rule, value, callback) {\r\n      if (value && value.trim().length === 0) {\r\n        callback(new Error('建议内容不能为空白字符'));\r\n      } else {\r\n        callback();\r\n      }\r\n    },\r\n    validateCreateTime(rule, value, callback) {\r\n      if (value) {\r\n        const now = new Date();\r\n        const selectedTime = new Date(value);\r\n        if (selectedTime > now) {\r\n          callback(new Error('建议时间不能晚于当前时间'));\r\n        } else {\r\n          callback();\r\n        }\r\n      } else {\r\n        callback();\r\n      }\r\n    },\r\n    validateFeedbackContent(rule, value, callback) {\r\n      if (value && value.trim().length === 0) {\r\n        callback(new Error('反馈内容不能为空白字符'));\r\n      } else {\r\n        callback();\r\n      }\r\n    },\r\n    validateFeedbackTime(rule, value, callback) {\r\n      if (value) {\r\n        const now = new Date();\r\n        const selectedTime = new Date(value);\r\n        if (selectedTime > now) {\r\n          callback(new Error('反馈时间不能晚于当前时间'));\r\n        } else {\r\n          callback();\r\n        }\r\n      } else {\r\n        callback();\r\n      }\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .img-container {\r\n    display: flex;\r\n    flex-direction: row;\r\n    flex-wrap: wrap;\r\n  }\r\n  .img-item {\r\n    width: 200px;\r\n    height: 150px;\r\n    margin: 12px;\r\n  }\r\n  .item {\r\n    display: flex;\r\n    flex-direction: row;\r\n    .label {\r\n      width: 80px;\r\n      line-height: 36px;\r\n      text-align: right;\r\n      font-size: 14px;\r\n      color: #606266;\r\n      padding: 0 12px 0 0;\r\n      font-weight: 700;\r\n    }\r\n    .content {\r\n      flex: 1;\r\n      margin-top: 6px;\r\n    }\r\n  }\r\n  .step-label {\r\n    font-size: 14px;\r\n    color: #606266;\r\n  }\r\n  .step-desc {\r\n    font-size: 14px;\r\n    color: #606266;\r\n  }\r\n  .feedback-content {\r\n    margin: 10px 0;\r\n  }\r\n</style>\r\n"]}]}