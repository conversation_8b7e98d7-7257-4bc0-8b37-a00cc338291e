package com.renda.core.service.impl;

import java.util.Date;
import java.util.List;
import com.renda.common.utils.DateUtils;
import com.renda.common.utils.SecurityUtils;
import com.renda.core.service.IRdRecomAttachmentService;
import com.renda.core.service.IRdRecomDeputyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.renda.core.mapper.RdRecomMapper;
import com.renda.core.domain.RdRecom;
import com.renda.core.service.IRdRecomService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 代表建议Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-23
 */
@Service
public class RdRecomServiceImpl implements IRdRecomService
{
    @Autowired
    private RdRecomMapper rdRecomMapper;

    @Autowired
    private IRdRecomDeputyService rdRecomDeputyService;

    @Autowired
    private IRdRecomAttachmentService rdRecomAttachmentService;

    /**
     * 查询代表建议
     *
     * @param id 代表建议主键
     * @return 代表建议
     */
    @Override
    public RdRecom selectRdRecomById(Long id)
    {
        return rdRecomMapper.selectRdRecomById(id);
    }

    /**
     * 查询代表建议列表
     *
     * @param rdRecom 代表建议
     * @return 代表建议
     */
    @Override
    public List<RdRecom> selectRdRecomList(RdRecom rdRecom)
    {
        return rdRecomMapper.selectRdRecomList(rdRecom);
    }

    /**
     * 新增代表建议
     *
     * @param rdRecom 代表建议
     * @return 结果
     */
    @Override
    @Transactional
    public int insertRdRecom(RdRecom rdRecom)
    {
        String username = SecurityUtils.getLoginUser().getUsername();
        Date nowDate = DateUtils.getNowDate();
        rdRecom.setCreateBy(username);
        rdRecom.setCreateTime(nowDate);
        rdRecom.setUpdateBy(username);
        rdRecom.setUpdateTime(nowDate);
        int i = rdRecomMapper.insertRdRecom(rdRecom);
        if (i > 0) {
            rdRecom.getDeputyList().forEach(deputy -> {
                deputy.setRecomId(rdRecom.getId());
                rdRecomDeputyService.insertRdRecomDeputy(deputy);
            });
            rdRecom.getAttachmentList().forEach(attachment -> {
                attachment.setRecomId(rdRecom.getId());
                rdRecomAttachmentService.insertRdRecomAttachment(attachment);
            });
            return 1;
        } else {
            throw new RuntimeException("新增失败");
        }
    }

    /**
     * 修改代表建议
     *
     * @param rdRecom 代表建议
     * @return 结果
     */
    @Override
    @Transactional
    public int updateRdRecom(RdRecom rdRecom)
    {
        String username = SecurityUtils.getLoginUser().getUsername();
        Date nowDate = DateUtils.getNowDate();
        rdRecom.setUpdateBy(username);
        rdRecom.setUpdateTime(nowDate);
        int i = rdRecomMapper.updateRdRecom(rdRecom);
        if (i > 0) {
            rdRecomDeputyService.deleteRdRecomDeputyByRecomId(rdRecom.getId());
            rdRecom.getDeputyList().forEach(deputy -> {
                deputy.setRecomId(rdRecom.getId());
                rdRecomDeputyService.insertRdRecomDeputy(deputy);
            });
            rdRecomAttachmentService.deleteRdRecomAttachmentByRecomId(rdRecom.getId());
            rdRecom.getAttachmentList().forEach(attachment -> {
                attachment.setRecomId(rdRecom.getId());
                rdRecomAttachmentService.insertRdRecomAttachment(attachment);
            });
            return 1;
        } else {
            throw new RuntimeException("修改失败");
        }
    }

    /**
     * 批量删除代表建议
     *
     * @param ids 需要删除的代表建议主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteRdRecomByIds(Long[] ids)
    {
        int i = rdRecomMapper.deleteRdRecomByIds(ids);
        if (i > 0) {
            rdRecomDeputyService.deleteRdRecomDeputyByRecomIds(ids);
            rdRecomAttachmentService.deleteRdRecomAttachmentByRecomIds(ids);
            return 1;
        } else {
            throw new RuntimeException("删除失败");
        }
    }

    /**
     * 删除代表建议信息
     *
     * @param id 代表建议主键
     * @return 结果
     */
    @Override
    public int deleteRdRecomById(Long id)
    {
        return rdRecomMapper.deleteRdRecomById(id);
    }

    /***
     * 查询主办协办列表
     * @param keyword 关键字
     * @return 主办协办列表
     */
    @Override
    public List<String> listHost(String keyword) {
        return rdRecomMapper.selectHostList(keyword);
    }

}
