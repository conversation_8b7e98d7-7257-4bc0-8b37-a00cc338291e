import request from '@/utils/request'

// 查询群众管理列表
export function listMass(query) {
  return request({
    url: '/renda/mass/list',
    method: 'get',
    params: query
  })
}

// 查询群众管理详细
export function getMass(id) {
  return request({
    url: '/renda/mass/' + id,
    method: 'get'
  })
}

// 新增群众管理
export function addMass(data) {
  return request({
    url: '/renda/mass',
    method: 'post',
    data: data
  })
}

// 修改群众管理
export function updateMass(data) {
  return request({
    url: '/renda/mass',
    method: 'put',
    data: data
  })
}

// 删除群众管理
export function delMass(id) {
  return request({
    url: '/renda/mass/' + id,
    method: 'delete'
  })
}
