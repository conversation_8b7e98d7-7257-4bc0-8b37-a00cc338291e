package com.renda.core.service.impl;

import java.util.List;
import com.renda.common.utils.DateUtils;
import com.renda.core.domain.vo.MassAdviceInfoVO;
import com.renda.core.domain.vo.MassStatisticsInfoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.renda.core.mapper.RdMassMapper;
import com.renda.core.domain.RdMass;
import com.renda.core.service.IRdMassService;

/**
 * 群众管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-10-30
 */
@Service
public class RdMassServiceImpl implements IRdMassService
{
    @Autowired
    private RdMassMapper rdMassMapper;

    /**
     * 查询群众管理
     *
     * @param id 群众管理主键
     * @return 群众管理
     */
    @Override
    public RdMass selectRdMassById(Long id)
    {
        return rdMassMapper.selectRdMassById(id);
    }

    /**
     * 查询群众管理列表
     *
     * @param rdMass 群众管理
     * @return 群众管理
     */
    @Override
    public List<RdMass> selectRdMassList(RdMass rdMass)
    {
        return rdMassMapper.selectRdMassList(rdMass);
    }

    /**
     * 新增群众管理
     *
     * @param rdMass 群众管理
     * @return 结果
     */
    @Override
    public int insertRdMass(RdMass rdMass)
    {
        rdMass.setCreateTime(DateUtils.getNowDate());
        return rdMassMapper.insertRdMass(rdMass);
    }

    /**
     * 修改群众管理
     *
     * @param rdMass 群众管理
     * @return 结果
     */
    @Override
    public int updateRdMass(RdMass rdMass)
    {
        rdMass.setUpdateTime(DateUtils.getNowDate());
        return rdMassMapper.updateRdMass(rdMass);
    }

    /**
     * 批量删除群众管理
     *
     * @param ids 需要删除的群众管理主键
     * @return 结果
     */
    @Override
    public int deleteRdMassByIds(Long[] ids)
    {
        return rdMassMapper.deleteRdMassByIds(ids);
    }

    /**
     * 删除群众管理信息
     *
     * @param id 群众管理主键
     * @return 结果
     */
    @Override
    public int deleteRdMassById(Long id)
    {
        return rdMassMapper.deleteRdMassById(id);
    }

    /***
     * 获取用户统计信息接口
     * @return
     */
    @Override
    public MassStatisticsInfoVO getMassStatistics(Long userId) {
        return rdMassMapper.getMassStatistics(userId);
    }

    /***
     * 获取用户建议列表接口
     * @return
     */
    @Override
    public List<MassAdviceInfoVO> getMassAdviceList(Long userId) {
        return rdMassMapper.getMassAdviceList(userId);
    }

}
