/*
 Navicat Premium Data Transfer

 Source Server         : localhost
 Source Server Type    : MySQL
 Source Server Version : 50740
 Source Host           : localhost:3306
 Source Schema         : renda

 Target Server Type    : MySQL
 Target Server Version : 50740
 File Encoding         : 65001

 Date: 28/10/2023 09:26:14
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for gen_table
-- ----------------------------
DROP TABLE IF EXISTS `gen_table`;
CREATE TABLE `gen_table`  (
  `table_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '表名称',
  `table_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '表描述',
  `sub_table_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联子表的表名',
  `sub_table_fk_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '子表关联的外键名',
  `class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '实体类名称',
  `tpl_category` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'crud' COMMENT '使用的模板（crud单表操作 tree树表操作）',
  `package_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生成包路径',
  `module_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生成模块名',
  `business_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生成业务名',
  `function_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生成功能名',
  `function_author` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生成功能作者',
  `gen_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '生成代码方式（0zip压缩包 1自定义路径）',
  `gen_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '/' COMMENT '生成路径（不填默认项目路径）',
  `options` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其它生成选项',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`table_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代码生成业务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of gen_table
-- ----------------------------
INSERT INTO `gen_table` VALUES (1, 'rd_deputy', '人大代表表', '', '', 'RdDeputy', 'crud', 'com.renda.core', 'renda', 'deputy', '人大代表管理', 'renda', '0', '/', '{}', 'admin', '2023-10-23 17:03:51', '', '2023-10-23 17:17:16', NULL);

-- ----------------------------
-- Table structure for gen_table_column
-- ----------------------------
DROP TABLE IF EXISTS `gen_table_column`;
CREATE TABLE `gen_table_column`  (
  `column_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_id` bigint(20) NULL DEFAULT NULL COMMENT '归属表编号',
  `column_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '列名称',
  `column_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '列描述',
  `column_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '列类型',
  `java_type` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'JAVA类型',
  `java_field` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'JAVA字段名',
  `is_pk` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否主键（1是）',
  `is_increment` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否自增（1是）',
  `is_required` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否必填（1是）',
  `is_insert` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否为插入字段（1是）',
  `is_edit` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否编辑字段（1是）',
  `is_list` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否列表字段（1是）',
  `is_query` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否查询字段（1是）',
  `query_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'EQ' COMMENT '查询方式（等于、不等于、大于、小于、范围）',
  `html_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）',
  `dict_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `sort` int(11) NULL DEFAULT NULL COMMENT '排序',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`column_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代码生成业务表字段' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of gen_table_column
-- ----------------------------
INSERT INTO `gen_table_column` VALUES (1, 1, 'id', NULL, 'bigint(20)', 'Long', 'id', '1', '0', NULL, '1', NULL, NULL, NULL, 'EQ', 'input', '', 1, 'admin', '2023-10-23 17:03:51', '', '2023-10-23 17:17:16');
INSERT INTO `gen_table_column` VALUES (2, 1, 'type', '代表类型：0-代表；1-委员；2-管理；', 'int(1)', 'Integer', 'type', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'select', '', 2, 'admin', '2023-10-23 17:03:51', '', '2023-10-23 17:17:16');
INSERT INTO `gen_table_column` VALUES (3, 1, 'name', '姓名', 'varchar(64)', 'String', 'name', '0', '0', '1', '1', '1', '1', '1', 'LIKE', 'input', '', 3, 'admin', '2023-10-23 17:03:51', '', '2023-10-23 17:17:16');
INSERT INTO `gen_table_column` VALUES (4, 1, 'nation', '民族', 'varchar(50)', 'String', 'nation', '0', '0', NULL, '1', '1', '1', '1', 'EQ', 'input', 'yw_mz', 4, 'admin', '2023-10-23 17:03:51', '', '2023-10-23 17:17:16');
INSERT INTO `gen_table_column` VALUES (5, 1, 'duty', '职务', 'varchar(50)', 'String', 'duty', '0', '0', NULL, '1', '1', '1', '1', 'EQ', 'input', '', 5, 'admin', '2023-10-23 17:03:51', '', '2023-10-23 17:17:16');
INSERT INTO `gen_table_column` VALUES (6, 1, 'birthday', '出生日期', 'date', 'Date', 'birthday', '0', '0', NULL, '1', '1', '1', '0', 'EQ', 'datetime', '', 6, 'admin', '2023-10-23 17:03:51', '', '2023-10-23 17:17:16');
INSERT INTO `gen_table_column` VALUES (7, 1, 'phone', '电话', 'varchar(11)', 'String', 'phone', '0', '0', '1', '1', '1', '1', '1', 'EQ', 'input', '', 7, 'admin', '2023-10-23 17:03:51', '', '2023-10-23 17:17:16');
INSERT INTO `gen_table_column` VALUES (8, 1, 'resume', '代表履历', 'text', 'String', 'resume', '0', '0', NULL, '1', '1', '0', '0', 'EQ', 'textarea', '', 8, 'admin', '2023-10-23 17:03:51', '', '2023-10-23 17:17:16');
INSERT INTO `gen_table_column` VALUES (9, 1, 'avatar', '头像', 'varchar(500)', 'String', 'avatar', '0', '0', NULL, '1', '1', '1', '0', 'EQ', 'textarea', '', 9, 'admin', '2023-10-23 17:03:51', '', '2023-10-23 17:17:16');
INSERT INTO `gen_table_column` VALUES (10, 1, 'openid', 'openid', 'varchar(64)', 'String', 'openid', '0', '0', NULL, '1', '1', '0', '0', 'EQ', 'input', '', 10, 'admin', '2023-10-23 17:03:51', '', '2023-10-23 17:17:16');
INSERT INTO `gen_table_column` VALUES (11, 1, 'create_by', '创建者', 'varchar(64)', 'String', 'createBy', '0', '0', NULL, '1', NULL, '0', NULL, 'EQ', 'input', '', 11, 'admin', '2023-10-23 17:03:51', '', '2023-10-23 17:17:16');
INSERT INTO `gen_table_column` VALUES (12, 1, 'create_time', '创建时间', 'datetime', 'Date', 'createTime', '0', '0', NULL, '1', NULL, '0', NULL, 'EQ', 'datetime', '', 12, 'admin', '2023-10-23 17:03:51', '', '2023-10-23 17:17:16');
INSERT INTO `gen_table_column` VALUES (13, 1, 'update_by', '更新者', 'varchar(64)', 'String', 'updateBy', '0', '0', NULL, '1', '1', '1', NULL, 'EQ', 'input', '', 13, 'admin', '2023-10-23 17:03:51', '', '2023-10-23 17:17:16');
INSERT INTO `gen_table_column` VALUES (14, 1, 'update_time', '更新时间', 'datetime', 'Date', 'updateTime', '0', '0', NULL, '1', '1', '1', NULL, 'EQ', 'datetime', '', 14, 'admin', '2023-10-23 17:03:51', '', '2023-10-23 17:17:16');

-- ----------------------------
-- Table structure for qrtz_blob_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_blob_triggers`;
CREATE TABLE `qrtz_blob_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `blob_data` blob NULL COMMENT '存放持久化Trigger对象',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_blob_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'Blob类型的触发器表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qrtz_calendars
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_calendars`;
CREATE TABLE `qrtz_calendars`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `calendar_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '日历名称',
  `calendar` blob NOT NULL COMMENT '存放持久化calendar对象',
  PRIMARY KEY (`sched_name`, `calendar_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '日历信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qrtz_cron_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_cron_triggers`;
CREATE TABLE `qrtz_cron_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `cron_expression` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'cron表达式',
  `time_zone_id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '时区',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_cron_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'Cron类型的触发器表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qrtz_fired_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_fired_triggers`;
CREATE TABLE `qrtz_fired_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `entry_id` varchar(95) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度器实例id',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `instance_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度器实例名',
  `fired_time` bigint(13) NOT NULL COMMENT '触发的时间',
  `sched_time` bigint(13) NOT NULL COMMENT '定时器制定的时间',
  `priority` int(11) NOT NULL COMMENT '优先级',
  `state` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '状态',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务名称',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务组名',
  `is_nonconcurrent` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否并发',
  `requests_recovery` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否接受恢复执行',
  PRIMARY KEY (`sched_name`, `entry_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '已触发的触发器表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qrtz_job_details
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_job_details`;
CREATE TABLE `qrtz_job_details`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务组名',
  `description` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '相关介绍',
  `job_class_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行任务类名称',
  `is_durable` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否持久化',
  `is_nonconcurrent` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否并发',
  `is_update_data` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否更新数据',
  `requests_recovery` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否接受恢复执行',
  `job_data` blob NULL COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '任务详细信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qrtz_locks
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_locks`;
CREATE TABLE `qrtz_locks`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `lock_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '悲观锁名称',
  PRIMARY KEY (`sched_name`, `lock_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '存储的悲观锁信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qrtz_paused_trigger_grps
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_paused_trigger_grps`;
CREATE TABLE `qrtz_paused_trigger_grps`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  PRIMARY KEY (`sched_name`, `trigger_group`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '暂停的触发器表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qrtz_scheduler_state
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_scheduler_state`;
CREATE TABLE `qrtz_scheduler_state`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `instance_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '实例名称',
  `last_checkin_time` bigint(13) NOT NULL COMMENT '上次检查时间',
  `checkin_interval` bigint(13) NOT NULL COMMENT '检查间隔时间',
  PRIMARY KEY (`sched_name`, `instance_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '调度器状态表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qrtz_simple_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_simple_triggers`;
CREATE TABLE `qrtz_simple_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `repeat_count` bigint(7) NOT NULL COMMENT '重复的次数统计',
  `repeat_interval` bigint(12) NOT NULL COMMENT '重复的间隔时间',
  `times_triggered` bigint(10) NOT NULL COMMENT '已经触发的次数',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_simple_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '简单触发器的信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qrtz_simprop_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_simprop_triggers`;
CREATE TABLE `qrtz_simprop_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `str_prop_1` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'String类型的trigger的第一个参数',
  `str_prop_2` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'String类型的trigger的第二个参数',
  `str_prop_3` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'String类型的trigger的第三个参数',
  `int_prop_1` int(11) NULL DEFAULT NULL COMMENT 'int类型的trigger的第一个参数',
  `int_prop_2` int(11) NULL DEFAULT NULL COMMENT 'int类型的trigger的第二个参数',
  `long_prop_1` bigint(20) NULL DEFAULT NULL COMMENT 'long类型的trigger的第一个参数',
  `long_prop_2` bigint(20) NULL DEFAULT NULL COMMENT 'long类型的trigger的第二个参数',
  `dec_prop_1` decimal(13, 4) NULL DEFAULT NULL COMMENT 'decimal类型的trigger的第一个参数',
  `dec_prop_2` decimal(13, 4) NULL DEFAULT NULL COMMENT 'decimal类型的trigger的第二个参数',
  `bool_prop_1` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'Boolean类型的trigger的第一个参数',
  `bool_prop_2` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'Boolean类型的trigger的第二个参数',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_simprop_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '同步机制的行锁表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for qrtz_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_triggers`;
CREATE TABLE `qrtz_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发器的名字',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发器所属组的名字',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_job_details表job_name的外键',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_job_details表job_group的外键',
  `description` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '相关介绍',
  `next_fire_time` bigint(13) NULL DEFAULT NULL COMMENT '上一次触发时间（毫秒）',
  `prev_fire_time` bigint(13) NULL DEFAULT NULL COMMENT '下一次触发时间（默认为-1表示不触发）',
  `priority` int(11) NULL DEFAULT NULL COMMENT '优先级',
  `trigger_state` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发器状态',
  `trigger_type` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发器的类型',
  `start_time` bigint(13) NOT NULL COMMENT '开始时间',
  `end_time` bigint(13) NULL DEFAULT NULL COMMENT '结束时间',
  `calendar_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '日程表名称',
  `misfire_instr` smallint(2) NULL DEFAULT NULL COMMENT '补偿执行的策略',
  `job_data` blob NULL COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  INDEX `sched_name`(`sched_name`, `job_name`, `job_group`) USING BTREE,
  CONSTRAINT `qrtz_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `job_name`, `job_group`) REFERENCES `qrtz_job_details` (`sched_name`, `job_name`, `job_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '触发器详细信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rd_advice
-- ----------------------------
DROP TABLE IF EXISTS `rd_advice`;
CREATE TABLE `rd_advice`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '建议内容',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '电话',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '群众建议表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of rd_advice
-- ----------------------------
INSERT INTO `rd_advice` VALUES (1, '标题', '内容', '张三', '13312345678');

-- ----------------------------
-- Table structure for rd_advice_attachment
-- ----------------------------
DROP TABLE IF EXISTS `rd_advice_attachment`;
CREATE TABLE `rd_advice_attachment`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `advice_id` bigint(20) NOT NULL COMMENT '建议ID',
  `file_type` int(1) NOT NULL COMMENT '文件类型：1-图片视频；2-文件',
  `file_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件URL',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '群众建议附件表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rd_deputy
-- ----------------------------
DROP TABLE IF EXISTS `rd_deputy`;
CREATE TABLE `rd_deputy`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `dept_id` bigint(20) NOT NULL DEFAULT 100 COMMENT '所属部门',
  `type` int(1) NOT NULL DEFAULT 0 COMMENT '代表类型：0-代表；1-委员；2-管理；',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '姓名',
  `nation` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '民族',
  `duty` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '职务',
  `birthday` date NULL DEFAULT NULL COMMENT '出生日期',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '电话',
  `resume` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '代表履历',
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '头像',
  `openid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'openid',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '人大代表表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of rd_deputy
-- ----------------------------
INSERT INTO `rd_deputy` VALUES (1, 100, 0, '刘敏', '01', '省水利厅副厅长、党组成员', '1966-08-08', '13779369898', '1989年07月至1992年06月，在江西萍乡钢铁厂技校任教师、助理工程师；\n\n1992年06月至1997年03月，在广州钢铁厂劳动人事处任干部、综合科科长、经济师；\n\n1997年03月至2000年12月，在广东省委组织部地方干部处任副主任科员、主任科员；2000年12月至2002年10月，广东省委组织部干部二处主任科员；\n\n2002年10月至2008年05月，广东省委组织部干部二处副处长（其间：2007年9月至今在中山大学岭南学院世界经济专业进行博士研究生学习）；\n\n2008年05月至2008年05月，广东省委组织部正处职干部；\n\n2008年05月至2009年09月，广东省水利厅人事劳动教育处（离退休人员管理处）处长；\n\n2009年09月至今，任广东省水利厅副厅长、党组成员。（副厅级）', 'https://lio-1256187893.cos.ap-chengdu.myqcloud.com/csgrouping/guild/logo/rtf.jpg', NULL, NULL, '2023-10-24 11:19:10', NULL, '2023-10-24 11:34:22');
INSERT INTO `rd_deputy` VALUES (2, 110, 1, '张科', '01', '省委统战部副部长', '1968-02-01', '18699178090', '男，汉族，重庆人，1968年2月生，学历在职研究生，公共管理硕士，1989年6月参加工作，1987年1月加入中国共产党。\n\n现任广东省委统战部副部长，省民族宗教事务委员会党组书记、主任，省政协民族宗教委员会副主任。', NULL, NULL, NULL, '2023-10-24 11:32:22', NULL, '2023-10-24 11:34:31');
INSERT INTO `rd_deputy` VALUES (3, 111, 2, '于建', '01', '书记', '1978-09-28', '13888888888', '未填写', NULL, NULL, NULL, '2023-10-24 11:35:32', NULL, NULL);
INSERT INTO `rd_deputy` VALUES (4, 100, 0, 'a', NULL, NULL, NULL, '1', NULL, NULL, NULL, NULL, '2023-10-27 10:43:54', NULL, NULL);
INSERT INTO `rd_deputy` VALUES (5, 100, 2, 'd', NULL, NULL, NULL, '4', NULL, NULL, NULL, NULL, '2023-10-27 12:09:45', NULL, NULL);
INSERT INTO `rd_deputy` VALUES (6, 100, 0, 'e', NULL, NULL, NULL, '5', NULL, '/profile/avatar/2023/10/26/blob_20231026125200A004.png', NULL, NULL, '2023-10-27 12:12:26', NULL, NULL);
INSERT INTO `rd_deputy` VALUES (7, 100, 1, 'f', NULL, NULL, NULL, '6', NULL, '/profile/upload/20231027/df44f286140340cbb9863bd696054093.png', NULL, NULL, '2023-10-27 12:19:58', NULL, NULL);

-- ----------------------------
-- Table structure for rd_feedback
-- ----------------------------
DROP TABLE IF EXISTS `rd_feedback`;
CREATE TABLE `rd_feedback`  (
  `id` bigint(20) NOT NULL,
  `type` int(1) NOT NULL COMMENT '用户类型：0-群众；1-代表',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '姓名',
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '头像',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '反馈时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '群众建议反馈表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rd_mass
-- ----------------------------
DROP TABLE IF EXISTS `rd_mass`;
CREATE TABLE `rd_mass`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '姓名',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '电话',
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '头像',
  `openid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'openid',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '群众表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`  (
  `config_id` int(5) NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数键值',
  `config_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '参数配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_config
-- ----------------------------
INSERT INTO `sys_config` VALUES (1, '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 'admin', '2023-10-16 10:30:55', '', NULL, '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
INSERT INTO `sys_config` VALUES (2, '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 'admin', '2023-10-16 10:30:55', '', NULL, '初始化密码 123456');
INSERT INTO `sys_config` VALUES (3, '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 'admin', '2023-10-16 10:30:55', '', NULL, '深色主题theme-dark，浅色主题theme-light');
INSERT INTO `sys_config` VALUES (4, '账号自助-验证码开关', 'sys.account.captchaEnabled', 'true', 'Y', 'admin', '2023-10-16 10:30:55', '', NULL, '是否开启验证码功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (5, '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 'admin', '2023-10-16 10:30:55', '', NULL, '是否开启注册用户功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (6, '用户登录-黑名单列表', 'sys.login.blackIPList', '', 'Y', 'admin', '2023-10-16 10:30:55', '', NULL, '设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）');

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`  (
  `dept_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint(20) NULL DEFAULT 0 COMMENT '父部门id',
  `ancestors` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '部门名称',
  `order_num` int(4) NULL DEFAULT 0 COMMENT '显示顺序',
  `leader` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 113 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '部门表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
INSERT INTO `sys_dept` VALUES (100, 0, '0', '哈密市人大', 0, NULL, NULL, NULL, '0', '0', 'admin', '2023-10-16 10:30:55', 'admin', '2023-10-20 18:02:59');
INSERT INTO `sys_dept` VALUES (110, 100, '0,100', '伊州区人大', 0, NULL, NULL, NULL, '0', '0', 'admin', '2023-10-20 18:07:49', '', NULL);
INSERT INTO `sys_dept` VALUES (111, 100, '0,100', '巴里坤县人大', 1, NULL, NULL, NULL, '0', '0', 'admin', '2023-10-20 18:08:02', 'admin', '2023-10-20 18:08:22');
INSERT INTO `sys_dept` VALUES (112, 100, '0,100', '伊吾县人大', 2, NULL, NULL, NULL, '0', '0', 'admin', '2023-10-20 18:08:15', '', NULL);

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `dict_code` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int(4) NULL DEFAULT 0 COMMENT '字典排序',
  `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 89 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO `sys_dict_data` VALUES (1, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '性别男');
INSERT INTO `sys_dict_data` VALUES (2, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '性别女');
INSERT INTO `sys_dict_data` VALUES (3, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '性别未知');
INSERT INTO `sys_dict_data` VALUES (4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '显示菜单');
INSERT INTO `sys_dict_data` VALUES (5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '隐藏菜单');
INSERT INTO `sys_dict_data` VALUES (6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (8, 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (9, 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (10, 1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '默认分组');
INSERT INTO `sys_dict_data` VALUES (11, 2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '系统分组');
INSERT INTO `sys_dict_data` VALUES (12, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '系统默认是');
INSERT INTO `sys_dict_data` VALUES (13, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '系统默认否');
INSERT INTO `sys_dict_data` VALUES (14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '通知');
INSERT INTO `sys_dict_data` VALUES (15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '公告');
INSERT INTO `sys_dict_data` VALUES (16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '关闭状态');
INSERT INTO `sys_dict_data` VALUES (18, 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '其他操作');
INSERT INTO `sys_dict_data` VALUES (19, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '新增操作');
INSERT INTO `sys_dict_data` VALUES (20, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '修改操作');
INSERT INTO `sys_dict_data` VALUES (21, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '删除操作');
INSERT INTO `sys_dict_data` VALUES (22, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '授权操作');
INSERT INTO `sys_dict_data` VALUES (23, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '导出操作');
INSERT INTO `sys_dict_data` VALUES (24, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '导入操作');
INSERT INTO `sys_dict_data` VALUES (25, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '强退操作');
INSERT INTO `sys_dict_data` VALUES (26, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '生成操作');
INSERT INTO `sys_dict_data` VALUES (27, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '清空操作');
INSERT INTO `sys_dict_data` VALUES (28, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (29, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (30, 1, '汉族', '01', 'yw_mz', '', '', 'Y', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (31, 2, '蒙古族', '02', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (32, 3, '回族', '03', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (33, 4, '藏族', '04', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (34, 5, '维吾尔族', '05', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (35, 6, '苗族', '06', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (36, 7, '彝族', '07', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (37, 8, '壮族', '08', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (38, 9, '布依族', '09', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (39, 10, '朝鲜族', '10', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (40, 11, '满族', '11', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (41, 12, '侗族', '12', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (42, 13, '瑶族', '13', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (43, 14, '白族', '14', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (44, 15, '土家族', '15', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (45, 16, '哈尼族', '16', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (46, 17, '哈萨克族', '17', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (47, 18, '傣族', '18', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (48, 19, '黎族', '19', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (49, 20, '僳僳族', '20', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (50, 21, '佤族', '21', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (51, 22, '畲族', '22', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (52, 23, '高山族', '23', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (53, 24, '拉祜族', '24', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (54, 25, '水族', '25', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (55, 26, '东乡族', '26', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (56, 27, '纳西族', '27', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (57, 28, '景颇族', '28', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (58, 29, '柯尔克孜族', '29', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (59, 30, '土族', '30', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (60, 31, '达斡尔族', '31', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (61, 32, '仫佬族', '32', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (62, 33, '羌族', '33', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (63, 34, '布朗族', '34', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (64, 35, '撒拉族', '35', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (65, 36, '毛南族', '36', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (66, 37, '仡佬族', '37', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (67, 38, '锡伯族', '38', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (68, 39, '阿昌族', '39', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (69, 40, '普米族', '40', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (70, 41, '塔吉克族', '41', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (71, 42, '怒族', '42', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (72, 43, '乌孜别克族', '43', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (73, 44, '俄罗斯族', '44', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (74, 45, '鄂温克族', '45', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (75, 46, '德昂族', '46', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (76, 47, '保安族', '47', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (77, 48, '裕固族', '48', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (78, 49, '京族', '49', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (79, 50, '塔塔尔族', '50', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (80, 51, '独龙族', '51', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (81, 52, '鄂伦春族', '52', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (82, 53, '赫哲族', '53', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (83, 54, '门巴族', '54', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (84, 55, '珞巴族', '55', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (85, 56, '基诺族', '56', 'yw_mz', '', '', 'N', '0', 'admin', '2023-10-23 09:51:38', '', NULL, '');
INSERT INTO `sys_dict_data` VALUES (86, 0, '人大代表', '0', 'rd_deputy_type', NULL, 'default', 'N', '0', 'admin', '2023-10-24 11:22:10', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (87, 1, '人大委员', '1', 'rd_deputy_type', NULL, 'default', 'N', '0', 'admin', '2023-10-24 11:22:22', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (88, 2, '管理', '2', 'rd_deputy_type', NULL, 'default', 'N', '0', 'admin', '2023-10-24 11:22:39', '', NULL, NULL);

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `dict_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE INDEX `dict_type`(`dict_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO `sys_dict_type` VALUES (1, '用户性别', 'sys_user_sex', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '用户性别列表');
INSERT INTO `sys_dict_type` VALUES (2, '菜单状态', 'sys_show_hide', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '菜单状态列表');
INSERT INTO `sys_dict_type` VALUES (3, '系统开关', 'sys_normal_disable', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '系统开关列表');
INSERT INTO `sys_dict_type` VALUES (4, '任务状态', 'sys_job_status', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '任务状态列表');
INSERT INTO `sys_dict_type` VALUES (5, '任务分组', 'sys_job_group', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '任务分组列表');
INSERT INTO `sys_dict_type` VALUES (6, '系统是否', 'sys_yes_no', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '系统是否列表');
INSERT INTO `sys_dict_type` VALUES (7, '通知类型', 'sys_notice_type', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '通知类型列表');
INSERT INTO `sys_dict_type` VALUES (8, '通知状态', 'sys_notice_status', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '通知状态列表');
INSERT INTO `sys_dict_type` VALUES (9, '操作类型', 'sys_oper_type', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '操作类型列表');
INSERT INTO `sys_dict_type` VALUES (10, '系统状态', 'sys_common_status', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '登录状态列表');
INSERT INTO `sys_dict_type` VALUES (11, '民族', 'yw_mz', '0', 'admin', '2023-10-23 09:38:48', '', NULL, '民族（业务）');
INSERT INTO `sys_dict_type` VALUES (12, '代表类型', 'rd_deputy_type', '0', 'admin', '2023-10-24 11:21:32', 'admin', '2023-10-24 11:27:12', NULL);

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job`  (
  `job_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调用目标字符串',
  `cron_expression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'cron执行表达式',
  `misfire_policy` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  `concurrent` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1暂停）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`job_id`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '定时任务调度表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_job
-- ----------------------------
INSERT INTO `sys_job` VALUES (1, '系统默认（无参）', 'DEFAULT', 'ryTask.ryNoParams', '0/10 * * * * ?', '3', '1', '1', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_job` VALUES (2, '系统默认（有参）', 'DEFAULT', 'ryTask.ryParams(\'ry\')', '0/15 * * * * ?', '3', '1', '1', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_job` VALUES (3, '系统默认（多参）', 'DEFAULT', 'ryTask.ryMultipleParams(\'ry\', true, 2000L, 316.50D, 100)', '0/20 * * * * ?', '3', '1', '1', 'admin', '2023-10-16 10:30:55', '', NULL, '');

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log`  (
  `job_log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调用目标字符串',
  `job_message` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '日志信息',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `exception_info` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '异常信息',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`job_log_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '定时任务调度日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_logininfor
-- ----------------------------
DROP TABLE IF EXISTS `sys_logininfor`;
CREATE TABLE `sys_logininfor`  (
  `info_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户账号',
  `ipaddr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '登录IP地址',
  `login_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '登录地点',
  `browser` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作系统',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '提示消息',
  `login_time` datetime(0) NULL DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`info_id`) USING BTREE,
  INDEX `idx_sys_logininfor_s`(`status`) USING BTREE,
  INDEX `idx_sys_logininfor_lt`(`login_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 143 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统访问记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_logininfor
-- ----------------------------
INSERT INTO `sys_logininfor` VALUES (100, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-16 13:22:08');
INSERT INTO `sys_logininfor` VALUES (101, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-20 13:29:57');
INSERT INTO `sys_logininfor` VALUES (102, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '退出成功', '2023-10-20 13:33:41');
INSERT INTO `sys_logininfor` VALUES (103, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-20 13:33:56');
INSERT INTO `sys_logininfor` VALUES (104, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-20 17:55:29');
INSERT INTO `sys_logininfor` VALUES (105, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '1', '验证码已失效', '2023-10-20 20:20:54');
INSERT INTO `sys_logininfor` VALUES (106, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-20 20:20:57');
INSERT INTO `sys_logininfor` VALUES (107, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '1', '验证码已失效', '2023-10-22 17:48:07');
INSERT INTO `sys_logininfor` VALUES (108, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-22 17:48:11');
INSERT INTO `sys_logininfor` VALUES (109, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-23 09:37:33');
INSERT INTO `sys_logininfor` VALUES (110, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '1', '验证码已失效', '2023-10-23 17:02:04');
INSERT INTO `sys_logininfor` VALUES (111, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-23 17:02:07');
INSERT INTO `sys_logininfor` VALUES (112, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '退出成功', '2023-10-23 17:52:16');
INSERT INTO `sys_logininfor` VALUES (113, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-23 17:52:18');
INSERT INTO `sys_logininfor` VALUES (114, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-23 18:37:28');
INSERT INTO `sys_logininfor` VALUES (115, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-24 11:03:58');
INSERT INTO `sys_logininfor` VALUES (116, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-24 13:08:56');
INSERT INTO `sys_logininfor` VALUES (117, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '退出成功', '2023-10-24 13:21:50');
INSERT INTO `sys_logininfor` VALUES (118, 'test', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '1', '用户不存在/密码错误', '2023-10-24 13:21:58');
INSERT INTO `sys_logininfor` VALUES (119, 'test', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '1', '密码输入错误1次', '2023-10-24 13:21:58');
INSERT INTO `sys_logininfor` VALUES (120, 'test', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '1', '验证码错误', '2023-10-24 13:22:03');
INSERT INTO `sys_logininfor` VALUES (121, 'blktest', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-24 13:22:10');
INSERT INTO `sys_logininfor` VALUES (122, 'blktest', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-24 17:16:33');
INSERT INTO `sys_logininfor` VALUES (123, 'blktest', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '退出成功', '2023-10-24 17:16:48');
INSERT INTO `sys_logininfor` VALUES (124, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-24 17:16:57');
INSERT INTO `sys_logininfor` VALUES (125, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '退出成功', '2023-10-24 17:17:15');
INSERT INTO `sys_logininfor` VALUES (126, 'test', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-24 17:17:23');
INSERT INTO `sys_logininfor` VALUES (127, 'test', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '退出成功', '2023-10-24 17:17:39');
INSERT INTO `sys_logininfor` VALUES (128, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-24 17:17:42');
INSERT INTO `sys_logininfor` VALUES (129, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '1', '验证码错误', '2023-10-24 17:51:40');
INSERT INTO `sys_logininfor` VALUES (130, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-24 17:51:44');
INSERT INTO `sys_logininfor` VALUES (131, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-24 18:35:53');
INSERT INTO `sys_logininfor` VALUES (132, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-25 10:01:15');
INSERT INTO `sys_logininfor` VALUES (133, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-25 18:04:09');
INSERT INTO `sys_logininfor` VALUES (134, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-26 10:56:25');
INSERT INTO `sys_logininfor` VALUES (135, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '退出成功', '2023-10-26 11:04:27');
INSERT INTO `sys_logininfor` VALUES (136, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-26 11:04:30');
INSERT INTO `sys_logininfor` VALUES (137, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-26 11:56:46');
INSERT INTO `sys_logininfor` VALUES (138, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-26 17:01:03');
INSERT INTO `sys_logininfor` VALUES (139, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-26 17:58:28');
INSERT INTO `sys_logininfor` VALUES (140, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-27 10:12:05');
INSERT INTO `sys_logininfor` VALUES (141, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-27 12:06:36');
INSERT INTO `sys_logininfor` VALUES (142, 'admin', '127.0.0.1', '内网IP', 'Chrome 11', 'Windows 10', '0', '登录成功', '2023-10-27 13:01:01');

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `menu_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint(20) NULL DEFAULT 0 COMMENT '父菜单ID',
  `order_num` int(4) NULL DEFAULT 0 COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '路由参数',
  `is_frame` int(1) NULL DEFAULT 1 COMMENT '是否为外链（0是 1否）',
  `is_cache` int(1) NULL DEFAULT 0 COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1067 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, '系统管理', 0, 1, 'system', NULL, '', 1, 0, 'M', '0', '0', '', 'system', 'admin', '2023-10-16 10:30:55', '', NULL, '系统管理目录');
INSERT INTO `sys_menu` VALUES (2, '系统监控', 0, 2, 'monitor', NULL, '', 1, 0, 'M', '0', '0', '', 'monitor', 'admin', '2023-10-16 10:30:55', '', NULL, '系统监控目录');
INSERT INTO `sys_menu` VALUES (3, '系统工具', 0, 3, 'tool', NULL, '', 1, 0, 'M', '0', '0', '', 'tool', 'admin', '2023-10-16 10:30:55', '', NULL, '系统工具目录');
INSERT INTO `sys_menu` VALUES (100, '用户管理', 1, 1, 'user', 'system/user/index', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'admin', '2023-10-16 10:30:55', '', NULL, '用户管理菜单');
INSERT INTO `sys_menu` VALUES (101, '角色管理', 1, 2, 'role', 'system/role/index', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'admin', '2023-10-16 10:30:55', '', NULL, '角色管理菜单');
INSERT INTO `sys_menu` VALUES (102, '菜单管理', 1, 3, 'menu', 'system/menu/index', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', '2023-10-16 10:30:55', '', NULL, '菜单管理菜单');
INSERT INTO `sys_menu` VALUES (103, '部门管理', 1, 4, 'dept', 'system/dept/index', '', 1, 0, 'C', '0', '0', 'system:dept:list', 'tree', 'admin', '2023-10-16 10:30:55', '', NULL, '部门管理菜单');
INSERT INTO `sys_menu` VALUES (104, '岗位管理', 1, 5, 'post', 'system/post/index', '', 1, 0, 'C', '0', '0', 'system:post:list', 'post', 'admin', '2023-10-16 10:30:55', '', NULL, '岗位管理菜单');
INSERT INTO `sys_menu` VALUES (105, '字典管理', 1, 6, 'dict', 'system/dict/index', '', 1, 0, 'C', '0', '0', 'system:dict:list', 'dict', 'admin', '2023-10-16 10:30:55', '', NULL, '字典管理菜单');
INSERT INTO `sys_menu` VALUES (106, '参数设置', 1, 7, 'config', 'system/config/index', '', 1, 0, 'C', '0', '0', 'system:config:list', 'edit', 'admin', '2023-10-16 10:30:55', '', NULL, '参数设置菜单');
INSERT INTO `sys_menu` VALUES (107, '通知公告', 1, 8, 'notice', 'system/notice/index', '', 1, 0, 'C', '0', '0', 'system:notice:list', 'message', 'admin', '2023-10-16 10:30:55', '', NULL, '通知公告菜单');
INSERT INTO `sys_menu` VALUES (108, '日志管理', 1, 9, 'log', '', '', 1, 0, 'M', '0', '0', '', 'log', 'admin', '2023-10-16 10:30:55', '', NULL, '日志管理菜单');
INSERT INTO `sys_menu` VALUES (109, '在线用户', 2, 1, 'online', 'monitor/online/index', '', 1, 0, 'C', '0', '0', 'monitor:online:list', 'online', 'admin', '2023-10-16 10:30:55', '', NULL, '在线用户菜单');
INSERT INTO `sys_menu` VALUES (110, '定时任务', 2, 2, 'job', 'monitor/job/index', '', 1, 0, 'C', '0', '0', 'monitor:job:list', 'job', 'admin', '2023-10-16 10:30:55', '', NULL, '定时任务菜单');
INSERT INTO `sys_menu` VALUES (111, '数据监控', 2, 3, 'druid', 'monitor/druid/index', '', 1, 0, 'C', '0', '0', 'monitor:druid:list', 'druid', 'admin', '2023-10-16 10:30:55', '', NULL, '数据监控菜单');
INSERT INTO `sys_menu` VALUES (112, '服务监控', 2, 4, 'server', 'monitor/server/index', '', 1, 0, 'C', '0', '0', 'monitor:server:list', 'server', 'admin', '2023-10-16 10:30:55', '', NULL, '服务监控菜单');
INSERT INTO `sys_menu` VALUES (113, '缓存监控', 2, 5, 'cache', 'monitor/cache/index', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis', 'admin', '2023-10-16 10:30:55', '', NULL, '缓存监控菜单');
INSERT INTO `sys_menu` VALUES (114, '缓存列表', 2, 6, 'cacheList', 'monitor/cache/list', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis-list', 'admin', '2023-10-16 10:30:55', '', NULL, '缓存列表菜单');
INSERT INTO `sys_menu` VALUES (115, '表单构建', 3, 1, 'build', 'tool/build/index', '', 1, 0, 'C', '0', '0', 'tool:build:list', 'build', 'admin', '2023-10-16 10:30:55', '', NULL, '表单构建菜单');
INSERT INTO `sys_menu` VALUES (116, '代码生成', 3, 2, 'gen', 'tool/gen/index', '', 1, 0, 'C', '0', '0', 'tool:gen:list', 'code', 'admin', '2023-10-16 10:30:55', '', NULL, '代码生成菜单');
INSERT INTO `sys_menu` VALUES (117, '系统接口', 3, 3, 'swagger', 'tool/swagger/index', '', 1, 0, 'C', '0', '0', 'tool:swagger:list', 'swagger', 'admin', '2023-10-16 10:30:55', '', NULL, '系统接口菜单');
INSERT INTO `sys_menu` VALUES (500, '操作日志', 108, 1, 'operlog', 'monitor/operlog/index', '', 1, 0, 'C', '0', '0', 'monitor:operlog:list', 'form', 'admin', '2023-10-16 10:30:55', '', NULL, '操作日志菜单');
INSERT INTO `sys_menu` VALUES (501, '登录日志', 108, 2, 'logininfor', 'monitor/logininfor/index', '', 1, 0, 'C', '0', '0', 'monitor:logininfor:list', 'logininfor', 'admin', '2023-10-16 10:30:55', '', NULL, '登录日志菜单');
INSERT INTO `sys_menu` VALUES (1000, '用户查询', 100, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:user:query', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1001, '用户新增', 100, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:user:add', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1002, '用户修改', 100, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:user:edit', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1003, '用户删除', 100, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:user:remove', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1004, '用户导出', 100, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:user:export', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1005, '用户导入', 100, 6, '', '', '', 1, 0, 'F', '0', '0', 'system:user:import', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1006, '重置密码', 100, 7, '', '', '', 1, 0, 'F', '0', '0', 'system:user:resetPwd', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1007, '角色查询', 101, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:role:query', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1008, '角色新增', 101, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:role:add', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1009, '角色修改', 101, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:role:edit', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1010, '角色删除', 101, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:role:remove', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1011, '角色导出', 101, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:role:export', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1012, '菜单查询', 102, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:menu:query', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1013, '菜单新增', 102, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:menu:add', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1014, '菜单修改', 102, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:menu:edit', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1015, '菜单删除', 102, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:menu:remove', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1016, '部门查询', 103, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:dept:query', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1017, '部门新增', 103, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:dept:add', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1018, '部门修改', 103, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:dept:edit', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1019, '部门删除', 103, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:dept:remove', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1020, '岗位查询', 104, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:post:query', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1021, '岗位新增', 104, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:post:add', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1022, '岗位修改', 104, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:post:edit', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1023, '岗位删除', 104, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:post:remove', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1024, '岗位导出', 104, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:post:export', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1025, '字典查询', 105, 1, '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:query', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1026, '字典新增', 105, 2, '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:add', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1027, '字典修改', 105, 3, '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:edit', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1028, '字典删除', 105, 4, '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:remove', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1029, '字典导出', 105, 5, '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:export', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1030, '参数查询', 106, 1, '#', '', '', 1, 0, 'F', '0', '0', 'system:config:query', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1031, '参数新增', 106, 2, '#', '', '', 1, 0, 'F', '0', '0', 'system:config:add', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1032, '参数修改', 106, 3, '#', '', '', 1, 0, 'F', '0', '0', 'system:config:edit', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1033, '参数删除', 106, 4, '#', '', '', 1, 0, 'F', '0', '0', 'system:config:remove', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1034, '参数导出', 106, 5, '#', '', '', 1, 0, 'F', '0', '0', 'system:config:export', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1035, '公告查询', 107, 1, '#', '', '', 1, 0, 'F', '0', '0', 'system:notice:query', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1036, '公告新增', 107, 2, '#', '', '', 1, 0, 'F', '0', '0', 'system:notice:add', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1037, '公告修改', 107, 3, '#', '', '', 1, 0, 'F', '0', '0', 'system:notice:edit', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1038, '公告删除', 107, 4, '#', '', '', 1, 0, 'F', '0', '0', 'system:notice:remove', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1039, '操作查询', 500, 1, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:query', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1040, '操作删除', 500, 2, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:remove', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1041, '日志导出', 500, 3, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:export', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1042, '登录查询', 501, 1, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:query', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1043, '登录删除', 501, 2, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:remove', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1044, '日志导出', 501, 3, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:export', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1045, '账户解锁', 501, 4, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:unlock', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1046, '在线查询', 109, 1, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:online:query', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1047, '批量强退', 109, 2, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:online:batchLogout', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1048, '单条强退', 109, 3, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:online:forceLogout', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1049, '任务查询', 110, 1, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:job:query', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1050, '任务新增', 110, 2, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:job:add', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1051, '任务修改', 110, 3, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:job:edit', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1052, '任务删除', 110, 4, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:job:remove', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1053, '状态修改', 110, 5, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:job:changeStatus', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1054, '任务导出', 110, 6, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:job:export', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1055, '生成查询', 116, 1, '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:query', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1056, '生成修改', 116, 2, '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:edit', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1057, '生成删除', 116, 3, '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:remove', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1058, '导入代码', 116, 4, '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:import', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1059, '预览代码', 116, 5, '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:preview', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1060, '生成代码', 116, 6, '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:code', '#', 'admin', '2023-10-16 10:30:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1061, '人大代表管理', 0, 0, 'deputy', 'renda/deputy/index', NULL, 1, 0, 'C', '0', '0', 'renda:deputy:list', 'user', 'admin', '2023-10-23 17:27:06', 'admin', '2023-10-23 17:28:30', '人大代表管理菜单');
INSERT INTO `sys_menu` VALUES (1062, '查询', 1061, 1, '#', '', NULL, 1, 0, 'F', '0', '0', 'renda:deputy:query', '#', 'admin', '2023-10-23 17:27:06', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1063, '新增', 1061, 2, '#', '', NULL, 1, 0, 'F', '0', '0', 'renda:deputy:add', '#', 'admin', '2023-10-23 17:27:06', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1064, '修改', 1061, 3, '#', '', NULL, 1, 0, 'F', '0', '0', 'renda:deputy:edit', '#', 'admin', '2023-10-23 17:27:06', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1065, '删除', 1061, 4, '#', '', NULL, 1, 0, 'F', '0', '0', 'renda:deputy:remove', '#', 'admin', '2023-10-23 17:27:06', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1066, '导出', 1061, 5, '#', '', NULL, 1, 0, 'F', '0', '0', 'renda:deputy:export', '#', 'admin', '2023-10-23 17:27:06', '', NULL, '');

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice`  (
  `notice_id` int(4) NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `notice_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公告标题',
  `notice_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公告类型（1通知 2公告）',
  `notice_content` longblob NULL COMMENT '公告内容',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通知公告表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_notice
-- ----------------------------
INSERT INTO `sys_notice` VALUES (1, '温馨提醒：2018-07-01 若依新版本发布啦', '2', 0xE696B0E78988E69CACE58685E5AEB9, '0', 'admin', '2023-10-16 10:30:55', '', NULL, '管理员');
INSERT INTO `sys_notice` VALUES (2, '维护通知：2018-07-01 若依系统凌晨维护', '1', 0xE7BBB4E68AA4E58685E5AEB9, '0', 'admin', '2023-10-16 10:30:55', '', NULL, '管理员');

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log`  (
  `oper_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '模块标题',
  `business_type` int(2) NULL DEFAULT 0 COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '请求方式',
  `operator_type` int(1) NULL DEFAULT 0 COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作地点',
  `oper_param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '返回参数',
  `status` int(1) NULL DEFAULT 0 COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime(0) NULL DEFAULT NULL COMMENT '操作时间',
  `cost_time` bigint(20) NULL DEFAULT 0 COMMENT '消耗时间',
  PRIMARY KEY (`oper_id`) USING BTREE,
  INDEX `idx_sys_oper_log_bt`(`business_type`) USING BTREE,
  INDEX `idx_sys_oper_log_s`(`status`) USING BTREE,
  INDEX `idx_sys_oper_log_ot`(`oper_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 92 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '操作日志记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_oper_log
-- ----------------------------
INSERT INTO `sys_oper_log` VALUES (1, '菜单管理', 3, 'com.renda.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/4', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}', 0, NULL, '2023-10-20 13:34:47', 9);
INSERT INTO `sys_oper_log` VALUES (2, '角色管理', 2, 'com.renda.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2023-10-16 10:30:55\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,114,3,115,116,1055,1056,1057,1058,1059,1060,117],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-20 13:35:09', 24);
INSERT INTO `sys_oper_log` VALUES (3, '菜单管理', 3, 'com.renda.web.controller.system.SysMenuController.remove()', 'DELETE', 1, 'admin', NULL, '/system/menu/4', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-20 13:35:15', 6);
INSERT INTO `sys_oper_log` VALUES (4, '用户管理', 5, 'com.renda.web.controller.system.SysUserController.export()', 'POST', 1, 'admin', NULL, '/system/user/export', '127.0.0.1', '内网IP', '{\"deptId\":\"106\",\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2023-10-20 18:00:06', 336);
INSERT INTO `sys_oper_log` VALUES (5, '部门管理', 2, 'com.renda.web.controller.system.SysDeptController.edit()', 'PUT', 1, 'admin', NULL, '/system/dept', '127.0.0.1', '内网IP', '{\"ancestors\":\"0\",\"children\":[],\"deptId\":100,\"deptName\":\"哈密市人大\",\"email\":\"<EMAIL>\",\"leader\":\"哈密市人大\",\"orderNum\":0,\"params\":{},\"parentId\":0,\"phone\":\"13888888888\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-20 18:02:59', 8);
INSERT INTO `sys_oper_log` VALUES (6, '部门管理', 3, 'com.renda.web.controller.system.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/system/dept/109', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-20 18:03:10', 5);
INSERT INTO `sys_oper_log` VALUES (7, '部门管理', 3, 'com.renda.web.controller.system.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/system/dept/108', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-20 18:03:12', 7);
INSERT INTO `sys_oper_log` VALUES (8, '部门管理', 3, 'com.renda.web.controller.system.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/system/dept/102', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-20 18:03:15', 6);
INSERT INTO `sys_oper_log` VALUES (9, '部门管理', 3, 'com.renda.web.controller.system.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/system/dept/107', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-20 18:03:17', 6);
INSERT INTO `sys_oper_log` VALUES (10, '部门管理', 3, 'com.renda.web.controller.system.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/system/dept/106', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-20 18:03:18', 7);
INSERT INTO `sys_oper_log` VALUES (11, '部门管理', 3, 'com.renda.web.controller.system.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/system/dept/105', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"部门存在用户,不允许删除\",\"code\":601}', 0, NULL, '2023-10-20 18:03:21', 3);
INSERT INTO `sys_oper_log` VALUES (12, '用户管理', 2, 'com.renda.web.controller.system.SysUserController.edit()', 'PUT', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"avatar\":\"\",\"createBy\":\"admin\",\"createTime\":\"2023-10-16 10:30:55\",\"delFlag\":\"0\",\"dept\":{\"ancestors\":\"0,100,101\",\"children\":[],\"deptId\":105,\"deptName\":\"测试部门\",\"leader\":\"若依\",\"orderNum\":3,\"params\":{},\"parentId\":101,\"status\":\"0\"},\"deptId\":100,\"email\":\"<EMAIL>\",\"loginDate\":\"2023-10-16 10:30:55\",\"loginIp\":\"127.0.0.1\",\"nickName\":\"若依\",\"params\":{},\"phonenumber\":\"15666666666\",\"postIds\":[2],\"remark\":\"测试员\",\"roleIds\":[2],\"roles\":[{\"admin\":false,\"dataScope\":\"2\",\"deptCheckStrictly\":false,\"flag\":false,\"menuCheckStrictly\":false,\"params\":{},\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\"}],\"sex\":\"1\",\"status\":\"0\",\"updateBy\":\"admin\",\"userId\":2,\"userName\":\"ry\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-20 18:03:59', 11);
INSERT INTO `sys_oper_log` VALUES (13, '部门管理', 3, 'com.renda.web.controller.system.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/system/dept/105', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-20 18:04:05', 4);
INSERT INTO `sys_oper_log` VALUES (14, '部门管理', 3, 'com.renda.web.controller.system.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/system/dept/104', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-20 18:04:07', 4);
INSERT INTO `sys_oper_log` VALUES (15, '部门管理', 3, 'com.renda.web.controller.system.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/system/dept/103', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"部门存在用户,不允许删除\",\"code\":601}', 0, NULL, '2023-10-20 18:04:09', 3);
INSERT INTO `sys_oper_log` VALUES (16, '用户管理', 2, 'com.renda.web.controller.system.SysUserController.edit()', 'PUT', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":true,\"avatar\":\"\",\"createBy\":\"admin\",\"createTime\":\"2023-10-16 10:30:55\",\"delFlag\":\"0\",\"dept\":{\"ancestors\":\"0,100,101\",\"children\":[],\"deptId\":103,\"deptName\":\"研发部门\",\"leader\":\"若依\",\"orderNum\":1,\"params\":{},\"parentId\":101,\"status\":\"0\"},\"deptId\":100,\"email\":\"<EMAIL>\",\"loginDate\":\"2023-10-20 17:55:29\",\"loginIp\":\"127.0.0.1\",\"nickName\":\"系统管理员\",\"params\":{},\"phonenumber\":\"15888888888\",\"postIds\":[],\"remark\":\"系统管理员\",\"roleIds\":[1],\"roles\":[{\"admin\":true,\"dataScope\":\"1\",\"deptCheckStrictly\":false,\"flag\":false,\"menuCheckStrictly\":false,\"params\":{},\"roleId\":1,\"roleKey\":\"admin\",\"roleName\":\"超级管理员\",\"roleSort\":1,\"status\":\"0\"}],\"sex\":\"2\",\"status\":\"0\",\"userId\":1,\"userName\":\"admin\"}', NULL, 1, '不允许操作超级管理员用户', '2023-10-20 18:05:18', 1);
INSERT INTO `sys_oper_log` VALUES (17, '用户管理', 2, 'com.renda.web.controller.system.SysUserController.edit()', 'PUT', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":true,\"avatar\":\"\",\"createBy\":\"admin\",\"createTime\":\"2023-10-16 10:30:55\",\"delFlag\":\"0\",\"dept\":{\"ancestors\":\"0,100,101\",\"children\":[],\"deptId\":103,\"deptName\":\"研发部门\",\"leader\":\"若依\",\"orderNum\":1,\"params\":{},\"parentId\":101,\"status\":\"0\"},\"deptId\":100,\"email\":\"<EMAIL>\",\"loginDate\":\"2023-10-20 17:55:29\",\"loginIp\":\"127.0.0.1\",\"nickName\":\"若依\",\"params\":{},\"phonenumber\":\"15888888888\",\"postIds\":[1],\"remark\":\"管理员\",\"roleIds\":[1],\"roles\":[{\"admin\":true,\"dataScope\":\"1\",\"deptCheckStrictly\":false,\"flag\":false,\"menuCheckStrictly\":false,\"params\":{},\"roleId\":1,\"roleKey\":\"admin\",\"roleName\":\"超级管理员\",\"roleSort\":1,\"status\":\"0\"}],\"sex\":\"1\",\"status\":\"0\",\"userId\":1,\"userName\":\"admin\"}', NULL, 1, '不允许操作超级管理员用户', '2023-10-20 18:05:28', 1);
INSERT INTO `sys_oper_log` VALUES (18, '部门管理', 3, 'com.renda.web.controller.system.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/system/dept/103', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-20 18:07:09', 5);
INSERT INTO `sys_oper_log` VALUES (19, '部门管理', 3, 'com.renda.web.controller.system.SysDeptController.remove()', 'DELETE', 1, 'admin', NULL, '/system/dept/101', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-20 18:07:12', 5);
INSERT INTO `sys_oper_log` VALUES (20, '部门管理', 1, 'com.renda.web.controller.system.SysDeptController.add()', 'POST', 1, 'admin', NULL, '/system/dept', '127.0.0.1', '内网IP', '{\"ancestors\":\"0,100\",\"children\":[],\"createBy\":\"admin\",\"deptName\":\"伊州区人大\",\"orderNum\":0,\"params\":{},\"parentId\":100,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-20 18:07:49', 6);
INSERT INTO `sys_oper_log` VALUES (21, '部门管理', 1, 'com.renda.web.controller.system.SysDeptController.add()', 'POST', 1, 'admin', NULL, '/system/dept', '127.0.0.1', '内网IP', '{\"ancestors\":\"0,100\",\"children\":[],\"createBy\":\"admin\",\"deptName\":\"巴里坤人大\",\"orderNum\":1,\"params\":{},\"parentId\":100,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-20 18:08:02', 5);
INSERT INTO `sys_oper_log` VALUES (22, '部门管理', 1, 'com.renda.web.controller.system.SysDeptController.add()', 'POST', 1, 'admin', NULL, '/system/dept', '127.0.0.1', '内网IP', '{\"ancestors\":\"0,100\",\"children\":[],\"createBy\":\"admin\",\"deptName\":\"伊吾县人大\",\"orderNum\":2,\"params\":{},\"parentId\":100,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-20 18:08:15', 5);
INSERT INTO `sys_oper_log` VALUES (23, '部门管理', 2, 'com.renda.web.controller.system.SysDeptController.edit()', 'PUT', 1, 'admin', NULL, '/system/dept', '127.0.0.1', '内网IP', '{\"ancestors\":\"0,100\",\"children\":[],\"deptId\":111,\"deptName\":\"巴里坤县人大\",\"orderNum\":1,\"params\":{},\"parentId\":100,\"parentName\":\"哈密市人大\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-20 18:08:22', 9);
INSERT INTO `sys_oper_log` VALUES (24, '岗位管理', 3, 'com.renda.web.controller.system.SysPostController.remove()', 'DELETE', 1, 'admin', NULL, '/system/post/4', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-20 18:11:10', 5);
INSERT INTO `sys_oper_log` VALUES (25, '岗位管理', 3, 'com.renda.web.controller.system.SysPostController.remove()', 'DELETE', 1, 'admin', NULL, '/system/post/3', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-20 18:11:12', 5);
INSERT INTO `sys_oper_log` VALUES (26, '岗位管理', 2, 'com.renda.web.controller.system.SysPostController.edit()', 'PUT', 1, 'admin', NULL, '/system/post', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2023-10-16 10:30:55\",\"flag\":false,\"params\":{},\"postCode\":\"manager\",\"postId\":1,\"postName\":\"管理员岗\",\"postSort\":1,\"remark\":\"\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-20 18:11:39', 7);
INSERT INTO `sys_oper_log` VALUES (27, '岗位管理', 2, 'com.renda.web.controller.system.SysPostController.edit()', 'PUT', 1, 'admin', NULL, '/system/post', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2023-10-16 10:30:55\",\"flag\":false,\"params\":{},\"postCode\":\"normal\",\"postId\":2,\"postName\":\"普通岗\",\"postSort\":2,\"remark\":\"\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-20 18:12:02', 5);
INSERT INTO `sys_oper_log` VALUES (28, '岗位管理', 2, 'com.renda.web.controller.system.SysPostController.edit()', 'PUT', 1, 'admin', NULL, '/system/post', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2023-10-16 10:30:55\",\"flag\":false,\"params\":{},\"postCode\":\"common\",\"postId\":2,\"postName\":\"普通岗\",\"postSort\":2,\"remark\":\"\",\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-20 18:12:36', 5);
INSERT INTO `sys_oper_log` VALUES (29, '用户管理', 2, 'com.renda.web.controller.system.SysUserController.edit()', 'PUT', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"avatar\":\"\",\"createBy\":\"admin\",\"createTime\":\"2023-10-16 10:30:55\",\"delFlag\":\"0\",\"dept\":{\"ancestors\":\"0\",\"children\":[],\"deptId\":100,\"deptName\":\"哈密市人大\",\"leader\":\"哈密市人大\",\"orderNum\":0,\"params\":{},\"parentId\":0,\"status\":\"0\"},\"deptId\":100,\"email\":\"<EMAIL>\",\"loginDate\":\"2023-10-16 10:30:55\",\"loginIp\":\"127.0.0.1\",\"nickName\":\"测试人员\",\"params\":{},\"phonenumber\":\"15666666666\",\"postIds\":[2],\"remark\":\"测试员\",\"roleIds\":[2],\"roles\":[{\"admin\":false,\"dataScope\":\"2\",\"deptCheckStrictly\":false,\"flag\":false,\"menuCheckStrictly\":false,\"params\":{},\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\"}],\"sex\":\"0\",\"status\":\"0\",\"updateBy\":\"admin\",\"userId\":2,\"userName\":\"ry\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-20 18:13:08', 11);
INSERT INTO `sys_oper_log` VALUES (30, '字典类型', 1, 'com.renda.web.controller.system.SysDictTypeController.add()', 'POST', 1, 'admin', NULL, '/system/dict/type', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"dictName\":\"民族\",\"dictType\":\"yw_mz\",\"params\":{},\"remark\":\"民族（业务）\",\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-23 09:38:48', 10);
INSERT INTO `sys_oper_log` VALUES (31, '代码生成', 6, 'com.renda.generator.controller.GenController.importTableSave()', 'POST', 1, 'admin', NULL, '/tool/gen/importTable', '127.0.0.1', '内网IP', '{\"tables\":\"rd_deputy\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-23 17:03:51', 72);
INSERT INTO `sys_oper_log` VALUES (32, '代码生成', 2, 'com.renda.generator.controller.GenController.editSave()', 'PUT', 1, 'admin', NULL, '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"deputy\",\"className\":\"RdDeputy\",\"columns\":[{\"capJavaField\":\"Id\",\"columnId\":1,\"columnName\":\"id\",\"columnType\":\"bigint(20)\",\"createBy\":\"admin\",\"createTime\":\"2023-10-23 17:03:51\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isPk\":\"1\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":1,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"Type\",\"columnComment\":\"代表类型：0-代表；1-委员；2-管理；\",\"columnId\":2,\"columnName\":\"type\",\"columnType\":\"int(1)\",\"createBy\":\"admin\",\"createTime\":\"2023-10-23 17:03:51\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"select\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"type\",\"javaType\":\"Integer\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":2,\"superColumn\":false,\"tableId\":1,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"Name\",\"columnComment\":\"姓名\",\"columnId\":3,\"columnName\":\"name\",\"columnType\":\"varchar(64)\",\"createBy\":\"admin\",\"createTime\":\"2023-10-23 17:03:51\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"name\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"LIKE\",\"required\":true,\"sort\":3,\"superColumn\":false,\"tableId\":1,\"updateBy\":\"\",\"usableColumn\":false},{\"capJavaField\":\"Nation\",\"columnComment\":\"民族\",\"columnId\":4,\"columnName\":\"nation\",\"columnType\":\"varchar(50)\",\"createBy\":\"admin\",\"createTime\":\"2023-10-23 17:03:51\",\"dictType\":\"yw_mz\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"javaField\":\"nation\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"quer', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-23 17:09:56', 34);
INSERT INTO `sys_oper_log` VALUES (33, '代码生成', 2, 'com.renda.generator.controller.GenController.editSave()', 'PUT', 1, 'admin', NULL, '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"deputy\",\"className\":\"RdDeputy\",\"columns\":[{\"capJavaField\":\"Id\",\"columnId\":1,\"columnName\":\"id\",\"columnType\":\"bigint(20)\",\"createBy\":\"admin\",\"createTime\":\"2023-10-23 17:03:51\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isPk\":\"1\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":1,\"updateBy\":\"\",\"updateTime\":\"2023-10-23 17:09:56\",\"usableColumn\":false},{\"capJavaField\":\"Type\",\"columnComment\":\"代表类型：0-代表；1-委员；2-管理；\",\"columnId\":2,\"columnName\":\"type\",\"columnType\":\"int(1)\",\"createBy\":\"admin\",\"createTime\":\"2023-10-23 17:03:51\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"select\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"type\",\"javaType\":\"Integer\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":2,\"superColumn\":false,\"tableId\":1,\"updateBy\":\"\",\"updateTime\":\"2023-10-23 17:09:56\",\"usableColumn\":false},{\"capJavaField\":\"Name\",\"columnComment\":\"姓名\",\"columnId\":3,\"columnName\":\"name\",\"columnType\":\"varchar(64)\",\"createBy\":\"admin\",\"createTime\":\"2023-10-23 17:03:51\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"name\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"LIKE\",\"required\":true,\"sort\":3,\"superColumn\":false,\"tableId\":1,\"updateBy\":\"\",\"updateTime\":\"2023-10-23 17:09:56\",\"usableColumn\":false},{\"capJavaField\":\"Nation\",\"columnComment\":\"民族\",\"columnId\":4,\"columnName\":\"nation\",\"columnType\":\"varchar(50)\",\"createBy\":\"admin\",\"createTime\":\"2023-10-23 17:03:51\",\"dictType\":\"yw_mz\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-23 17:15:24', 45);
INSERT INTO `sys_oper_log` VALUES (34, '代码生成', 2, 'com.renda.generator.controller.GenController.editSave()', 'PUT', 1, 'admin', NULL, '/tool/gen', '127.0.0.1', '内网IP', '{\"businessName\":\"deputy\",\"className\":\"RdDeputy\",\"columns\":[{\"capJavaField\":\"Id\",\"columnId\":1,\"columnName\":\"id\",\"columnType\":\"bigint(20)\",\"createBy\":\"admin\",\"createTime\":\"2023-10-23 17:03:51\",\"dictType\":\"\",\"edit\":false,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isPk\":\"1\",\"javaField\":\"id\",\"javaType\":\"Long\",\"list\":false,\"params\":{},\"pk\":true,\"query\":false,\"queryType\":\"EQ\",\"required\":false,\"sort\":1,\"superColumn\":false,\"tableId\":1,\"updateBy\":\"\",\"updateTime\":\"2023-10-23 17:15:24\",\"usableColumn\":false},{\"capJavaField\":\"Type\",\"columnComment\":\"代表类型：0-代表；1-委员；2-管理；\",\"columnId\":2,\"columnName\":\"type\",\"columnType\":\"int(1)\",\"createBy\":\"admin\",\"createTime\":\"2023-10-23 17:03:51\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"select\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"type\",\"javaType\":\"Integer\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"EQ\",\"required\":true,\"sort\":2,\"superColumn\":false,\"tableId\":1,\"updateBy\":\"\",\"updateTime\":\"2023-10-23 17:15:24\",\"usableColumn\":false},{\"capJavaField\":\"Name\",\"columnComment\":\"姓名\",\"columnId\":3,\"columnName\":\"name\",\"columnType\":\"varchar(64)\",\"createBy\":\"admin\",\"createTime\":\"2023-10-23 17:03:51\",\"dictType\":\"\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"isPk\":\"0\",\"isQuery\":\"1\",\"isRequired\":\"1\",\"javaField\":\"name\",\"javaType\":\"String\",\"list\":true,\"params\":{},\"pk\":false,\"query\":true,\"queryType\":\"LIKE\",\"required\":true,\"sort\":3,\"superColumn\":false,\"tableId\":1,\"updateBy\":\"\",\"updateTime\":\"2023-10-23 17:15:24\",\"usableColumn\":false},{\"capJavaField\":\"Nation\",\"columnComment\":\"民族\",\"columnId\":4,\"columnName\":\"nation\",\"columnType\":\"varchar(50)\",\"createBy\":\"admin\",\"createTime\":\"2023-10-23 17:03:51\",\"dictType\":\"yw_mz\",\"edit\":true,\"htmlType\":\"input\",\"increment\":false,\"insert\":true,\"isEdit\":\"1\",\"isIncrement\":\"0\",\"isInsert\":\"1\",\"isList\":\"1\",\"', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-23 17:17:16', 24);
INSERT INTO `sys_oper_log` VALUES (35, '代码生成', 8, 'com.renda.generator.controller.GenController.batchGenCode()', 'GET', 1, 'admin', NULL, '/tool/gen/batchGenCode', '127.0.0.1', '内网IP', '{\"tables\":\"rd_deputy\"}', NULL, 0, NULL, '2023-10-23 17:18:12', 93);
INSERT INTO `sys_oper_log` VALUES (36, '菜单管理', 2, 'com.renda.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"renda/deputy/index\",\"createTime\":\"2023-10-23 17:27:06\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1061,\"menuName\":\"人大代表管理\",\"menuType\":\"C\",\"orderNum\":0,\"params\":{},\"parentId\":0,\"path\":\"deputy\",\"perms\":\"renda:deputy:list\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-23 17:28:00', 10);
INSERT INTO `sys_oper_log` VALUES (37, '菜单管理', 2, 'com.renda.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', NULL, '/system/menu', '127.0.0.1', '内网IP', '{\"children\":[],\"component\":\"renda/deputy/index\",\"createTime\":\"2023-10-23 17:27:06\",\"icon\":\"user\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":1061,\"menuName\":\"人大代表管理\",\"menuType\":\"C\",\"orderNum\":0,\"params\":{},\"parentId\":0,\"path\":\"deputy\",\"perms\":\"renda:deputy:list\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-23 17:28:30', 5);
INSERT INTO `sys_oper_log` VALUES (38, '参数管理', 9, 'com.renda.web.controller.system.SysConfigController.refreshCache()', 'DELETE', 1, 'admin', NULL, '/system/config/refreshCache', '127.0.0.1', '内网IP', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-24 11:04:25', 31);
INSERT INTO `sys_oper_log` VALUES (39, '人大代表管理', 1, 'com.renda.core.controller.RdDeputyController.add()', 'POST', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"birthday\":\"1966-08-08\",\"createTime\":\"2023-10-24 11:18:31\",\"duty\":\"县委常委\",\"name\":\"张三\",\"nation\":\"01\",\"params\":{},\"phone\":\"13319020123\",\"resume\":\"1989年07月至1992年06月，在江西萍乡钢铁厂技校任教师、助理工程师；\\n\\n1992年06月至1997年03月，在广州钢铁厂劳动人事处任干部、综合科科长、经济师；\\n\\n1997年03月至2000年12月，在广东省委组织部地方干部处任副主任科员、主任科员；2000年12月至2002年10月，广东省委组织部干部二处主任科员；\\n\\n2002年10月至2008年05月，广东省委组织部干部二处副处长（其间：2007年9月至今在中山大学岭南学院世界经济专业进行博士研究生学习）；\\n\\n2008年05月至2008年05月，广东省委组织部正处职干部；\\n\\n2008年05月至2009年09月，广东省水利厅人事劳动教育处（离退休人员管理处）处长；\\n\\n2009年09月至今，任广东省水利厅副厅长、党组成员。（副厅级）\"}', NULL, 1, '\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'id\' doesn\'t have a default value\r\n### The error may exist in file [D:\\renda\\renda-back\\renda-core\\target\\classes\\mapper\\renda\\RdDeputyMapper.xml]\r\n### The error may involve com.renda.core.mapper.RdDeputyMapper.insertRdDeputy-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into rd_deputy          ( name,             nation,             duty,             birthday,             phone,             resume,                                                    create_time )           values ( ?,             ?,             ?,             ?,             ?,             ?,                                                    ? )\r\n### Cause: java.sql.SQLException: Field \'id\' doesn\'t have a default value\n; Field \'id\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'id\' doesn\'t have a default value', '2023-10-24 11:18:31', 40);
INSERT INTO `sys_oper_log` VALUES (40, '人大代表管理', 1, 'com.renda.core.controller.RdDeputyController.add()', 'POST', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"birthday\":\"1966-08-08\",\"createTime\":\"2023-10-24 11:19:09\",\"duty\":\"县委常委\",\"name\":\"张三\",\"nation\":\"01\",\"params\":{},\"phone\":\"13319020123\",\"resume\":\"1989年07月至1992年06月，在江西萍乡钢铁厂技校任教师、助理工程师；\\n\\n1992年06月至1997年03月，在广州钢铁厂劳动人事处任干部、综合科科长、经济师；\\n\\n1997年03月至2000年12月，在广东省委组织部地方干部处任副主任科员、主任科员；2000年12月至2002年10月，广东省委组织部干部二处主任科员；\\n\\n2002年10月至2008年05月，广东省委组织部干部二处副处长（其间：2007年9月至今在中山大学岭南学院世界经济专业进行博士研究生学习）；\\n\\n2008年05月至2008年05月，广东省委组织部正处职干部；\\n\\n2008年05月至2009年09月，广东省水利厅人事劳动教育处（离退休人员管理处）处长；\\n\\n2009年09月至今，任广东省水利厅副厅长、党组成员。（副厅级）\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-24 11:19:09', 6);
INSERT INTO `sys_oper_log` VALUES (41, '人大代表管理', 2, 'com.renda.core.controller.RdDeputyController.edit()', 'PUT', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"birthday\":\"1966-08-08\",\"createTime\":\"2023-10-24 11:19:10\",\"duty\":\"省水利厅副厅长、党组成员\",\"id\":1,\"name\":\"刘敏\",\"nation\":\"01\",\"params\":{},\"phone\":\"13319020123\",\"resume\":\"1989年07月至1992年06月，在江西萍乡钢铁厂技校任教师、助理工程师；\\n\\n1992年06月至1997年03月，在广州钢铁厂劳动人事处任干部、综合科科长、经济师；\\n\\n1997年03月至2000年12月，在广东省委组织部地方干部处任副主任科员、主任科员；2000年12月至2002年10月，广东省委组织部干部二处主任科员；\\n\\n2002年10月至2008年05月，广东省委组织部干部二处副处长（其间：2007年9月至今在中山大学岭南学院世界经济专业进行博士研究生学习）；\\n\\n2008年05月至2008年05月，广东省委组织部正处职干部；\\n\\n2008年05月至2009年09月，广东省水利厅人事劳动教育处（离退休人员管理处）处长；\\n\\n2009年09月至今，任广东省水利厅副厅长、党组成员。（副厅级）\",\"type\":0,\"updateTime\":\"2023-10-24 11:19:45\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-24 11:19:45', 4);
INSERT INTO `sys_oper_log` VALUES (42, '字典类型', 1, 'com.renda.web.controller.system.SysDictTypeController.add()', 'POST', 1, 'admin', NULL, '/system/dict/type', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"dictName\":\"代表类型\",\"dictType\":\"rd_deputytype\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-24 11:21:32', 9);
INSERT INTO `sys_oper_log` VALUES (43, '字典数据', 1, 'com.renda.web.controller.system.SysDictDataController.add()', 'POST', 1, 'admin', NULL, '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"人大代表\",\"dictSort\":0,\"dictType\":\"rd_deputytype\",\"dictValue\":\"0\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-24 11:22:10', 6);
INSERT INTO `sys_oper_log` VALUES (44, '字典数据', 1, 'com.renda.web.controller.system.SysDictDataController.add()', 'POST', 1, 'admin', NULL, '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"人大委员\",\"dictSort\":1,\"dictType\":\"rd_deputytype\",\"dictValue\":\"1\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-24 11:22:22', 6);
INSERT INTO `sys_oper_log` VALUES (45, '字典数据', 1, 'com.renda.web.controller.system.SysDictDataController.add()', 'POST', 1, 'admin', NULL, '/system/dict/data', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"管理\",\"dictSort\":2,\"dictType\":\"rd_deputytype\",\"dictValue\":\"2\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-24 11:22:39', 8);
INSERT INTO `sys_oper_log` VALUES (46, '字典类型', 2, 'com.renda.web.controller.system.SysDictTypeController.edit()', 'PUT', 1, 'admin', NULL, '/system/dict/type', '127.0.0.1', '内网IP', '{\"createBy\":\"admin\",\"createTime\":\"2023-10-24 11:21:32\",\"dictId\":12,\"dictName\":\"代表类型\",\"dictType\":\"rd_deputy_type\",\"params\":{},\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-24 11:27:12', 24);
INSERT INTO `sys_oper_log` VALUES (47, '人大代表管理', 1, 'com.renda.core.controller.RdDeputyController.add()', 'POST', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"birthday\":\"1968-02-01\",\"createTime\":\"2023-10-24 11:32:22\",\"duty\":\"省委统战部副部长\",\"name\":\"张科\",\"nation\":\"01\",\"params\":{},\"phone\":\"13899881154\",\"resume\":\"男，汉族，重庆人，1968年2月生，学历在职研究生，公共管理硕士，1989年6月参加工作，1987年1月加入中国共产党。\\n\\n现任广东省委统战部副部长，省民族宗教事务委员会党组书记、主任，省政协民族宗教委员会副主任。\",\"type\":0}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-24 11:32:22', 4);
INSERT INTO `sys_oper_log` VALUES (48, '人大代表管理', 2, 'com.renda.core.controller.RdDeputyController.edit()', 'PUT', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"birthday\":\"1968-02-01\",\"createTime\":\"2023-10-24 11:32:22\",\"duty\":\"省委统战部副部长\",\"id\":2,\"name\":\"张科\",\"nation\":\"01\",\"params\":{},\"phone\":\"13899881154\",\"resume\":\"男，汉族，重庆人，1968年2月生，学历在职研究生，公共管理硕士，1989年6月参加工作，1987年1月加入中国共产党。\\n\\n现任广东省委统战部副部长，省民族宗教事务委员会党组书记、主任，省政协民族宗教委员会副主任。\",\"type\":1,\"updateTime\":\"2023-10-24 11:32:37\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-24 11:32:37', 4);
INSERT INTO `sys_oper_log` VALUES (49, '人大代表管理', 2, 'com.renda.core.controller.RdDeputyController.edit()', 'PUT', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"birthday\":\"1966-08-08\",\"createTime\":\"2023-10-24 11:19:10\",\"duty\":\"省水利厅副厅长、党组成员\",\"id\":1,\"name\":\"刘敏\",\"nation\":\"01\",\"params\":{},\"phone\":\"13779369898\",\"resume\":\"1989年07月至1992年06月，在江西萍乡钢铁厂技校任教师、助理工程师；\\n\\n1992年06月至1997年03月，在广州钢铁厂劳动人事处任干部、综合科科长、经济师；\\n\\n1997年03月至2000年12月，在广东省委组织部地方干部处任副主任科员、主任科员；2000年12月至2002年10月，广东省委组织部干部二处主任科员；\\n\\n2002年10月至2008年05月，广东省委组织部干部二处副处长（其间：2007年9月至今在中山大学岭南学院世界经济专业进行博士研究生学习）；\\n\\n2008年05月至2008年05月，广东省委组织部正处职干部；\\n\\n2008年05月至2009年09月，广东省水利厅人事劳动教育处（离退休人员管理处）处长；\\n\\n2009年09月至今，任广东省水利厅副厅长、党组成员。（副厅级）\",\"type\":0,\"updateTime\":\"2023-10-24 11:34:22\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-24 11:34:22', 15);
INSERT INTO `sys_oper_log` VALUES (50, '人大代表管理', 2, 'com.renda.core.controller.RdDeputyController.edit()', 'PUT', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"birthday\":\"1968-02-01\",\"createTime\":\"2023-10-24 11:32:22\",\"duty\":\"省委统战部副部长\",\"id\":2,\"name\":\"张科\",\"nation\":\"01\",\"params\":{},\"phone\":\"18699178090\",\"resume\":\"男，汉族，重庆人，1968年2月生，学历在职研究生，公共管理硕士，1989年6月参加工作，1987年1月加入中国共产党。\\n\\n现任广东省委统战部副部长，省民族宗教事务委员会党组书记、主任，省政协民族宗教委员会副主任。\",\"type\":1,\"updateTime\":\"2023-10-24 11:34:30\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-24 11:34:30', 3);
INSERT INTO `sys_oper_log` VALUES (51, '人大代表管理', 1, 'com.renda.core.controller.RdDeputyController.add()', 'POST', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"birthday\":\"1978-09-28\",\"createTime\":\"2023-10-24 11:35:31\",\"duty\":\"书记\",\"name\":\"于建\",\"nation\":\"01\",\"params\":{},\"phone\":\"13888888888\",\"resume\":\"未填写\",\"type\":2}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-24 11:35:31', 4);
INSERT INTO `sys_oper_log` VALUES (52, '人大代表管理', 5, 'com.renda.core.controller.RdDeputyController.export()', 'POST', 1, 'admin', NULL, '/renda/deputy/export', '127.0.0.1', '内网IP', '{\"pageSize\":\"10\",\"pageNum\":\"1\"}', NULL, 0, NULL, '2023-10-24 11:36:28', 471);
INSERT INTO `sys_oper_log` VALUES (53, '用户头像', 2, 'com.renda.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/profile/avatar/2023/10/24/blob_20231024130947A001.png\",\"code\":200}', 0, NULL, '2023-10-24 13:09:48', 71);
INSERT INTO `sys_oper_log` VALUES (54, '用户头像', 2, 'com.renda.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/profile/avatar/2023/10/24/blob_20231024131310A002.png\",\"code\":200}', 0, NULL, '2023-10-24 13:13:10', 13);
INSERT INTO `sys_oper_log` VALUES (55, '用户管理', 2, 'com.renda.web.controller.system.SysUserController.edit()', 'PUT', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"avatar\":\"\",\"createBy\":\"admin\",\"createTime\":\"2023-10-16 10:30:55\",\"delFlag\":\"0\",\"dept\":{\"ancestors\":\"0\",\"children\":[],\"deptId\":100,\"deptName\":\"哈密市人大\",\"orderNum\":0,\"params\":{},\"parentId\":0,\"status\":\"0\"},\"deptId\":110,\"email\":\"<EMAIL>\",\"loginDate\":\"2023-10-16 10:30:55\",\"loginIp\":\"127.0.0.1\",\"nickName\":\"测试人员\",\"params\":{},\"phonenumber\":\"13999999999\",\"postIds\":[2],\"remark\":\"测试员\",\"roleIds\":[2],\"roles\":[{\"admin\":false,\"dataScope\":\"2\",\"deptCheckStrictly\":false,\"flag\":false,\"menuCheckStrictly\":false,\"params\":{},\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\"}],\"sex\":\"0\",\"status\":\"0\",\"updateBy\":\"admin\",\"userId\":2,\"userName\":\"test\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-24 13:17:34', 56);
INSERT INTO `sys_oper_log` VALUES (56, '角色管理', 1, 'com.renda.web.controller.system.SysRoleController.add()', 'POST', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createBy\":\"admin\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1061,1062,1063,1064,1065,1066],\"params\":{},\"roleId\":3,\"roleKey\":\"yzq_all\",\"roleName\":\"伊州区管理员\",\"roleSort\":0,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-24 13:18:40', 15);
INSERT INTO `sys_oper_log` VALUES (57, '角色管理', 2, 'com.renda.web.controller.system.SysRoleController.dataScope()', 'PUT', 1, 'admin', NULL, '/system/role/dataScope', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2023-10-24 13:18:40\",\"dataScope\":\"4\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"params\":{},\"roleId\":3,\"roleKey\":\"yzq_all\",\"roleName\":\"伊州区管理员\",\"roleSort\":0,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-24 13:18:49', 6);
INSERT INTO `sys_oper_log` VALUES (58, '角色管理', 1, 'com.renda.web.controller.system.SysRoleController.add()', 'POST', 1, 'admin', NULL, '/system/role', '127.0.0.1', '内网IP', '{\"admin\":false,\"createBy\":\"admin\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1061,1062,1063,1064,1065,1066],\"params\":{},\"roleId\":4,\"roleKey\":\"blk_all\",\"roleName\":\"巴里坤管理员\",\"roleSort\":0,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-24 13:19:16', 10);
INSERT INTO `sys_oper_log` VALUES (59, '角色管理', 2, 'com.renda.web.controller.system.SysRoleController.dataScope()', 'PUT', 1, 'admin', NULL, '/system/role/dataScope', '127.0.0.1', '内网IP', '{\"admin\":false,\"createTime\":\"2023-10-24 13:19:16\",\"dataScope\":\"4\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"params\":{},\"roleId\":4,\"roleKey\":\"blk_all\",\"roleName\":\"巴里坤管理员\",\"roleSort\":0,\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-24 13:19:28', 6);
INSERT INTO `sys_oper_log` VALUES (60, '用户管理', 2, 'com.renda.web.controller.system.SysUserController.edit()', 'PUT', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"avatar\":\"\",\"createBy\":\"admin\",\"createTime\":\"2023-10-16 10:30:55\",\"delFlag\":\"0\",\"dept\":{\"ancestors\":\"0,100\",\"children\":[],\"deptId\":110,\"deptName\":\"伊州区人大\",\"orderNum\":0,\"params\":{},\"parentId\":100,\"status\":\"0\"},\"deptId\":110,\"email\":\"<EMAIL>\",\"loginDate\":\"2023-10-16 10:30:55\",\"loginIp\":\"127.0.0.1\",\"nickName\":\"测试人员\",\"params\":{},\"phonenumber\":\"13999999999\",\"postIds\":[2],\"remark\":\"测试员\",\"roleIds\":[3],\"roles\":[{\"admin\":false,\"dataScope\":\"2\",\"deptCheckStrictly\":false,\"flag\":false,\"menuCheckStrictly\":false,\"params\":{},\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\"}],\"sex\":\"0\",\"status\":\"0\",\"updateBy\":\"admin\",\"userId\":2,\"userName\":\"test\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-24 13:19:48', 14);
INSERT INTO `sys_oper_log` VALUES (61, '用户管理', 1, 'com.renda.web.controller.system.SysUserController.add()', 'POST', 1, 'admin', NULL, '/system/user', '127.0.0.1', '内网IP', '{\"admin\":false,\"createBy\":\"admin\",\"deptId\":111,\"nickName\":\"巴里坤管理员\",\"params\":{},\"postIds\":[],\"roleIds\":[4],\"status\":\"0\",\"userId\":3,\"userName\":\"blktest\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-24 13:20:29', 82);
INSERT INTO `sys_oper_log` VALUES (62, '用户管理', 2, 'com.renda.web.controller.system.SysUserController.resetPwd()', 'PUT', 1, 'admin', NULL, '/system/user/resetPwd', '127.0.0.1', '内网IP', '{\"admin\":false,\"params\":{},\"updateBy\":\"admin\",\"userId\":2}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-24 17:17:11', 79);
INSERT INTO `sys_oper_log` VALUES (63, '用户头像', 2, 'com.renda.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/profile/avatar/2023/10/25/blob_20231025101641A001.png\",\"code\":200}', 0, NULL, '2023-10-25 10:16:41', 42);
INSERT INTO `sys_oper_log` VALUES (64, '用户头像', 2, 'com.renda.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/profile/avatar/2023/10/25/blob_20231025102029A002.png\",\"code\":200}', 0, NULL, '2023-10-25 10:20:29', 6);
INSERT INTO `sys_oper_log` VALUES (65, '用户头像', 2, 'com.renda.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/profile/avatar/2023/10/25/blob_20231025102042A003.png\",\"code\":200}', 0, NULL, '2023-10-25 10:20:42', 3);
INSERT INTO `sys_oper_log` VALUES (66, '用户头像', 2, 'com.renda.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/profile/avatar/2023/10/25/blob_20231025102055A004.png\",\"code\":200}', 0, NULL, '2023-10-25 10:20:55', 3);
INSERT INTO `sys_oper_log` VALUES (67, '用户头像', 2, 'com.renda.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/profile/avatar/2023/10/25/blob_20231025184754A001.png\",\"code\":200}', 0, NULL, '2023-10-25 18:47:54', 49);
INSERT INTO `sys_oper_log` VALUES (68, '用户头像', 2, 'com.renda.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/profile/avatar/2023/10/25/blob_20231025190649A002.png\",\"code\":200}', 0, NULL, '2023-10-25 19:06:49', 8);
INSERT INTO `sys_oper_log` VALUES (69, '用户头像', 2, 'com.renda.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/profile/avatar/2023/10/25/blob_20231025190716A003.png\",\"code\":200}', 0, NULL, '2023-10-25 19:07:16', 5);
INSERT INTO `sys_oper_log` VALUES (70, '用户头像', 2, 'com.renda.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/profile/avatar/2023/10/25/blob_20231025190759A004.png\",\"code\":200}', 0, NULL, '2023-10-25 19:07:59', 8);
INSERT INTO `sys_oper_log` VALUES (71, '用户头像', 2, 'com.renda.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/profile/avatar/2023/10/26/blob_20231026110251A001.png\",\"code\":200}', 0, NULL, '2023-10-26 11:02:51', 60);
INSERT INTO `sys_oper_log` VALUES (72, '用户头像', 2, 'com.renda.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/profile/avatar/2023/10/26/blob_20231026124610A002.png\",\"code\":200}', 0, NULL, '2023-10-26 12:46:10', 8);
INSERT INTO `sys_oper_log` VALUES (73, '用户头像', 2, 'com.renda.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/profile/avatar/2023/10/26/blob_20231026125049A003.png\",\"code\":200}', 0, NULL, '2023-10-26 12:50:49', 0);
INSERT INTO `sys_oper_log` VALUES (74, '用户头像', 2, 'com.renda.web.controller.system.SysProfileController.avatar()', 'POST', 1, 'admin', NULL, '/system/user/profile/avatar', '127.0.0.1', '内网IP', '', '{\"msg\":\"操作成功\",\"imgUrl\":\"/profile/avatar/2023/10/26/blob_20231026125200A004.png\",\"code\":200}', 0, NULL, '2023-10-26 12:52:01', 0);
INSERT INTO `sys_oper_log` VALUES (75, '人大代表管理', 2, 'com.renda.core.controller.RdDeputyController.edit()', 'PUT', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"avatar\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAPwAAAD8CAYAAABTq8lnAAAAAXNSR0IArs4c6QAAIABJREFUeF7cvWesrel1HrZ2b6fdPo0cFomy5cAKnDhRDASKgPwI/CNGkMBKQZwAaUjgH05BrEhWYMCKHFmRRYmCZapQhRSbRJGUSIkih6JEatjrDDkznD5z587t556yew2e51nr+969z97n7HPnjqzkSJdzyy7f937vs8qznrXegr39SzMrFMxsZla0Iz8F/lv+gz9VbGbFQsGKVuB/V/3otWaVgvErCnwf3jXj9+Hfi/72Av4Kfy7mFxHfgX/j6wpTvrdUyF/Dz/D3xnXgmmd4E7+zYCUr6tZmuNWCFQolvXQ2tVLRbFaY+nXpc7N7nk2z6+T78DlF/BcfNdH9Fws2nc34X9wTPiHuBfec3YNfXHx2/t+pronrgWvTguh/8XdaM35Xtl5Tm+HffK3iGWTv9fXSNfm6LjwnfJ7WG2sx4zUXCnqu8f2Fmf+ea4971GdpDQvZuhd5w37tuGffH/P7BnslFkGvwf8X8ADiM/1Z8uv4eb5P8H1FrK3WvuTXzueM7yrGv8Tr/f3+urnP5zXke0h7Bben6+dTju/lfsnvolgs61qLvi9irfA+vBb/x6XwZ8p9r79Ln7vuT7/yNcnvFfc37vfsnT/zs/bE175hk9HYRsORTaYTm01nNh5rwxNTwEJR9zuZTm0yHdtkMuFzxH9nM+wV7PPYu3cB+CqBFIuaL0j2PH0h8CgB+HIsXEGbVMumi/7/HOD9IRLw2PTYMG5ICIoVgM9AlGwSPbQE8L7h7gXg8TWlBPDazMnmPQXgaRCwr/4VAR6Xiu9eCnisPY1wGEuA1MH3rwjwME5hKE4NeBi0QsGuPPecvfedv2ovPPm09XpdGg3sMzqXQtGmU33H1P9+MBi6p9Fr8APA0yiUCjQU+JDCnIeXKUoNs1vueVAH4GXX/Re90OLPzKoAPb/fI4hiwSp8PHcPeEUJ7m2WeHjd5d17+PAIiADySEQenl68WKTlLC+uFTelvGVY4HU9PA0FABrvdaN4ag9PR4FNAZCE15I7WBfwEfD9ZfHwqwCvRXYves8AH05I0VVEhXROKzw8/y1xgPK84YVP5+HxWdhXX/2zz9ofvPcDdu2VKzYaDG06mfAZlstlm06Nnnw8mdh0MrPZZCpPPjOb2pQRC/4M7899WinLWEwQIdPDC+cewc2hdj6kR3hcsGoBgRT+T2BniLWwoeJDBHgPV7ABixGS3X1Iz+9i2Oohp7Cdb2iE6ZkxKjAFmA/pfesDGCX8/viQXvtK3pxh2Bzg45v01BcBr7BNBiq8bLqmJ3t4vT8P6bHuboiWhfQLgA/jFd8fz2VZSM8IxdMgRfGnCOkjJGZ4mYSqmWE+bUgfvkcPd5mHPwp2OZbM3icGOdZeQc98SB+uY/E1JwE+W1uCJ085GGH4FjvZw+OF8r7hXAD4j73/A/bpj37M2nv77p0B8plNpxObDMc2nU3lyaexH/FvU5vg75gC+b7B/ublzWw0GjngI4Y8NoeXRagUiwTwrDDJAZ/k1Is+noDnDekBIsxEvpN9ZSzMmjl8uEAammW5vCM/zBGgghApBXxco8Dr+SlBWczzd+4LASu/VsEkeAYxAQmBsATwtP4eqgsIAaL470k5/FHA4zsB+qU5/BIPH9eQPpsc8J5Du91SRCHDdnrAF7nR9BnzO0EGM0P/Gjl8Emw6f7AY0jN3pfHPuQ+BMDiJ/CJWAT43iNqg3A5uFFYCntFjwi/4bcX1nR7w+NKJ77OC2Who73vnr9jnH/mMjfoDPmd46gFz+QEdLACMX7r+Eg0OLhxGIONF4MpoGODZzYbj8ekBXy4WrZoAHvsdgIqFSx8z/haAL9PqKsw+DeBjk4R3AxlyesAf9fBzgCcKdX1FLGUapieAx3tgYE4LeH2ur8+/YsCToPJNsgj4zNY', NULL, 1, '\r\n### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'avatar\' at row 1\r\n### The error may exist in file [D:\\renda\\renda-back\\renda-core\\target\\classes\\mapper\\renda\\RdDeputyMapper.xml]\r\n### The error may involve com.renda.core.mapper.RdDeputyMapper.updateRdDeputy-Inline\r\n### The error occurred while setting parameters\r\n### SQL: update rd_deputy          SET type = ?,             name = ?,             nation = ?,             duty = ?,             birthday = ?,             phone = ?,             resume = ?,             avatar = ?,                                       create_time = ?,                          update_time = ?          where id = ?\r\n### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'avatar\' at row 1\n; Data truncation: Data too long for column \'avatar\' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'avatar\' at row 1', '2023-10-26 17:59:26', 102);
INSERT INTO `sys_oper_log` VALUES (76, '人大代表管理', 2, 'com.renda.core.controller.RdDeputyController.edit()', 'PUT', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"avatar\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAPwAAAD8CAYAAABTq8lnAAAAAXNSR0IArs4c6QAAIABJREFUeF7cvWesrel1HrZ2b6fdPo0cFomy5cAKnDhRDASKgPwI/CNGkMBKQZwAaUjgH05BrEhWYMCKHFmRRYmCZapQhRSbRJGUSIkih6JEatjrDDkznD5z587t556yew2e51nr+969z97n7HPnjqzkSJdzyy7f937vs8qznrXegr39SzMrFMxsZla0Iz8F/lv+gz9VbGbFQsGKVuB/V/3otWaVgvErCnwf3jXj9+Hfi/72Av4Kfy7mFxHfgX/j6wpTvrdUyF/Dz/D3xnXgmmd4E7+zYCUr6tZmuNWCFQolvXQ2tVLRbFaY+nXpc7N7nk2z6+T78DlF/BcfNdH9Fws2nc34X9wTPiHuBfec3YNfXHx2/t+pronrgWvTguh/8XdaM35Xtl5Tm+HffK3iGWTv9fXSNfm6LjwnfJ7WG2sx4zUXCnqu8f2Fmf+ea4971GdpDQvZuhd5w37tuGffH/P7BnslFkGvwf8X8ADiM/1Z8uv4eb5P8H1FrK3WvuTXzueM7yrGv8Tr/f3+urnP5zXke0h7Bben6+dTju/lfsnvolgs61qLvi9irfA+vBb/x6XwZ8p9r79Ln7vuT7/yNcnvFfc37vfsnT/zs/bE175hk9HYRsORTaYTm01nNh5rwxNTwEJR9zuZTm0yHdtkMuFzxH9nM+wV7PPYu3cB+CqBFIuaL0j2PH0h8CgB+HIsXEGbVMumi/7/HOD9IRLw2PTYMG5ICIoVgM9AlGwSPbQE8L7h7gXg8TWlBPDazMnmPQXgaRCwr/4VAR6Xiu9eCnisPY1wGEuA1MH3rwjwME5hKE4NeBi0QsGuPPecvfedv2ovPPm09XpdGg3sMzqXQtGmU33H1P9+MBi6p9Fr8APA0yiUCjQU+JDCnIeXKUoNs1vueVAH4GXX/Re90OLPzKoAPb/fI4hiwSp8PHcPeEUJ7m2WeHjd5d17+PAIiADySEQenl68WKTlLC+uFTelvGVY4HU9PA0FABrvdaN4ag9PR4FNAZCE15I7WBfwEfD9ZfHwqwCvRXYves8AH05I0VVEhXROKzw8/y1xgPK84YVP5+HxWdhXX/2zz9ofvPcDdu2VKzYaDG06mfAZlstlm06Nnnw8mdh0MrPZZCpPPjOb2pQRC/4M7899WinLWEwQIdPDC+cewc2hdj6kR3hcsGoBgRT+T2BniLWwoeJDBHgPV7ABixGS3X1Iz+9i2Oohp7Cdb2iE6ZkxKjAFmA/pfesDGCX8/viQXvtK3pxh2Bzg45v01BcBr7BNBiq8bLqmJ3t4vT8P6bHuboiWhfQLgA/jFd8fz2VZSM8IxdMgRfGnCOkjJGZ4mYSqmWE+bUgfvkcPd5mHPwp2OZbM3icGOdZeQc98SB+uY/E1JwE+W1uCJ085GGH4FjvZw+OF8r7hXAD4j73/A/bpj37M2nv77p0B8plNpxObDMc2nU3lyaexH/FvU5vg75gC+b7B/ublzWw0GjngI4Y8NoeXRagUiwTwrDDJAZ/k1Is+noDnDekBIsxEvpN9ZSzMmjl8uEAammW5vCM/zBGgghApBXxco8Dr+SlBWczzd+4LASu/VsEkeAYxAQmBsATwtP4eqgsIAaL470k5/FHA4zsB+qU5/BIPH9eQPpsc8J5Du91SRCHDdnrAF7nR9BnzO0EGM0P/Gjl8Emw6f7AY0jN3pfHPuQ+BMDiJ/CJWAT43iNqg3A5uFFYCntFjwi/4bcX1nR7w+NKJ77OC2Who73vnr9jnH/mMjfoDPmd46gFz+QEdLACMX7r+Eg0OLhxGIONF4MpoGODZzYbj8ekBXy4WrZoAHvsdgIqFSx8z/haAL9PqKsw+DeBjk4R3AxlyesAf9fBzgCcKdX1FLGUapieAx3tgYE4LeH2ur8+/YsCToPJNsgj4zNY', NULL, 1, '\r\n### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'avatar\' at row 1\r\n### The error may exist in file [D:\\renda\\renda-back\\renda-core\\target\\classes\\mapper\\renda\\RdDeputyMapper.xml]\r\n### The error may involve com.renda.core.mapper.RdDeputyMapper.updateRdDeputy-Inline\r\n### The error occurred while setting parameters\r\n### SQL: update rd_deputy          SET type = ?,             name = ?,             nation = ?,             duty = ?,             birthday = ?,             phone = ?,             resume = ?,             avatar = ?,                                       create_time = ?,                          update_time = ?          where id = ?\r\n### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'avatar\' at row 1\n; Data truncation: Data too long for column \'avatar\' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'avatar\' at row 1', '2023-10-26 18:01:02', 14);
INSERT INTO `sys_oper_log` VALUES (77, '人大代表管理', 2, 'com.renda.core.controller.RdDeputyController.edit()', 'PUT', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"avatar\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAPwAAAD8CAYAAABTq8lnAAAAAXNSR0IArs4c6QAAIABJREFUeF7cvWesrel1HrZ2b6fdPo0cFomy5cAKnDhRDASKgPwI/CNGkMBKQZwAaUjgH05BrEhWYMCKHFmRRYmCZapQhRSbRJGUSIkih6JEatjrDDkznD5z587t556yew2e51nr+969z97n7HPnjqzkSJdzyy7f937vs8qznrXegr39SzMrFMxsZla0Iz8F/lv+gz9VbGbFQsGKVuB/V/3otWaVgvErCnwf3jXj9+Hfi/72Av4Kfy7mFxHfgX/j6wpTvrdUyF/Dz/D3xnXgmmd4E7+zYCUr6tZmuNWCFQolvXQ2tVLRbFaY+nXpc7N7nk2z6+T78DlF/BcfNdH9Fws2nc34X9wTPiHuBfec3YNfXHx2/t+pronrgWvTguh/8XdaM35Xtl5Tm+HffK3iGWTv9fXSNfm6LjwnfJ7WG2sx4zUXCnqu8f2Fmf+ea4971GdpDQvZuhd5w37tuGffH/P7BnslFkGvwf8X8ADiM/1Z8uv4eb5P8H1FrK3WvuTXzueM7yrGv8Tr/f3+urnP5zXke0h7Bben6+dTju/lfsnvolgs61qLvi9irfA+vBb/x6XwZ8p9r79Ln7vuT7/yNcnvFfc37vfsnT/zs/bE175hk9HYRsORTaYTm01nNh5rwxNTwEJR9zuZTm0yHdtkMuFzxH9nM+wV7PPYu3cB+CqBFIuaL0j2PH0h8CgB+HIsXEGbVMumi/7/HOD9IRLw2PTYMG5ICIoVgM9AlGwSPbQE8L7h7gXg8TWlBPDazMnmPQXgaRCwr/4VAR6Xiu9eCnisPY1wGEuA1MH3rwjwME5hKE4NeBi0QsGuPPecvfedv2ovPPm09XpdGg3sMzqXQtGmU33H1P9+MBi6p9Fr8APA0yiUCjQU+JDCnIeXKUoNs1vueVAH4GXX/Re90OLPzKoAPb/fI4hiwSp8PHcPeEUJ7m2WeHjd5d17+PAIiADySEQenl68WKTlLC+uFTelvGVY4HU9PA0FABrvdaN4ag9PR4FNAZCE15I7WBfwEfD9ZfHwqwCvRXYves8AH05I0VVEhXROKzw8/y1xgPK84YVP5+HxWdhXX/2zz9ofvPcDdu2VKzYaDG06mfAZlstlm06Nnnw8mdh0MrPZZCpPPjOb2pQRC/4M7899WinLWEwQIdPDC+cewc2hdj6kR3hcsGoBgRT+T2BniLWwoeJDBHgPV7ABixGS3X1Iz+9i2Oohp7Cdb2iE6ZkxKjAFmA/pfesDGCX8/viQXvtK3pxh2Bzg45v01BcBr7BNBiq8bLqmJ3t4vT8P6bHuboiWhfQLgA/jFd8fz2VZSM8IxdMgRfGnCOkjJGZ4mYSqmWE+bUgfvkcPd5mHPwp2OZbM3icGOdZeQc98SB+uY/E1JwE+W1uCJ085GGH4FjvZw+OF8r7hXAD4j73/A/bpj37M2nv77p0B8plNpxObDMc2nU3lyaexH/FvU5vg75gC+b7B/ublzWw0GjngI4Y8NoeXRagUiwTwrDDJAZ/k1Is+noDnDekBIsxEvpN9ZSzMmjl8uEAammW5vCM/zBGgghApBXxco8Dr+SlBWczzd+4LASu/VsEkeAYxAQmBsATwtP4eqgsIAaL470k5/FHA4zsB+qU5/BIPH9eQPpsc8J5Du91SRCHDdnrAF7nR9BnzO0EGM0P/Gjl8Emw6f7AY0jN3pfHPuQ+BMDiJ/CJWAT43iNqg3A5uFFYCntFjwi/4bcX1nR7w+NKJ77OC2Who73vnr9jnH/mMjfoDPmd46gFz+QEdLACMX7r+Eg0OLhxGIONF4MpoGODZzYbj8ekBXy4WrZoAHvsdgIqFSx8z/haAL9PqKsw+DeBjk4R3AxlyesAf9fBzgCcKdX1FLGUapieAx3tgYE4LeH2ur8+/YsCToPJNsgj4zNY', NULL, 1, '\r\n### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'avatar\' at row 1\r\n### The error may exist in file [D:\\renda\\renda-back\\renda-core\\target\\classes\\mapper\\renda\\RdDeputyMapper.xml]\r\n### The error may involve com.renda.core.mapper.RdDeputyMapper.updateRdDeputy-Inline\r\n### The error occurred while setting parameters\r\n### SQL: update rd_deputy          SET type = ?,             name = ?,             nation = ?,             duty = ?,             birthday = ?,             phone = ?,             resume = ?,             avatar = ?,                                       create_time = ?,                          update_time = ?          where id = ?\r\n### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'avatar\' at row 1\n; Data truncation: Data too long for column \'avatar\' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'avatar\' at row 1', '2023-10-26 18:05:37', 265632);
INSERT INTO `sys_oper_log` VALUES (78, '人大代表管理', 1, 'com.renda.core.controller.RdDeputyController.add()', 'POST', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"createTime\":\"2023-10-27 10:19:59\",\"name\":\"a\",\"params\":{},\"phone\":\"1\",\"type\":0}', NULL, 1, '\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'dept_id\' doesn\'t have a default value\r\n### The error may exist in file [D:\\renda\\renda-back\\renda-core\\target\\classes\\mapper\\renda\\RdDeputyMapper.xml]\r\n### The error may involve com.renda.core.mapper.RdDeputyMapper.insertRdDeputy-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into rd_deputy          ( type,             name,                                                    phone,                                                                 create_time )           values ( ?,             ?,                                                    ?,                                                                 ? )\r\n### Cause: java.sql.SQLException: Field \'dept_id\' doesn\'t have a default value\n; Field \'dept_id\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'dept_id\' doesn\'t have a default value', '2023-10-27 10:19:59', 10028);
INSERT INTO `sys_oper_log` VALUES (79, '人大代表管理', 1, 'com.renda.core.controller.RdDeputyController.add()', 'POST', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"createTime\":\"2023-10-27 10:20:15\",\"name\":\"a\",\"params\":{},\"phone\":\"1\",\"type\":0}', NULL, 1, '\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'dept_id\' doesn\'t have a default value\r\n### The error may exist in file [D:\\renda\\renda-back\\renda-core\\target\\classes\\mapper\\renda\\RdDeputyMapper.xml]\r\n### The error may involve com.renda.core.mapper.RdDeputyMapper.insertRdDeputy-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into rd_deputy          ( type,             name,                                                    phone,                                                                 create_time )           values ( ?,             ?,                                                    ?,                                                                 ? )\r\n### Cause: java.sql.SQLException: Field \'dept_id\' doesn\'t have a default value\n; Field \'dept_id\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'dept_id\' doesn\'t have a default value', '2023-10-27 10:20:15', 3993);
INSERT INTO `sys_oper_log` VALUES (80, '人大代表管理', 1, 'com.renda.core.controller.RdDeputyController.add()', 'POST', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"avatar\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAALQAAAC0CAYAAAA9zQYyAAAAAXNSR0IArs4c6QAAIABJREFUeF6svVeUZNd1JbhfRLzwNiN9ZvlCwRQKBEAABAlQIEBSpEixKUpsmR6tNUuamZZW98x8ztf8z+f89KzpnlY3W02KokQrgRAp0ROGAAEasGAL5W26yPA+4sWsvc99kVkGQBWl5EpWIjPixTPnnrvPPvuc433g8y9PAMCbePznpr74yiAI4EUiN3j9BHCHmvDHYAIEI8QGHWy8+D2c+MbnEG1VEIsEGI9GGI7GmEwm8GM+EokEop6H0XCEWCSCWCyGYDwCgiFS8Qj2LBVxaGkBhYyPQi6DfDqJXDoNnkbUi2AwHKI/8vDS62dwaq0CeFH0+0NEYwk0uz1sVmsYjkfwozHwcuPxBDx4iEQ8BABGozEGwwEm8PQdjUYxHgVIJxKIeB7GkzGyuSwGoyHG4zFSqRQG/T76vQF4mZFYFL7vo9/rwfM8+H5Mx6huV8GbnEwmMR4HCIKx3s/XRLyIfh4HI/ixGDzeM4/fHnhSPDdMxsilEyjlM8gmYliYzWNxvoh0Io54LIKUH9PPhWwWmVQSUXugGIzH6A9GGAyG6A1H6PaH6PUH6PT66A+GaHf72K7WUWt20OsF6I/5PUZ/OEaz3bf3j0a6F7x+Plc+0wmvhj/oa5fdeB6iHhCLRhFMAiSSCT3XeCIBz08gnskjEo1jkisje9f7kL3zQfizqwj4PHidOha/w2PflDlOXz8JAO+Rz/9Sp0aD5v/sa4IJ7+z06+oP4WvHk4mMSK/Wa+31OwvD3jMJxhj1Ooi1tvHKV/4z2q/8FF6/CWCMiXuQfKixaEwGzZ8HgwFivEAuGkxQyiaxOlvAbfvmMV/KIJdJwI8AGT7EfAF+LCpD6XV6GE+i+MWJczi5vo1Wf4Rms41ef4RBAGxsV+FFI4jHfIwnQDKVwoQ/YILAm6DrDJHXMRpPZFhxP6nrTMTjCDDWNfoJX4uN59nr9TAeBojGYognbYHw98PhAPG4LyPmQ+Lvut2ebhNvW7/fnz4+/n0yCRCNRGU4suXA/nsSjJDLppH0PSRjEaTjURzev4I9q3NI+VEk/SgKmawMOpdMysBjsQgikQj6MuIBRqMAnW4fg9HI/a6Pbr+PLg2930e92UFlu4nuYIxxJIJGq4P+aILhEOgNxxj0BxjyWY3HiEYjOr/ReIzAGbUcVzSic6Yx81q4ECPRqJ5pMpHSPYgl04gmEhgnskgfOIaZ+x9DbOkgJvEUJpEYxrx7kQhu5CbfybT5eTwnfoYM+mbXQfg6M1reeD6IHeMP1xdfx4vVZ4xHmHTbGJ1/A8e//J8xvnIWwaCNcTDQhfP9fIi5dBaJVBKdZls3hTcuGI2QTkQwl0/j0Mos9i2WMJNPojyTQymXk0EnufojEX1eMA6wvd3A8bfO4/j5daxVOxiNA0R8H41GC73BEJ3eAJ4XRXcwQDqd0ee3ux34cV/HGPIpuuuKROjrokjHE/YwfXrMCKK+L89Lb8z/7nX6Ogbfn0qlEYtE0R/0MRz2MA6AdCql13mIYjDoc/nIoEejIeKxOGLRiBY3zyWqawl0T1L0cH4U0Ukg7xxDgHIxi6g3wuGDe7E8V0Yy5qGQSSObSmonScRiiPkRxPyoFmW92QJNpNcfajekh+4NzKDb3F1GQ/R6AzTbPfSGAbrDMTYq5rWrtQ4QjaPb62McTDAcD6emQmPlNfJeRaMx8Oq4Wrnwx8EYw9EQMd63YIxUPIVEIo5INALE4oim8ogv7kP2zoeQOHgMkfwcPBo9F4Ps6tZMesegI/A++AWDHFd/aSN+RzuXIdMYBTt2PLu88iQwg+bfgxFQ28Txb/x3tI4/h1FtHRFvgrFuDr1gRDAjk87IiLudLuic6ZmL+QyK2TgO75nH4dV5zGYTWCznMDdbQiYVRz6dQYQ3MxpBxIt', NULL, 1, '\r\n### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'avatar\' at row 1\r\n### The error may exist in file [D:\\renda\\renda-back\\renda-core\\target\\classes\\mapper\\renda\\RdDeputyMapper.xml]\r\n### The error may involve com.renda.core.mapper.RdDeputyMapper.insertRdDeputy-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into rd_deputy          ( type,             name,                                                    phone,                          avatar,                                       create_time )           values ( ?,             ?,                                                    ?,                          ?,                                       ? )\r\n### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'avatar\' at row 1\n; Data truncation: Data too long for column \'avatar\' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'avatar\' at row 1', '2023-10-27 10:20:40', 15);
INSERT INTO `sys_oper_log` VALUES (81, '人大代表管理', 1, 'com.renda.core.controller.RdDeputyController.add()', 'POST', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"avatar\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAALQAAAC0CAYAAAA9zQYyAAAAAXNSR0IArs4c6QAAIABJREFUeF6svVeUZNd1JbhfRLzwNiN9ZvlCwRQKBEAABAlQIEBSpEixKUpsmR6tNUuamZZW98x8ztf8z+f89KzpnlY3W02KokQrgRAp0ROGAAEasGAL5W26yPA+4sWsvc99kVkGQBWl5EpWIjPixTPnnrvPPvuc433g8y9PAMCbePznpr74yiAI4EUiN3j9BHCHmvDHYAIEI8QGHWy8+D2c+MbnEG1VEIsEGI9GGI7GmEwm8GM+EokEop6H0XCEWCSCWCyGYDwCgiFS8Qj2LBVxaGkBhYyPQi6DfDqJXDoNnkbUi2AwHKI/8vDS62dwaq0CeFH0+0NEYwk0uz1sVmsYjkfwozHwcuPxBDx4iEQ8BABGozEGwwEm8PQdjUYxHgVIJxKIeB7GkzGyuSwGoyHG4zFSqRQG/T76vQF4mZFYFL7vo9/rwfM8+H5Mx6huV8GbnEwmMR4HCIKx3s/XRLyIfh4HI/ixGDzeM4/fHnhSPDdMxsilEyjlM8gmYliYzWNxvoh0Io54LIKUH9PPhWwWmVQSUXugGIzH6A9GGAyG6A1H6PaH6PUH6PT66A+GaHf72K7WUWt20OsF6I/5PUZ/OEaz3bf3j0a6F7x+Plc+0wmvhj/oa5fdeB6iHhCLRhFMAiSSCT3XeCIBz08gnskjEo1jkisje9f7kL3zQfizqwj4PHidOha/w2PflDlOXz8JAO+Rz/9Sp0aD5v/sa4IJ7+z06+oP4WvHk4mMSK/Wa+31OwvD3jMJxhj1Ooi1tvHKV/4z2q/8FF6/CWCMiXuQfKixaEwGzZ8HgwFivEAuGkxQyiaxOlvAbfvmMV/KIJdJwI8AGT7EfAF+LCpD6XV6GE+i+MWJczi5vo1Wf4Rms41ef4RBAGxsV+FFI4jHfIwnQDKVwoQ/YILAm6DrDJHXMRpPZFhxP6nrTMTjCDDWNfoJX4uN59nr9TAeBojGYognbYHw98PhAPG4LyPmQ+Lvut2ebhNvW7/fnz4+/n0yCRCNRGU4suXA/nsSjJDLppH0PSRjEaTjURzev4I9q3NI+VEk/SgKmawMOpdMysBjsQgikQj6MuIBRqMAnW4fg9HI/a6Pbr+PLg2930e92UFlu4nuYIxxJIJGq4P+aILhEOgNxxj0BxjyWY3HiEYjOr/ReIzAGbUcVzSic6Yx81q4ECPRqJ5pMpHSPYgl04gmEhgnskgfOIaZ+x9DbOkgJvEUJpEYxrx7kQhu5CbfybT5eTwnfoYM+mbXQfg6M1reeD6IHeMP1xdfx4vVZ4xHmHTbGJ1/A8e//J8xvnIWwaCNcTDQhfP9fIi5dBaJVBKdZls3hTcuGI2QTkQwl0/j0Mos9i2WMJNPojyTQymXk0EnufojEX1eMA6wvd3A8bfO4/j5daxVOxiNA0R8H41GC73BEJ3eAJ4XRXcwQDqd0ee3ux34cV/HGPIpuuuKROjrokjHE/YwfXrMCKK+L89Lb8z/7nX6Ogbfn0qlEYtE0R/0MRz2MA6AdCql13mIYjDoc/nIoEejIeKxOGLRiBY3zyWqawl0T1L0cH4U0Ukg7xxDgHIxi6g3wuGDe7E8V0Yy5qGQSSObSmonScRiiPkRxPyoFmW92QJNpNcfajekh+4NzKDb3F1GQ/R6AzTbPfSGAbrDMTYq5rWrtQ4QjaPb62McTDAcD6emQmPlNfJeRaMx8Oq4Wrnwx8EYw9EQMd63YIxUPIVEIo5INALE4oim8ogv7kP2zoeQOHgMkfwcPBo9F4Ps6tZMesegI/A++AWDHFd/aSN+RzuXIdMYBTt2PLu88iQwg+bfgxFQ28Txb/x3tI4/h1FtHRFvgrFuDr1gRDAjk87IiLudLuic6ZmL+QyK2TgO75nH4dV5zGYTWCznMDdbQiYVRz6dQYQ3MxpBxIt', NULL, 1, '\r\n### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'avatar\' at row 1\r\n### The error may exist in file [D:\\renda\\renda-back\\renda-core\\target\\classes\\mapper\\renda\\RdDeputyMapper.xml]\r\n### The error may involve com.renda.core.mapper.RdDeputyMapper.insertRdDeputy-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into rd_deputy          ( type,             name,                                                    phone,                          avatar,                                       create_time )           values ( ?,             ?,                                                    ?,                          ?,                                       ? )\r\n### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'avatar\' at row 1\n; Data truncation: Data too long for column \'avatar\' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'avatar\' at row 1', '2023-10-27 10:20:54', 9434);
INSERT INTO `sys_oper_log` VALUES (82, '人大代表管理', 1, 'com.renda.core.controller.RdDeputyController.add()', 'POST', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"createTime\":\"2023-10-27 10:43:53\",\"name\":\"a\",\"params\":{},\"phone\":\"1\",\"type\":0}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-27 10:43:53', 5);
INSERT INTO `sys_oper_log` VALUES (83, '人大代表管理', 1, 'com.renda.core.controller.RdDeputyController.add()', 'POST', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"avatar\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFEAAABRCAYAAACqj0o2AAAAAXNSR0IArs4c6QAAIABJREFUeF5VfVmsZel51drzPvuM996auqurB7dtHDl26ESxEgEiJpN4IA+8IB6QCEiAeIkikIBECDJi5MRx7BASEQkFAYndNrYSo8TgOAQR8pAQuu1O3N3urip3DV11xzPveW+01vfv26GS61t17zn77P3937C+9Q3t3fv8R/seHjz08Hz+r/vOn/LL8/Tl8xW+D/4J4hh+EAKejyAI0Xke0PUIvAAeX+MDnhcAfI/noemAtusBL0Df9WjbVp9jf2/Q9z18n68HmrYF+h6BPsvT//OP5/l6XRD46MHPDfRvXov3Z6/hvfJaHnzfRxgFel0YRqjrCl3HJ+3RtQ08dPrSddzP+o4/A3zPQ9d26JrGvncd0POeGz6mXqPP0hU6ePc+/6/7vrMf8sPdK/St4y8oNM+XgE0OIcIolEA9P0IcJ/YeCTyw93lAz5uhYLwAnaQa6Gb4xddReHxx2zRo6kaHIVnwAbqOl9Mh6oeeJ4HrqhKUj6btEIehhEiBdV0LL/D1HBQ0nyUMQ/3OHhgoy1z36Qc+wNfz+fhvfQZ0T17X2+HwPvn5vf27a2t0Pe+Z99Kh04EGUh7v7uf+VU8t4x9+IC84aF9PBTN9cDcToJf2+fDDEPBDhH4IvU6n6G6YN8nrSNAmRL6P1+VDD+rVNC2CMEBbNyZ1qRPvmzdObWhNs9/5FfwgQF3XEiSf3eeH+9T0DlEU6d544HzAQUN5vSgIUJYF4HW6h5Bv7jt9tjs7aSSodU4T+fO2qd1tdaibCg0F7dkBmYb28G5/9qd6/pDSDf0AgR/ACwOnWTRhMw3dEE1UQgykZV4YSqUHbaHm+p5pAR9W5kzTixKZNR9UDxCGzhSpcRQYTcg34TjTrptOD8XP5mv0YI1pwvBHguIBOw3kdzNfarUdGL+b9pvWt22JMAjR06X0HULP12FVVan7llHRmpwmUi59R1Nu0XatNI9/JPBB4d548Sd4lvbBfBhpD0+X302jeGX6KD+IEUWJLsaH9njDMW84kC/TTVN0em2AMIj1956+kDdIjW27S9dAFaaAmqahUpiwnB+WJsqM+ADmbgY/GIQ+mqrmTcly+L7Beviay9e5v9fU9K6TkPq+kTD5iPystmlNSD0tiZ8/+GCZF5quocd0ftSdobwcf+o+9+5nf7qXFlI7ePLUJGqRBGkn8453p7NOqYzyYfw8CjEIYmmovFZrDoAHwWvRjPmTIIwUjChE873mvPnAFH7bdtLU3uvRtxbqBguhFps22d1Q480C7IFpwlEUX2rdoIGDRkormxZNQ00fBGbvlf+ra56HCZoHCU9mK3lKoxspDq2E99vUtWnzEAtuv/iT/eAH6Z/oa+R4ecKSo3lMnbZPU2EgobnSTCMEUSifJyHxte4kLQzobM1/+YyCFrwUJS+t8h0/yZOVkHgQzsHbgwJeaGZt92EmKI0fTIqW4zRWGu3+TkFS06m58qGe3RUtR7fQ9ejo92QK/FyaryGUwf0ItfB9cpmmJHwOBR/+350Xf1xBm2bMT6Eg6e8oQN0kf6439wjCEIEfSiAIPJ1+ELnoC0/+lF86gJ7XNMHp4RVonJ8UXLCb4mdQC03I7ib5egrSSVoRmPflghM1cTDhy6DjXsv7HSK2ZDRosIv4TV1IWENgaKuacckJkUHvnT+GEnq0feu0XlHvUoOlIBT4G5/6l6aJfiDt4oPLUcskGVCGSOuCjrTKorP8FOGO50u9h0iqj2HM10Pz/aaVcFjQ3LJFc/lEmrhzB/I1rdOGy/fSHQTCqi1NWe6ql4AGYfC++Ro+pOFB+xSTrQVOal1dl/xUKQbdFv1j39QWhRkY6cqoNL3Dh4zQPWOAuRR+V0wYLIpX//qn/kVPgfGkCUqleQAi38A0Ncj8DjEXNYlgmr4xsgA', NULL, 1, '\r\n### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'avatar\' at row 1\r\n### The error may exist in file [D:\\renda\\renda-back\\renda-core\\target\\classes\\mapper\\renda\\RdDeputyMapper.xml]\r\n### The error may involve com.renda.core.mapper.RdDeputyMapper.insertRdDeputy-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into rd_deputy          ( type,             name,                                                    phone,                          avatar,                                       create_time )           values ( ?,             ?,                                                    ?,                          ?,                                       ? )\r\n### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'avatar\' at row 1\n; Data truncation: Data too long for column \'avatar\' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'avatar\' at row 1', '2023-10-27 10:44:30', 5);
INSERT INTO `sys_oper_log` VALUES (84, '人大代表管理', 1, 'com.renda.core.controller.RdDeputyController.add()', 'POST', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"avatar\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAALQAAAC0CAYAAAA9zQYyAAAAAXNSR0IArs4c6QAAIABJREFUeF7MvVeQbcl1JbaOu96Vf/VMPdsO3WiHbhg6cAiQBB1AkABIeAxAgKBGGulHHzMxH1LERIz0OfMpfShCPyMpQjHSxFAKhkbUTJAACYIGQAMN192vXz9X3lx/73GKtTPz3LznnVtVDZIhFdisV/eekydP5tprm9y504mTNEX+xwHsDx35PoVjfZiqD+d/+H3WHC9I4TguUv4uuPw8H6W6PcdxYP59+n18Voqk+xA7f/I/I73/CrxJH5503oH0O+uMCzgO/0/6zf/Jl44Dl8/jg/invAk/1iNh7tENyXeuA7jm/dVNqb7eNMKx4GWm3RRu1qZc4+pnshn2IXWRsk15TsKHSN/4k/B95JFygW6T72JeMJu1+Wc4QBynCIM2vAvP4MKzv4TEr8s8Lfqx59rGQNH1vFbmm2MkM6HGxNynR1jNhVyhftTY69dbABZpTzVoTeLs36mTwikEtAwo5gA8/8dpkErnpUE6YbquO21ewnRcT5IaAC01puOPilvhw2eAVy/oIEYajTE9fICEgCYW0hlIFUgU8NTjzcCoTmVY1ECenwgN7rmL9BgbicneLZsxBSwlPUqQsj6of8mt6v/p8TfSpCfDgISzKl9pAdUSIo80QmdGSe6xfxwkvN4twal2ACeYA4gR3vOQzekCoAmEvVTdzYCrR9gM2GyADGlk75Cf/Fnv5BVyXwtmFwH60c5avTr326onqvGn6FJyhbdnE2jLmulggdI49yM1MBMA4wjojiNMYt33vKxlDKsfbAM0u9Z0Sg+moVcNnkxryIvZzGEm0AgLv844S0+uNCL4lsmwplaJtqZzGUCrfWoTV4tgfs5FaA2YZppRtAXb0URR9lKstQJUPAe+k8KlJv1bjfvsfYVcMh0vqkqRhGbimZKcXZXJnxlXSwpn/8yL5gwVGmkK0Od7D80Kus1z3jRTO7aCsTublzLTx/N1qhDnk8TBj3bHePM4wk4fGMZkMoUAoz6z5gkAAZPRDJban0mj3Kg0jZqaR9rJDBMzdQrICj9m0GYsJW0Y/AvOLJVrgdKCSab5jEw9MkRiNhldYwRA/Z2RSJrARYKaH+PaioerbR9PrpdQC+aF7fwEcvaVYnaKTKaZ0GTmm20+mPHV5p4NX3P9WU87FdAZKditZOrZMI/92BmbZWwzR1ozVWnw84i5pB96XoHJv2AM4K/fGuCPfjTB/b6HyPGROp6Bi7E2Z4yRJ+eMmRWfaF2CJHU16DVb2g/WZkbqUC+oexRYjfljOyWZpa6vUcBTfKxNINFqolSljew7ayxV8zanG7xrwcjszZmGMDqAwHLTBE4a4WItxoeeLOPd12vwzkLLT/x9bpaFoueNGxtWp5k9ZwE7Mzn+FoS4+DUzD0INMh2t7IfAERNEfTbnZAql5HXp+UZzmjr4l3/4Fn44aSNyyrrdTK1kjbhOisCJ4TuxYl3tdPLfYeJhmrjaqTOmhnh8mqUT5cApY1SbDfyOjlvmoWV+iFLAM2FXl+lr5VUV49MmpNNIP105lJ7GIMGXgE6k7RiaMZJHZs6z5cIUjKHtiPG1/WSM916M8cX3r6J0viH+Ca5Sjnc2o5bCmmPhc0z5IkBn729saBnwwtDFT9T/mf+ve3we73juSZm9d/7nczy6E+Cf/S9v4jhYAU1nBVTDTZod0xSNUoyNygQVd6KZl9cl6DTLGKcl/GjPQ29KQCnbkmwoQoAprq2XsH08Qr3i4aDHh3i40Hax3i4jioH+OMb2SQjPS1BxEwRIUCl5OBzEmCYeLi1X0KyqqAhhun0coVEF2kEJjbKLwSTF0TTEnZ0YF1c99HtDLDd83N8PAb+M7lhpHBXQMAKnx2lOYVo2jf7a6AzjELtpjKeWpvjHv7iOdnnOLT//wJ95pTbUZuZ0Zkk', NULL, 1, '\r\n### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'avatar\' at row 1\r\n### The error may exist in file [D:\\renda\\renda-back\\renda-core\\target\\classes\\mapper\\renda\\RdDeputyMapper.xml]\r\n### The error may involve com.renda.core.mapper.RdDeputyMapper.insertRdDeputy-Inline\r\n### The error occurred while setting parameters\r\n### SQL: insert into rd_deputy          ( type,             name,                                                    phone,                          avatar,                                       create_time )           values ( ?,             ?,                                                    ?,                          ?,                                       ? )\r\n### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'avatar\' at row 1\n; Data truncation: Data too long for column \'avatar\' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Data too long for column \'avatar\' at row 1', '2023-10-27 11:16:00', 1742457);
INSERT INTO `sys_oper_log` VALUES (85, '人大代表管理', 1, 'com.renda.core.controller.RdDeputyController.add()', 'POST', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"avatar\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAACWCAYAAAA8AXHiAAAAAXNSR0IArs4c6QAAIABJREFUeF7svWmPJNmVpnfMzHePPSL3yqxiLVyb7G5qNJIAAQL0J6dbM1rmiyAIkH6C9GEGmhHU000Om0OyilXFrNwzMnbfFzPhec+9ZuYeHllVLZItNNPIrMj0MLfl3nPP8p73nJv8VdIr7N3x//sRSDRLhfGDP0mS3PjMaWGWWGG5/ss/bj43XiQpCmuG8+dWWGLhO0lihW747YYoeSdY327A/qhnF4ULUJjYGzVAOA/hSM1swUO+TZg4P7xImpilRWKLILTfRAi/yRi8E6xvMkr/COdIQ4XZf5tAIUj6PZrlpudE5SSug7ICTVYJnv4eDn5/7RpSV/GESshdiBMJMt8rNZxOLeydYP0jCM2NtwyTxe/zt5mwwiyTcBS2KEwTXNxkqoJgtAytlNtSIpB8Y9NWBBPJ9zGrMytsGSWwvOfqzbUm3pnCf3zJYvX78RYBMUyWe1lYOfyntx1ZPDcIRPE2jRYuxHe4KgLYlC4qbCEl9S0drHeC9Y8vVAjLTdqpnM6ikIa6yXdCq0RfrBEcMrTKW6Oy4JGnVhjaCIc9D37ZPySaK58hLpF3GusfT7jwo2TCNkRdEqrgjH9ddIduiedG4Vuf6LofpVsWLlDTmvghnN9EqPh+9K1ilJpHrRuE850p/CPLlYRJ5uyGSQzaKTrYNzvkZg18seDlM683+lly2vHLCptb4tqvHh28bQyKwhqWWDN8B8FNCnfaiSiXhd4mwCAVtPFOsP4YgqXwnv9V5ubabcOKZ9KXX4M7EdnJF3rLeWgkTb4l0kxjYsG3nF8qTkWNhXUtlQCOk8JyJPZbulnvBOsPJVhB1fh8+KreODkBzPSJDVpswyTii3EIp3K09DpqGT5Gky3NNQ0aBkG5BqjWVCHnc6Al+Tv+Fs6+28W3AKybTLg08ruo8A8lVvJhMks0qTch5WnQPG/zoTgHYZIWC47+Tah7u0DozPIwuW/VMkVhHUtsGQRJgv0NEPqVAcMMCmB1gcRcZklqSZG/gxv+IJIVwnYHLq/fAXghTuQmHypqp+gLvS3c51wiOwzt/C1+k4Oj7pO1hLQ7rvVNkHZMr4Q7mPN2gEPRbK4RgyGtCeY7U/h7lKwyn7e28mOEJoEKuNAmgeL7TGAerGYdFS8fM/hr0WSlCVpx8yFNqejPzRMaiXNvvK7ygv5k5A25B1GjosDEtZt/9+uTh+8E6/ckWEzg20xJTKXE9Msm5114kkzZBp/IgXZpJ3ynEia4wXxxbjOAB7MQgb7N/3YIIrctS2xmZnwHCOMmM/51w/ZOsL5uhL7m99GP/jqzhxZat4vKsZV+1s0+jvtiHlWiNW4SYJ4F7URCmbPxt24+CvlG+EVz8+9Nv62fFfxINBtmmzEgaJDJfAeQ/sMlqxSqtQmM5qSh1MsNKRFycEmiXN9N+BMT1UV7BG14k/lEaDlvHs7bbOqq90QQupbY0DH/r3faZR5lpCWAfWm1wsbBdNZzj2W8+k6w/gGCFZxzeRtrQhURc37Kqd30e8yZHOjNQsXqj3748gbBkzMeziuSwpbFzVBFdNwxbDje429AkeE7COAiIPStxPS9efADS615g7v1TmN9S7nyhPHmZLFHc4GMt+77KCR3ykpc1esmDdEgcsPDv1GgwnVipIjZuymyQzg7ZjbhukALScDTNryzAgxLrK1zzSaJC65r1G+Jjr7Dsb6ZVEXTdhM9xTEr96HwMeo4k5z6uPp1yuokRXOKduDMG9F0pVZ8svGFNglTFZWabRepTZJcKZy3HR6pJgaPGChiFAT7a0em5HgF6CRgZxhMgot3GutrRzCAz5vMVojSYqRX94GiKYuEuE2aBQ2FScTTEUh6A+LOeQr3I0lvRbD8rgi9cKoQIngyepNq8s8wbRHEhQmBA1/p0nWnsbo', NULL, 1, 'Illegal base64 character 3a', '2023-10-27 12:07:01', 8);
INSERT INTO `sys_oper_log` VALUES (86, '人大代表管理', 1, 'com.renda.core.controller.RdDeputyController.add()', 'POST', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"avatar\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAACWCAYAAAA8AXHiAAAAAXNSR0IArs4c6QAAIABJREFUeF7svWmPJNmVpnfMzHePPSL3yqxiLVyb7G5qNJIAAQL0J6dbM1rmiyAIkH6C9GEGmhHU000Om0OyilXFrNwzMnbfFzPhec+9ZuYeHllVLZItNNPIrMj0MLfl3nPP8p73nJv8VdIr7N3x//sRSDRLhfGDP0mS3PjMaWGWWGG5/ss/bj43XiQpCmuG8+dWWGLhO0lihW747YYoeSdY327A/qhnF4ULUJjYGzVAOA/hSM1swUO+TZg4P7xImpilRWKLILTfRAi/yRi8E6xvMkr/COdIQ4XZf5tAIUj6PZrlpudE5SSug7ICTVYJnv4eDn5/7RpSV/GESshdiBMJMt8rNZxOLeydYP0jCM2NtwyTxe/zt5mwwiyTcBS2KEwTXNxkqoJgtAytlNtSIpB8Y9NWBBPJ9zGrMytsGSWwvOfqzbUm3pnCf3zJYvX78RYBMUyWe1lYOfyntx1ZPDcIRPE2jRYuxHe4KgLYlC4qbCEl9S0drHeC9Y8vVAjLTdqpnM6ikIa6yXdCq0RfrBEcMrTKW6Oy4JGnVhjaCIc9D37ZPySaK58hLpF3GusfT7jwo2TCNkRdEqrgjH9ddIduiedG4Vuf6LofpVsWLlDTmvghnN9EqPh+9K1ilJpHrRuE850p/CPLlYRJ5uyGSQzaKTrYNzvkZg18seDlM683+lly2vHLCptb4tqvHh28bQyKwhqWWDN8B8FNCnfaiSiXhd4mwCAVtPFOsP4YgqXwnv9V5ubabcOKZ9KXX4M7EdnJF3rLeWgkTb4l0kxjYsG3nF8qTkWNhXUtlQCOk8JyJPZbulnvBOsPJVhB1fh8+KreODkBzPSJDVpswyTii3EIp3K09DpqGT5Gky3NNQ0aBkG5BqjWVCHnc6Al+Tv+Fs6+28W3AKybTLg08ruo8A8lVvJhMks0qTch5WnQPG/zoTgHYZIWC47+Tah7u0DozPIwuW/VMkVhHUtsGQRJgv0NEPqVAcMMCmB1gcRcZklqSZG/gxv+IJIVwnYHLq/fAXghTuQmHypqp+gLvS3c51wiOwzt/C1+k4Oj7pO1hLQ7rvVNkHZMr4Q7mPN2gEPRbK4RgyGtCeY7U/h7lKwyn7e28mOEJoEKuNAmgeL7TGAerGYdFS8fM/hr0WSlCVpx8yFNqejPzRMaiXNvvK7ygv5k5A25B1GjosDEtZt/9+uTh+8E6/ckWEzg20xJTKXE9Msm5114kkzZBp/IgXZpJ3ynEia4wXxxbjOAB7MQgb7N/3YIIrctS2xmZnwHCOMmM/51w/ZOsL5uhL7m99GP/jqzhxZat4vKsZV+1s0+jvtiHlWiNW4SYJ4F7URCmbPxt24+CvlG+EVz8+9Nv62fFfxINBtmmzEgaJDJfAeQ/sMlqxSqtQmM5qSh1MsNKRFycEmiXN9N+BMT1UV7BG14k/lEaDlvHs7bbOqq90QQupbY0DH/r3faZR5lpCWAfWm1wsbBdNZzj2W8+k6w/gGCFZxzeRtrQhURc37Kqd30e8yZHOjNQsXqj3748gbBkzMeziuSwpbFzVBFdNwxbDje429AkeE7COAiIPStxPS9efADS615g7v1TmN9S7nyhPHmZLFHc4GMt+77KCR3ykpc1esmDdEgcsPDv1GgwnVipIjZuymyQzg7ZjbhukALScDTNryzAgxLrK1zzSaJC65r1G+Jjr7Dsb6ZVEXTdhM9xTEr96HwMeo4k5z6uPp1yuokRXOKduDMG9F0pVZ8svGFNglTFZWabRepTZJcKZy3HR6pJgaPGChiFAT7a0em5HgF6CRgZxhMgot3GutrRzCAz5vMVojSYqRX94GiKYuEuE2aBQ2FScTTEUh6A+LOeQr3I0lvRbD8rgi9cKoQIngyepNq8s8wbRHEhQmBA1/p0nWnsbo', NULL, 1, 'Illegal base64 character 3a', '2023-10-27 12:07:08', 1);
INSERT INTO `sys_oper_log` VALUES (87, '人大代表管理', 1, 'com.renda.core.controller.RdDeputyController.add()', 'POST', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"avatar\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAACWCAYAAAA8AXHiAAAAAXNSR0IArs4c6QAAIABJREFUeF7svWmPJNmVpnfMzHePPSL3yqxiLVyb7G5qNJIAAQL0J6dbM1rmiyAIkH6C9GEGmhHU000Om0OyilXFrNwzMnbfFzPhec+9ZuYeHllVLZItNNPIrMj0MLfl3nPP8p73nJv8VdIr7N3x//sRSDRLhfGDP0mS3PjMaWGWWGG5/ss/bj43XiQpCmuG8+dWWGLhO0lihW747YYoeSdY327A/qhnF4ULUJjYGzVAOA/hSM1swUO+TZg4P7xImpilRWKLILTfRAi/yRi8E6xvMkr/COdIQ4XZf5tAIUj6PZrlpudE5SSug7ICTVYJnv4eDn5/7RpSV/GESshdiBMJMt8rNZxOLeydYP0jCM2NtwyTxe/zt5mwwiyTcBS2KEwTXNxkqoJgtAytlNtSIpB8Y9NWBBPJ9zGrMytsGSWwvOfqzbUm3pnCf3zJYvX78RYBMUyWe1lYOfyntx1ZPDcIRPE2jRYuxHe4KgLYlC4qbCEl9S0drHeC9Y8vVAjLTdqpnM6ikIa6yXdCq0RfrBEcMrTKW6Oy4JGnVhjaCIc9D37ZPySaK58hLpF3GusfT7jwo2TCNkRdEqrgjH9ddIduiedG4Vuf6LofpVsWLlDTmvghnN9EqPh+9K1ilJpHrRuE850p/CPLlYRJ5uyGSQzaKTrYNzvkZg18seDlM683+lly2vHLCptb4tqvHh28bQyKwhqWWDN8B8FNCnfaiSiXhd4mwCAVtPFOsP4YgqXwnv9V5ubabcOKZ9KXX4M7EdnJF3rLeWgkTb4l0kxjYsG3nF8qTkWNhXUtlQCOk8JyJPZbulnvBOsPJVhB1fh8+KreODkBzPSJDVpswyTii3EIp3K09DpqGT5Gky3NNQ0aBkG5BqjWVCHnc6Al+Tv+Fs6+28W3AKybTLg08ruo8A8lVvJhMks0qTch5WnQPG/zoTgHYZIWC47+Tah7u0DozPIwuW/VMkVhHUtsGQRJgv0NEPqVAcMMCmB1gcRcZklqSZG/gxv+IJIVwnYHLq/fAXghTuQmHypqp+gLvS3c51wiOwzt/C1+k4Oj7pO1hLQ7rvVNkHZMr4Q7mPN2gEPRbK4RgyGtCeY7U/h7lKwyn7e28mOEJoEKuNAmgeL7TGAerGYdFS8fM/hr0WSlCVpx8yFNqejPzRMaiXNvvK7ygv5k5A25B1GjosDEtZt/9+uTh+8E6/ckWEzg20xJTKXE9Msm5114kkzZBp/IgXZpJ3ynEia4wXxxbjOAB7MQgb7N/3YIIrctS2xmZnwHCOMmM/51w/ZOsL5uhL7m99GP/jqzhxZat4vKsZV+1s0+jvtiHlWiNW4SYJ4F7URCmbPxt24+CvlG+EVz8+9Nv62fFfxINBtmmzEgaJDJfAeQ/sMlqxSqtQmM5qSh1MsNKRFycEmiXN9N+BMT1UV7BG14k/lEaDlvHs7bbOqq90QQupbY0DH/r3faZR5lpCWAfWm1wsbBdNZzj2W8+k6w/gGCFZxzeRtrQhURc37Kqd30e8yZHOjNQsXqj3748gbBkzMeziuSwpbFzVBFdNwxbDje429AkeE7COAiIPStxPS9efADS615g7v1TmN9S7nyhPHmZLFHc4GMt+77KCR3ykpc1esmDdEgcsPDv1GgwnVipIjZuymyQzg7ZjbhukALScDTNryzAgxLrK1zzSaJC65r1G+Jjr7Dsb6ZVEXTdhM9xTEr96HwMeo4k5z6uPp1yuokRXOKduDMG9F0pVZ8svGFNglTFZWabRepTZJcKZy3HR6pJgaPGChiFAT7a0em5HgF6CRgZxhMgot3GutrRzCAz5vMVojSYqRX94GiKYuEuE2aBQ2FScTTEUh6A+LOeQr3I0lvRbD8rgi9cKoQIngyepNq8s8wbRHEhQmBA1/p0nWnsbo', NULL, 1, 'Illegal base64 character 3a', '2023-10-27 12:07:47', 1);
INSERT INTO `sys_oper_log` VALUES (88, '人大代表管理', 1, 'com.renda.core.controller.RdDeputyController.add()', 'POST', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"avatar\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJYAAACWCAYAAAA8AXHiAAAAAXNSR0IArs4c6QAAIABJREFUeF7svWmPJNmVpnfMzHePPSL3yqxiLVyb7G5qNJIAAQL0J6dbM1rmiyAIkH6C9GEGmhHU000Om0OyilXFrNwzMnbfFzPhec+9ZuYeHllVLZItNNPIrMj0MLfl3nPP8p73nJv8VdIr7N3x//sRSDRLhfGDP0mS3PjMaWGWWGG5/ss/bj43XiQpCmuG8+dWWGLhO0lihW747YYoeSdY327A/qhnF4ULUJjYGzVAOA/hSM1swUO+TZg4P7xImpilRWKLILTfRAi/yRi8E6xvMkr/COdIQ4XZf5tAIUj6PZrlpudE5SSug7ICTVYJnv4eDn5/7RpSV/GESshdiBMJMt8rNZxOLeydYP0jCM2NtwyTxe/zt5mwwiyTcBS2KEwTXNxkqoJgtAytlNtSIpB8Y9NWBBPJ9zGrMytsGSWwvOfqzbUm3pnCf3zJYvX78RYBMUyWe1lYOfyntx1ZPDcIRPE2jRYuxHe4KgLYlC4qbCEl9S0drHeC9Y8vVAjLTdqpnM6ikIa6yXdCq0RfrBEcMrTKW6Oy4JGnVhjaCIc9D37ZPySaK58hLpF3GusfT7jwo2TCNkRdEqrgjH9ddIduiedG4Vuf6LofpVsWLlDTmvghnN9EqPh+9K1ilJpHrRuE850p/CPLlYRJ5uyGSQzaKTrYNzvkZg18seDlM683+lly2vHLCptb4tqvHh28bQyKwhqWWDN8B8FNCnfaiSiXhd4mwCAVtPFOsP4YgqXwnv9V5ubabcOKZ9KXX4M7EdnJF3rLeWgkTb4l0kxjYsG3nF8qTkWNhXUtlQCOk8JyJPZbulnvBOsPJVhB1fh8+KreODkBzPSJDVpswyTii3EIp3K09DpqGT5Gky3NNQ0aBkG5BqjWVCHnc6Al+Tv+Fs6+28W3AKybTLg08ruo8A8lVvJhMks0qTch5WnQPG/zoTgHYZIWC47+Tah7u0DozPIwuW/VMkVhHUtsGQRJgv0NEPqVAcMMCmB1gcRcZklqSZG/gxv+IJIVwnYHLq/fAXghTuQmHypqp+gLvS3c51wiOwzt/C1+k4Oj7pO1hLQ7rvVNkHZMr4Q7mPN2gEPRbK4RgyGtCeY7U/h7lKwyn7e28mOEJoEKuNAmgeL7TGAerGYdFS8fM/hr0WSlCVpx8yFNqejPzRMaiXNvvK7ygv5k5A25B1GjosDEtZt/9+uTh+8E6/ckWEzg20xJTKXE9Msm5114kkzZBp/IgXZpJ3ynEia4wXxxbjOAB7MQgb7N/3YIIrctS2xmZnwHCOMmM/51w/ZOsL5uhL7m99GP/jqzhxZat4vKsZV+1s0+jvtiHlWiNW4SYJ4F7URCmbPxt24+CvlG+EVz8+9Nv62fFfxINBtmmzEgaJDJfAeQ/sMlqxSqtQmM5qSh1MsNKRFycEmiXN9N+BMT1UV7BG14k/lEaDlvHs7bbOqq90QQupbY0DH/r3faZR5lpCWAfWm1wsbBdNZzj2W8+k6w/gGCFZxzeRtrQhURc37Kqd30e8yZHOjNQsXqj3748gbBkzMeziuSwpbFzVBFdNwxbDje429AkeE7COAiIPStxPS9efADS615g7v1TmN9S7nyhPHmZLFHc4GMt+77KCR3ykpc1esmDdEgcsPDv1GgwnVipIjZuymyQzg7ZjbhukALScDTNryzAgxLrK1zzSaJC65r1G+Jjr7Dsb6ZVEXTdhM9xTEr96HwMeo4k5z6uPp1yuokRXOKduDMG9F0pVZ8svGFNglTFZWabRepTZJcKZy3HR6pJgaPGChiFAT7a0em5HgF6CRgZxhMgot3GutrRzCAz5vMVojSYqRX94GiKYuEuE2aBQ2FScTTEUh6A+LOeQr3I0lvRbD8rgi9cKoQIngyepNq8s8wbRHEhQmBA1/p0nWnsbo', NULL, 1, 'Illegal base64 character 3a', '2023-10-27 12:08:24', 26132);
INSERT INTO `sys_oper_log` VALUES (89, '人大代表管理', 1, 'com.renda.core.controller.RdDeputyController.add()', 'POST', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"avatar\":\"D:/renda/uploadPath/upload\\\\20231027\\\\234ffed2b494479d90cc4c744f5b2a20.png\",\"createTime\":\"2023-10-27 12:09:45\",\"name\":\"d\",\"params\":{},\"phone\":\"4\",\"type\":2}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-27 12:09:45', 15695);
INSERT INTO `sys_oper_log` VALUES (90, '人大代表管理', 1, 'com.renda.core.controller.RdDeputyController.add()', 'POST', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"avatar\":\"D:/renda/uploadPath/upload\\\\20231027\\\\413641ec5fd14869ad76d57f3972ff67.png\",\"createTime\":\"2023-10-27 12:12:26\",\"name\":\"e\",\"params\":{},\"phone\":\"5\",\"type\":0}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-27 12:12:26', 6);
INSERT INTO `sys_oper_log` VALUES (91, '人大代表管理', 1, 'com.renda.core.controller.RdDeputyController.add()', 'POST', 1, 'admin', NULL, '/renda/deputy', '127.0.0.1', '内网IP', '{\"avatar\":\"/profile/upload\\\\20231027/df44f286140340cbb9863bd696054093.png\",\"createTime\":\"2023-10-27 12:19:57\",\"name\":\"f\",\"params\":{},\"phone\":\"6\",\"type\":1}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2023-10-27 12:19:57', 63);

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_post`;
CREATE TABLE `sys_post`  (
  `post_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位名称',
  `post_sort` int(4) NOT NULL COMMENT '显示顺序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '岗位信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_post
-- ----------------------------
INSERT INTO `sys_post` VALUES (1, 'manager', '管理员岗', 1, '0', 'admin', '2023-10-16 10:30:55', 'admin', '2023-10-20 18:11:39', '');
INSERT INTO `sys_post` VALUES (2, 'common', '普通岗', 2, '0', 'admin', '2023-10-16 10:30:55', 'admin', '2023-10-20 18:12:36', '');

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `role_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色权限字符串',
  `role_sort` int(4) NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '部门树选择项是否关联显示',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, '超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 'admin', '2023-10-16 10:30:55', '', NULL, '超级管理员');
INSERT INTO `sys_role` VALUES (2, '普通角色', 'common', 2, '2', 1, 1, '0', '0', 'admin', '2023-10-16 10:30:55', 'admin', '2023-10-20 13:35:09', '普通角色');
INSERT INTO `sys_role` VALUES (3, '伊州区管理员', 'yzq_all', 0, '4', 1, 1, '0', '0', 'admin', '2023-10-24 13:18:40', '', '2023-10-24 13:18:49', NULL);
INSERT INTO `sys_role` VALUES (4, '巴里坤管理员', 'blk_all', 0, '4', 1, 1, '0', '0', 'admin', '2023-10-24 13:19:16', '', '2023-10-24 13:19:28', NULL);

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept`  (
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `dept_id` bigint(20) NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`, `dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色和部门关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_dept
-- ----------------------------
INSERT INTO `sys_role_dept` VALUES (2, 100);
INSERT INTO `sys_role_dept` VALUES (2, 101);
INSERT INTO `sys_role_dept` VALUES (2, 105);

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `menu_id` bigint(20) NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色和菜单关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (2, 1);
INSERT INTO `sys_role_menu` VALUES (2, 2);
INSERT INTO `sys_role_menu` VALUES (2, 3);
INSERT INTO `sys_role_menu` VALUES (2, 100);
INSERT INTO `sys_role_menu` VALUES (2, 101);
INSERT INTO `sys_role_menu` VALUES (2, 102);
INSERT INTO `sys_role_menu` VALUES (2, 103);
INSERT INTO `sys_role_menu` VALUES (2, 104);
INSERT INTO `sys_role_menu` VALUES (2, 105);
INSERT INTO `sys_role_menu` VALUES (2, 106);
INSERT INTO `sys_role_menu` VALUES (2, 107);
INSERT INTO `sys_role_menu` VALUES (2, 108);
INSERT INTO `sys_role_menu` VALUES (2, 109);
INSERT INTO `sys_role_menu` VALUES (2, 110);
INSERT INTO `sys_role_menu` VALUES (2, 111);
INSERT INTO `sys_role_menu` VALUES (2, 112);
INSERT INTO `sys_role_menu` VALUES (2, 113);
INSERT INTO `sys_role_menu` VALUES (2, 114);
INSERT INTO `sys_role_menu` VALUES (2, 115);
INSERT INTO `sys_role_menu` VALUES (2, 116);
INSERT INTO `sys_role_menu` VALUES (2, 117);
INSERT INTO `sys_role_menu` VALUES (2, 500);
INSERT INTO `sys_role_menu` VALUES (2, 501);
INSERT INTO `sys_role_menu` VALUES (2, 1000);
INSERT INTO `sys_role_menu` VALUES (2, 1001);
INSERT INTO `sys_role_menu` VALUES (2, 1002);
INSERT INTO `sys_role_menu` VALUES (2, 1003);
INSERT INTO `sys_role_menu` VALUES (2, 1004);
INSERT INTO `sys_role_menu` VALUES (2, 1005);
INSERT INTO `sys_role_menu` VALUES (2, 1006);
INSERT INTO `sys_role_menu` VALUES (2, 1007);
INSERT INTO `sys_role_menu` VALUES (2, 1008);
INSERT INTO `sys_role_menu` VALUES (2, 1009);
INSERT INTO `sys_role_menu` VALUES (2, 1010);
INSERT INTO `sys_role_menu` VALUES (2, 1011);
INSERT INTO `sys_role_menu` VALUES (2, 1012);
INSERT INTO `sys_role_menu` VALUES (2, 1013);
INSERT INTO `sys_role_menu` VALUES (2, 1014);
INSERT INTO `sys_role_menu` VALUES (2, 1015);
INSERT INTO `sys_role_menu` VALUES (2, 1016);
INSERT INTO `sys_role_menu` VALUES (2, 1017);
INSERT INTO `sys_role_menu` VALUES (2, 1018);
INSERT INTO `sys_role_menu` VALUES (2, 1019);
INSERT INTO `sys_role_menu` VALUES (2, 1020);
INSERT INTO `sys_role_menu` VALUES (2, 1021);
INSERT INTO `sys_role_menu` VALUES (2, 1022);
INSERT INTO `sys_role_menu` VALUES (2, 1023);
INSERT INTO `sys_role_menu` VALUES (2, 1024);
INSERT INTO `sys_role_menu` VALUES (2, 1025);
INSERT INTO `sys_role_menu` VALUES (2, 1026);
INSERT INTO `sys_role_menu` VALUES (2, 1027);
INSERT INTO `sys_role_menu` VALUES (2, 1028);
INSERT INTO `sys_role_menu` VALUES (2, 1029);
INSERT INTO `sys_role_menu` VALUES (2, 1030);
INSERT INTO `sys_role_menu` VALUES (2, 1031);
INSERT INTO `sys_role_menu` VALUES (2, 1032);
INSERT INTO `sys_role_menu` VALUES (2, 1033);
INSERT INTO `sys_role_menu` VALUES (2, 1034);
INSERT INTO `sys_role_menu` VALUES (2, 1035);
INSERT INTO `sys_role_menu` VALUES (2, 1036);
INSERT INTO `sys_role_menu` VALUES (2, 1037);
INSERT INTO `sys_role_menu` VALUES (2, 1038);
INSERT INTO `sys_role_menu` VALUES (2, 1039);
INSERT INTO `sys_role_menu` VALUES (2, 1040);
INSERT INTO `sys_role_menu` VALUES (2, 1041);
INSERT INTO `sys_role_menu` VALUES (2, 1042);
INSERT INTO `sys_role_menu` VALUES (2, 1043);
INSERT INTO `sys_role_menu` VALUES (2, 1044);
INSERT INTO `sys_role_menu` VALUES (2, 1045);
INSERT INTO `sys_role_menu` VALUES (2, 1046);
INSERT INTO `sys_role_menu` VALUES (2, 1047);
INSERT INTO `sys_role_menu` VALUES (2, 1048);
INSERT INTO `sys_role_menu` VALUES (2, 1049);
INSERT INTO `sys_role_menu` VALUES (2, 1050);
INSERT INTO `sys_role_menu` VALUES (2, 1051);
INSERT INTO `sys_role_menu` VALUES (2, 1052);
INSERT INTO `sys_role_menu` VALUES (2, 1053);
INSERT INTO `sys_role_menu` VALUES (2, 1054);
INSERT INTO `sys_role_menu` VALUES (2, 1055);
INSERT INTO `sys_role_menu` VALUES (2, 1056);
INSERT INTO `sys_role_menu` VALUES (2, 1057);
INSERT INTO `sys_role_menu` VALUES (2, 1058);
INSERT INTO `sys_role_menu` VALUES (2, 1059);
INSERT INTO `sys_role_menu` VALUES (2, 1060);
INSERT INTO `sys_role_menu` VALUES (3, 1061);
INSERT INTO `sys_role_menu` VALUES (3, 1062);
INSERT INTO `sys_role_menu` VALUES (3, 1063);
INSERT INTO `sys_role_menu` VALUES (3, 1064);
INSERT INTO `sys_role_menu` VALUES (3, 1065);
INSERT INTO `sys_role_menu` VALUES (3, 1066);
INSERT INTO `sys_role_menu` VALUES (4, 1061);
INSERT INTO `sys_role_menu` VALUES (4, 1062);
INSERT INTO `sys_role_menu` VALUES (4, 1063);
INSERT INTO `sys_role_menu` VALUES (4, 1064);
INSERT INTO `sys_role_menu` VALUES (4, 1065);
INSERT INTO `sys_role_menu` VALUES (4, 1066);

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `user_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint(20) NULL DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '手机号码',
  `sex` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '密码',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime(0) NULL DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, 100, 'admin', '系统管理员', '00', '<EMAIL>', '13888888888', '0', '/profile/avatar/2023/10/26/blob_20231026125200A004.png', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', '2023-10-27 13:01:01', 'admin', '2023-10-16 10:30:55', '', '2023-10-27 13:01:01', '管理员');
INSERT INTO `sys_user` VALUES (2, 110, 'test', '测试人员', '00', '<EMAIL>', '13999999999', '0', '', '$2a$10$ec2R/HtyAhbegWpohyZSPewMOdq9OyxOheSWNu.ZOcuYbqST5FbvG', '0', '0', '127.0.0.1', '2023-10-24 17:17:23', 'admin', '2023-10-16 10:30:55', 'admin', '2023-10-24 17:17:23', '测试员');
INSERT INTO `sys_user` VALUES (3, 111, 'blktest', '巴里坤管理员', '00', '', '', '0', '', '$2a$10$hlxzGgPGQ46JoBvOLzR.ouBxLR4glI5DPYR0BdCQZu7mHExMxNQCi', '0', '0', '127.0.0.1', '2023-10-24 17:16:33', 'admin', '2023-10-24 13:20:29', '', '2023-10-24 17:16:32', NULL);

-- ----------------------------
-- Table structure for sys_user_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_post`;
CREATE TABLE `sys_user_post`  (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `post_id` bigint(20) NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`, `post_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户与岗位关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_post
-- ----------------------------
INSERT INTO `sys_user_post` VALUES (1, 1);
INSERT INTO `sys_user_post` VALUES (2, 2);

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户和角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (1, 1);
INSERT INTO `sys_user_role` VALUES (2, 3);
INSERT INTO `sys_user_role` VALUES (3, 4);

SET FOREIGN_KEY_CHECKS = 1;
