<template>
  <div class="app-container bg" @click="onClick">
    <el-image class="gh" :src="require('@/assets/images/screen/gh.png')"></el-image>
    <div class="title1">哈密市人大代表智慧管理平台</div>
    <div class="title2"><PERSON></div>
    <div class="company">哈密市人民代表大会常务委员会</div>
  </div>
</template>

<script>

export default {
  name: "ScreenCover",
  components: { },
  data() {
    return {
    };
  },
  mounted() {
    this.$nextTick(() => {
      document.oncontextmenu = function() {
        return false;
      };
    });
  },
  methods: {
    onClick() {
      let player = this.$parent.$parent.$parent.$refs.MusicPlay;
      player.play();
      this.$router.push({ path: "ScreenMenu"});
    },
  }
};
</script>

<style lang="scss" scoped>

@import '../../../common/font/font.css';

.bg {
  background: url("~@/assets/images/screen/bg1.jpg") no-repeat;
  background-size: 100% 100%;
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  margin: 0;
  padding: 0;
}

.gh {
  width: 640px;
  height: 640px;
  top: 160px;
  left: 1600px;
}

.title1 {
  position: absolute;
  top: 930px;
  width: 100%;
  text-align: center;
  text-align: center;
  font-family: AL-BL;
  text-shadow: 0px 2px 12px rgba(0,0,0,0.5);
  color: #FFFFFF;
  font-weight: 900;
  font-size: 220px;
  font-style: normal;
  text-decoration: none;
}

.title2 {
  position: absolute;
  top: 1280px;
  width: 100%;
  text-align: center;
  text-shadow: 0px 2px 12px rgba(0,0,0,0.5);
  font-family: AL-R;
  color: #FFFFFF;
  font-weight: 300;
  font-size: 84px;
  font-style: normal;
  text-decoration: none;
}

.company {
  position: absolute;
  top: 1850px;
  width: 100%;
  text-align: center;
  font-family: AL-L;
  color: #FFFFFF;
  font-weight: 300;
  font-size: 80px;
  font-style: normal;
  text-decoration: none;
  text-shadow: 0px 2px 12px rgba(0,0,0,0.5);
}

</style>
