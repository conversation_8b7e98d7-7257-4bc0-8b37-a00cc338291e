package com.renda.core.mapper;

import com.renda.common.core.domain.entity.RdStation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 站点管理 数据层
 *
 * <AUTHOR>
 */
public interface RdStationMapper
{
    /**
     * 查询站点管理数据
     *
     * @param station 站点信息
     * @return 站点信息集合
     */
    public List<RdStation> selectStationList(RdStation station);

    /**
     * 根据站点ID查询信息
     *
     * @param stationId 站点ID
     * @return 站点信息
     */
    public RdStation selectStationById(Long stationId);

    /**
     * 根据ID查询所有子站点
     *
     * @param stationId 站点ID
     * @return 站点列表
     */
    public List<RdStation> selectChildrenStationById(Long stationId);

    /**
     * 根据ID查询所有子站点（正常状态）
     *
     * @param stationId 站点ID
     * @return 子站点数
     */
    public int selectNormalChildrenStationById(Long stationId);

    /**
     * 是否存在子节点
     *
     * @param stationId 站点ID
     * @return 结果
     */
    public int hasChildByStationId(Long stationId);

    /**
     * 查询站点是否存在用户
     *
     * @param stationId 站点ID
     * @return 结果
     */
    public int checkStationExistDeputy(Long stationId);

    /**
     * 校验站点名称是否唯一
     *
     * @param stationName 站点名称
     * @param parentId 父站点ID
     * @return 结果
     */
    public RdStation checkStationNameUnique(@Param("stationName") String stationName, @Param("parentId") Long parentId);

    /**
     * 新增站点信息
     *
     * @param station 站点信息
     * @return 结果
     */
    public int insertStation(RdStation station);

    /**
     * 修改站点信息
     *
     * @param station 站点信息
     * @return 结果
     */
    public int updateStation(RdStation station);

    /**
     * 修改子元素关系
     *
     * @param stations 子元素
     * @return 结果
     */
    public int updateStationChildren(@Param("stations") List<RdStation> stations);

    /**
     * 删除站点管理信息
     *
     * @param stationId 站点ID
     * @return 结果
     */
    public int deleteStationById(Long stationId);
}
