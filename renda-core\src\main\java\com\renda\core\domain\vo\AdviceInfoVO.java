package com.renda.core.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.renda.core.domain.RdAdviceAttachment;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AdviceInfoVO {

    /** 建议ID */
    private  Long AdviceId;

    /** 人大代表ID */
    private Long deputyId;

    /** 标题 */
    private String title;

    /** 内容 */
    private String content;

    /** 建议人 */
    private String name;

    /** 联系电话 */
    private String phone;

    /** 建议类别 */
    private String category;

    /** 建议时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    /** 图片列表 */
    private List<String> imgList;

    /** 文件列表 */
    private List<AttachFileInfoVO> fileList;

    /** 附件列表 */
    private List<RdAdviceAttachment> attachs;

    /** 处理状态 */
    private String status;

    /** 服务评分 */
    private Integer serviceRating;

}
