<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renda.core.mapper.RdAdviceReportMapper">

    <resultMap type="RdAdviceReport" id="RdAdviceReportResult">
        <result property="id"    column="id"    />
        <result property="adviceId"    column="advice_id"    />
        <result property="deputyId"    column="deputy_id"    />
        <result property="creator"    column="creator"    />
    </resultMap>

    <sql id="selectRdAdviceReportVo">
        select id, advice_id, deputy_id, creator from rd_advice_report
    </sql>

    <select id="selectRdAdviceReportList" parameterType="RdAdviceReport" resultMap="RdAdviceReportResult">
        <include refid="selectRdAdviceReportVo"/>
        <where>
            <if test="adviceId != null "> and advice_id = #{adviceId}</if>
            <if test="deputyId != null "> and deputy_id = #{deputyId}</if>
            <if test="creator != null "> and creator = #{creator}</if>
        </where>
    </select>

    <select id="selectRdAdviceReportById" parameterType="Long" resultMap="RdAdviceReportResult">
        <include refid="selectRdAdviceReportVo"/>
        where id = #{id}
    </select>

    <insert id="insertRdAdviceReport" parameterType="RdAdviceReport" useGeneratedKeys="true" keyProperty="id">
        insert into rd_advice_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="adviceId != null">advice_id,</if>
            <if test="deputyId != null">deputy_id,</if>
            <if test="creator != null">creator,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="adviceId != null">#{adviceId},</if>
            <if test="deputyId != null">#{deputyId},</if>
            <if test="creator != null">#{creator},</if>
         </trim>
    </insert>

    <update id="updateRdAdviceReport" parameterType="RdAdviceReport">
        update rd_advice_report
        <trim prefix="SET" suffixOverrides=",">
            <if test="adviceId != null">advice_id = #{adviceId},</if>
            <if test="deputyId != null">deputy_id = #{deputyId},</if>
            <if test="creator != null">creator = #{creator},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRdAdviceReportById" parameterType="Long">
        delete from rd_advice_report where id = #{id}
    </delete>

    <delete id="deleteRdAdviceReportByIds" parameterType="String">
        delete from rd_advice_report where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
