package com.renda.core.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.renda.common.annotation.Excel;
import com.renda.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 工作通知反馈对象 rd_notice_feedback
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
@Data
public class RdNoticeFeedback extends BaseEntity
{

    /** $column.columnComment */
    private Long id;

    /** 动态ID */
    @Excel(name = "动态ID")
    private Long noticeId;

    /** 代表ID */
    @Excel(name = "代表ID")
    private Long deputyId;

    /** 反馈内容 */
    @Excel(name = "反馈内容")
    private String content;

    /** 代表姓名 */
    private String deputyName;

    /** 代表电话 */
    private String phone;

    /** 代表头像 */
    private String avatar;

    private List<RdNoticeFeedbackAttachment> attachments;

}
