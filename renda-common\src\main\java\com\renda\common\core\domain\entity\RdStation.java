package com.renda.common.core.domain.entity;

import com.renda.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * 部门表 sys_dept
 *
 * <AUTHOR>
 */
public class RdStation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 站点ID */
    private Long stationId;

    /** 父站点ID */
    private Long parentId;

    /** 祖级列表 */
    private String ancestors;

    /** 站点名称 */
    private String stationName;

    /** 显示顺序 */
    private Integer orderNum;

    /** 父站点名称 */
    private String parentName;

    /** 子部门 */
    private List<RdStation> children = new ArrayList<RdStation>();

    public Long getStationId() {
        return stationId;
    }

    public void setStationId(Long stationId) {
        this.stationId = stationId;
    }

    public Long getParentId()
    {
        return parentId;
    }

    public void setParentId(Long parentId)
    {
        this.parentId = parentId;
    }

    public String getAncestors()
    {
        return ancestors;
    }

    public void setAncestors(String ancestors)
    {
        this.ancestors = ancestors;
    }

    @NotBlank(message = "站点名称不能为空")
    @Size(min = 0, max = 30, message = "站点名称长度不能超过30个字符")
    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    @NotNull(message = "显示顺序不能为空")
    public Integer getOrderNum()
    {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum)
    {
        this.orderNum = orderNum;
    }

    public String getParentName()
    {
        return parentName;
    }

    public void setParentName(String parentName)
    {
        this.parentName = parentName;
    }

    public List<RdStation> getChildren()
    {
        return children;
    }

    public void setChildren(List<RdStation> children)
    {
        this.children = children;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("stationId", getStationId())
            .append("parentId", getParentId())
            .append("ancestors", getAncestors())
            .append("stationName", getStationName())
            .append("orderNum", getOrderNum())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }

}
