<template>
  <div class="app-container bg">
    <screenHeader ref="headerComp" />
    <div class="deputy-container">
      <div class="deputy-list" v-if="deputyList.length > 0">
        <div v-for="(item, index) in deputyList">
          <div class="deputy" @click="onShowDeputyDetail(item.id)">
            <el-image class="deputy-pic" :src="baseURL + item.avatar" fit="cover"></el-image>
            <div class="deputy-footer">
              <el-image class="deputy-code" :src="baseURL + item.qrcodeUrl"></el-image>
              <div class="deputy-info">
                <div class="deputy-name">{{item.name}}</div>
                <div class="deputy-duty">{{item.duty}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div style="margin: auto;" v-else>
        <el-empty :image-size="400">
          <div slot="description" style="font-family: AL-R; font-size: 40px; color: #909399;">没有符合条件的人大代表</div>
        </el-empty>
      </div>
      <div class="deputy-toolbar">
        <div class="filter-bar">
          <treeselect v-model="stationId" :options="stationOptions" placeholder="所属站点" @input="stationChanged" />
        </div>
        <div class="search-bar">
          <input style="border: none;outline: none;" type="text" placeholder="请输入关键字" v-model="keyword" @keydown.enter="searchDeputy" @focus="onInputFocus()" />
          <i class="el-icon-search search-icon" @click="searchDeputy" />
        </div>
        <el-pagination
          v-show="deputyTotal>0"
          :total="deputyTotal"
          :current-page.sync="currPage"
          :page-size="pageSize"
          @pagination="getDeputyList"
          @current-change="handleCurrentChange"
          background
          layout="prev, pager, next">
        </el-pagination>
      </div>
    </div>
    <el-image class="return-btn" :src="require('@/assets/images/screen/return.png')" @click="onBack"></el-image>
    <div v-show="showKeyboard">
      <SimpleKeyboard ref="SimpleKeyboard" @onChange="onChangeKeyboard" @onKeyPress="onKeyPress" />
    </div>
  </div>

</template>

<script>

import screenHeader from "./components/screenHeader";
import Bus from '@/api/renda/bus';
import { listDeputy2, stationTreeSelect } from '@/api/renda/screen';
import Treeselect from '@riophae/vue-treeselect';
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import SimpleKeyboard from './components/simpleKeyboard.vue'

export default {
  name: "DeputyList",
  components: { screenHeader, Treeselect, SimpleKeyboard },
  data() {
    return {
      defaultAvatar: require("@/assets/images/avatar.png"), // 默认头像
      baseURL: process.env.VUE_APP_BASE_API, // 图片上传地址头
      deputyList: [], // 人大代表列表
      deputyTotal: 0, // 人大代表总数
      currPage: 1, // 当前页
      pageSize: 18, // 每页数据量
      stationOptions: undefined, // 站点树选项
      stationId: null, // 所属站点
      keyword: '', // 搜索关键字
      showKeyboard: false,
      groupId: null, // 代表团ID
    };
  },
  created() {
    this.groupId = this.$route.query.groupId;
    this.getDeputyList(this.currPage);
    stationTreeSelect().then(response => {
      this.stationOptions = response.data;
    });
  },
  activated() {
    this.groupId = this.$route.query.groupId;
    this.getDeputyList(this.currPage);
    Bus.$off('lastPageNum');
    Bus.$on('lastPageNum', function(data){
      this.currPage = Number(data)
      this.getDeputyList(this.currPage);
    }.bind(this));
  },
  mounted() {
    this.$nextTick(() => {
      document.oncontextmenu = function() {
        return false;
      };
    });
  },
  methods: {
    getDeputyList(pageNum) {
      listDeputy2({
        pageNum: pageNum,
        pageSize: this.pageSize,
        name: this.keyword,
        stationId: this.stationId,
        groupId: this.groupId,
      }).then(response => {
        this.deputyList = response.rows;
        this.deputyTotal = response.total;
      });
    },
    handleCurrentChange(pageNum) {
      this.getDeputyList(pageNum)
    },
    searchDeputy() {
      this.getDeputyList(1);
    },
    onShowDeputyDetail(deputyId) {
      this.$router.push({ path: 'DeputyDetail', query: { deputyId: deputyId, lastPageNum: this.currPage.toString() }})
    },
    onBack() {
      this.$refs.headerComp.onClick();
    },
    stationChanged(node) {
      this.stationId = node;
      this.searchDeputy();
    },

    // inpuit获取焦点显示虚拟键盘
    onInputFocus() {
      this.keyword = ''
      this.showKeyboard = true
      // 父组件调用子组件的方法
      this.$refs.SimpleKeyboard.onKeyPress('{clear}')
    },
    // 给输入框赋值
    onChangeKeyboard(input) {
      this.keyword = input
    },
    //
    onKeyPress(button) {
      if ('{enter}' === button) {
        this.searchDeputy();
      }
      if ('{clear}' === button) {
        this.keyword = ''
        this.searchDeputy();
      }
    },
    // 点击关闭隐藏键盘
    closekeyboard() {
      this.showKeyboard = false
    },

  }
};
</script>

<style lang="scss" scoped>

@import '../../../common/font/font.css';

.deputy-container {
  margin: 250px 540px 100px 540px;
  padding: 50px 100px;
  height: 1826px;
  border-radius: 24px;
  background: #FFF6EF;
  display: flex;
  flex-direction: column;
  position: relative;
  box-shadow: 0px 2px 12px 0px rgba(0,0,0,0.5);

  .deputy-list {
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    .deputy {
      display: flex;
      align-items: center;
      justify-content: start;
      flex-direction: column;
      width: 300px;
      height: 540px;
      margin: 12px 60px;
      flex-shrink: 0;
      .deputy-pic {
        width: 300px;
        height: 400px;
        flex-shrink: 0;
        border: #ffffff solid 10px;
        box-shadow: 0px 0px 20px 0px rgba(0,0,0,0.1);
      }
      .deputy-footer {
        display: flex;
        flex-direction: row;
        margin-top: 10px;
        width: 100%;
        .deputy-code {
          display: flex;
          width: 100px;
          height: 100px;
          flex-shrink: 0;
        }
        .deputy-info {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 200px;
          .deputy-name {
            width: 100%;
            text-align: center;
            padding: 0 4px;
            font-size: 28px;
            font-family: AL-B;
            font-weight: bold;
            color: #2F2F2F;
            line-height: 32px;
          }
          .deputy-duty {
            margin-top: 10px;
            width: 100%;
            text-align: center;
            padding: 0 8px;
            font-size: 22px;
            font-family: AL-R;
            font-weight: 300;
            color: #909090;
            line-height: 26px;
          }
        }
      }
    }
  }
  .deputy-toolbar {
    position: absolute;
    bottom: 30px;
    margin: 12px 60px;
    display: flex;
    flex-direction: row;
    .filter-bar {
      margin-right: 50px;
      width: 400px;
    }
    .search-bar {
      width: 300px;
      margin-right: 80px;
      display: flex;
      flex-direction: row;
      line-height: 30px;
      border: #f9e6c7 1px solid;
      background: #fff;
      border-radius: 15px;
      padding: 0 10px;
      justify-content: space-between;
      input {
        display: flex;
        flex-grow: 1;
      }
      .search-icon {
        width: 25px;
        height: 30px;
        line-height: 29px;
        margin-left: 5px;
      }
    }
  }

  .el-empty__description {
    background-color: red !important;
  }

}

</style>
