package com.renda.core.service.impl;

import java.util.Date;
import java.util.List;
import com.renda.common.config.RendaConfig;
import com.renda.common.utils.DateUtils;
import com.renda.common.utils.SecurityUtils;
import com.renda.common.utils.StringUtils;
import com.renda.common.utils.file.FileUploadUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.renda.core.mapper.RdStationStaffMapper;
import com.renda.core.domain.RdStationStaff;
import com.renda.core.service.IRdStationStaffService;

/**
 * 工作人员管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Service
public class RdStationStaffServiceImpl implements IRdStationStaffService
{
    @Autowired
    private RdStationStaffMapper rdStationStaffMapper;

    /**
     * 查询工作人员管理
     *
     * @param id 工作人员管理主键
     * @return 工作人员管理
     */
    @Override
    public RdStationStaff selectRdStationStaffById(Long id)
    {
        return rdStationStaffMapper.selectRdStationStaffById(id);
    }

    /**
     * 查询工作人员管理列表
     *
     * @param rdStationStaff 工作人员管理
     * @return 工作人员管理
     */
    @Override
    public List<RdStationStaff> selectRdStationStaffList(RdStationStaff rdStationStaff)
    {
        return rdStationStaffMapper.selectRdStationStaffList(rdStationStaff);
    }

    /**
     * 新增工作人员管理
     *
     * @param rdStationStaff 工作人员管理
     * @return 结果
     */
    @Override
    public int insertRdStationStaff(RdStationStaff rdStationStaff)
    {
        // 如果rdStationStaff的Avatar是data:image开头的，说明是base64编码的图片，需要先保存图片，再把图片路径赋值给rdStationStaff的Avatar
        if (StringUtils.isNotEmpty(rdStationStaff.getAvatar()) && rdStationStaff.getAvatar().startsWith("data:image")) {
            // 获取Base64数据
            String base64 = rdStationStaff.getAvatar().split(",")[1];
            String avatar = FileUploadUtils.uploadBase64Image(RendaConfig.getProfile()+"/stationstaff", base64);
            if (StringUtils.isEmpty(avatar)) {
                throw new RuntimeException("图片上传失败");
            }
            rdStationStaff.setAvatar(avatar);
        }
        
        String username = SecurityUtils.getLoginUser().getUsername();
        Date date = DateUtils.getNowDate();
        rdStationStaff.setCreateBy(username);
        rdStationStaff.setCreateTime(date);
        rdStationStaff.setUpdateBy(username);
        rdStationStaff.setUpdateTime(date);
        
        return rdStationStaffMapper.insertRdStationStaff(rdStationStaff);
    }

    /**
     * 修改工作人员管理
     *
     * @param rdStationStaff 工作人员管理
     * @return 结果
     */
    @Override
    public int updateRdStationStaff(RdStationStaff rdStationStaff)
    {
        // 如果rdStationStaff的Avatar是data:image开头的，说明是base64编码的图片，需要先保存图片，再把图片路径赋值给rdStationStaff的Avatar
        if (StringUtils.isNotEmpty(rdStationStaff.getAvatar()) && rdStationStaff.getAvatar().startsWith("data:image")) {
            // 获取Base64数据
            String base64 = rdStationStaff.getAvatar().split(",")[1];
            String avatar = FileUploadUtils.uploadBase64Image(RendaConfig.getProfile()+"/stationstaff", base64);
            if (StringUtils.isEmpty(avatar)) {
                throw new RuntimeException("图片上传失败");
            }
            rdStationStaff.setAvatar(avatar);
        }
        
        String username = SecurityUtils.getLoginUser().getUsername();
        Date date = DateUtils.getNowDate();
        rdStationStaff.setUpdateBy(username);
        rdStationStaff.setUpdateTime(date);
        
        return rdStationStaffMapper.updateRdStationStaff(rdStationStaff);
    }

    /**
     * 批量删除工作人员管理
     *
     * @param ids 需要删除的工作人员管理主键
     * @return 结果
     */
    @Override
    public int deleteRdStationStaffByIds(Long[] ids)
    {
        return rdStationStaffMapper.deleteRdStationStaffByIds(ids);
    }

    /**
     * 删除工作人员管理信息
     *
     * @param id 工作人员管理主键
     * @return 结果
     */
    @Override
    public int deleteRdStationStaffById(Long id)
    {
        return rdStationStaffMapper.deleteRdStationStaffById(id);
    }
}
