package com.renda.core.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.renda.core.domain.RdDeputy;
import com.renda.core.domain.vo.DeputyInfoVO;
import com.renda.core.domain.vo.DeputyStatVO;
import com.renda.core.domain.vo.DeputyStatisticsInfoVO;

/**
 * 人大代表管理Mapper接口
 *
 * <AUTHOR>
 * @date 2023-10-23
 */
public interface RdDeputyMapper
{
    /**
     * 查询人大代表管理
     *
     * @param id 人大代表管理主键
     * @return 人大代表管理
     */
    public RdDeputy selectRdDeputyById(Long id);

    /**
     * 查询人大代表管理列表
     *
     * @param rdDeputy 人大代表管理
     * @return 人大代表管理集合
     */
    public List<RdDeputy> selectRdDeputyList(RdDeputy rdDeputy);

    /**
     * 查询人大代表管理列表
     *
     * @param rdDeputy 人大代表管理
     * @return 人大代表管理集合
     */
    public List<RdDeputy> selectRdDeputyList2(RdDeputy rdDeputy);

    /**
     * 新增人大代表管理
     *
     * @param rdDeputy 人大代表管理
     * @return 结果
     */
    public int insertRdDeputy(RdDeputy rdDeputy);

    /**
     * 修改人大代表管理
     *
     * @param rdDeputy 人大代表管理
     * @return 结果
     */
    public int updateRdDeputy(RdDeputy rdDeputy);

    /**
     * 删除人大代表管理
     *
     * @param id 人大代表管理主键
     * @return 结果
     */
    public int deleteRdDeputyById(Long id);

    /**
     * 批量删除人大代表管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRdDeputyByIds(Long[] ids);

    /**
     * 查询人大代表管理（小程序端展示）
     *
     * @param id 人大代表管理主键
     * @return 人大代表管理
     */
    DeputyInfoVO selectRdDeputyInfoById(Long id);

    /***
     * 根据手机号获取人大代表信息
     * @param phone
     * @return 人大代表信息
     */
    RdDeputy selectDeputyByPhone(String phone);

    /***
     * 获取代表统计信息接口
     * @return
     */
    DeputyStatisticsInfoVO getDeputyStatistics(Long userId);

    /**
     * 获取人大代表履职统计信息
     */
    List<DeputyStatVO> getDeputyStat(Long id);

    /***
     * 根据部门查询人大代表列表
     * @param deptId 部门id
     * @return
     */
    List<RdDeputy> selectRdDeputyListByDeptId(Long id);

    /**
     * 查询指定联络站指定站内职务的人数
     *
     * @param stationId 联络站ID
     * @param stationDuty 站内职务
     * @param excludeId 排除的代表ID（修改时使用）
     * @return 人数
     */
    int countByStationIdAndStationDuty(@Param("stationId") Long stationId, @Param("stationDuty") String stationDuty, @Param("excludeId") Long excludeId);

}
