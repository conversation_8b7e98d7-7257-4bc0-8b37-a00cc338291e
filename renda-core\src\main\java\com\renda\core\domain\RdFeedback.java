package com.renda.core.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.renda.common.annotation.Excel;
import com.renda.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 人大代反馈意见对象 rd_feedback
 *
 * <AUTHOR>
 * @date 2023-11-04
 */
@Data
public class RdFeedback extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 建议ID */
    @Excel(name = "建议ID")
    private Long adviceId;

    /** 反馈类型：1-普通；2-上报 */
    private Integer feedbackType;

    /** 用户类型：1-群众；2-代表 */
    @Excel(name = "用户类型：1-群众；2-代表")
    private Integer userType;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 头像 */
    @Excel(name = "头像")
    private String avatar;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /** 附件 */
    private List<RdFeedbackAttachment> attachmentList;

}
