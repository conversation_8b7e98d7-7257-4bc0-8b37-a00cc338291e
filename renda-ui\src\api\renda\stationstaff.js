import request from '@/utils/request'

// 查询工作人员管理列表
export function listStationstaff(query) {
  return request({
    url: '/renda/stationstaff/list',
    method: 'get',
    params: query
  })
}

// 查询工作人员管理详细
export function getStationstaff(id) {
  return request({
    url: '/renda/stationstaff/' + id,
    method: 'get'
  })
}

// 新增工作人员管理
export function addStationstaff(data) {
  return request({
    url: '/renda/stationstaff',
    method: 'post',
    data: data
  })
}

// 修改工作人员管理
export function updateStationstaff(data) {
  return request({
    url: '/renda/stationstaff',
    method: 'put',
    data: data
  })
}

// 删除工作人员管理
export function delStationstaff(id) {
  return request({
    url: '/renda/stationstaff/' + id,
    method: 'delete'
  })
}
