import request from '@/utils/request'

// 查询代表建议列表
export function listRecom(query) {
  return request({
    url: '/renda/recom/list',
    method: 'get',
    params: query
  })
}

// 查询代表建议详细
export function getRecom(id) {
  return request({
    url: '/renda/recom/' + id,
    method: 'get'
  })
}

// 新增代表建议
export function addRecom(data) {
  return request({
    url: '/renda/recom',
    method: 'post',
    data: data
  })
}

// 修改代表建议
export function updateRecom(data) {
  return request({
    url: '/renda/recom',
    method: 'put',
    data: data
  })
}

// 删除代表建议
export function delRecom(id) {
  return request({
    url: '/renda/recom/' + id,
    method: 'delete'
  })
}

// 删除已上传文件
export function deleteUploadedFile(data) {
  return request({
    url: '/renda/recom/deleteUploadedFile',
    method: 'post',
    data: data
  })
}

// 查询代表建议列表
export function getHostOptions(query) {
  return request({
    url: '/renda/recom/listHost',
    method: 'get',
    params: query
  })
}
