<template>
  <header class="header">
    <div class="logo">
      <img src='@/assets/images/screen/gh256.png' alt="国徽" class="emblem" />
      <div class="title">
        <h1>哈密市人大代表智慧管理平台</h1>
        <p>管理成就卓越 智慧引领未来</p>
      </div>
    </div>
    <div v-if="showBackBtn" class="back-btn" @click="$router.back()">
      <i class="el-icon-arrow-left"></i>
    </div>
  </header>
</template>

<script>
export default {
  name: 'AppHeader',
  props: {
    showBackBtn: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  height: 120px;
  background: linear-gradient(90deg, rgba(180, 22, 22, 0.9), rgba(217, 37, 37, 0.9));
  padding: 0 50px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.3), transparent);
  }

  .logo {
    height: 100%;
    display: flex;
    align-items: center;

    .emblem {
      width: 90px;
      height: 90px;
      margin-right: 30px;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    }

    .title {
      text-align: left;
      color: #fff;

      h1 {
        font-size: 36px;
        margin: 0;
        letter-spacing: 2px;
        font-weight: 500;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        font-family: 'AL-BL' !important;
      }

      p {
        font-size: 30px !important;
        margin: 5px 0 0;
        opacity: 0.9;
        font-family: 'AL-L' !important;
      }
    }
  }

  .back-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
    color: #fff;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    i {
      font-size: 24px;
    }
  }
}

/* 4K分辨率适配 */
@media (min-width: 3000px) {
  .header {
    height: 160px;
    padding: 0 80px;

    .logo {
      .emblem {
        width: 120px;
        height: 120px;
        margin-right: 40px;
      }

      .title {
        h1 {
          font-size: 48px;
          letter-spacing: 3px;
          font-family: 'AL-L' !important;
        }

        p {
          font-size: 24px;
          margin-top: 8px;
          font-family: 'AL-L' !important;
        }
      }
    }

    .back-btn {
      width: 70px;
      height: 70px;

      i {
        font-size: 32px;
      }
    }
  }
}
</style>
