import request from '@/utils/request'

// 查询联络站动态列表
export function listStationnews(query) {
  return request({
    url: '/renda/stationnews/list',
    method: 'get',
    params: query
  })
}

// 查询联络站动态详细
export function getStationnews(id) {
  return request({
    url: '/renda/stationnews/' + id,
    method: 'get'
  })
}

// 新增联络站动态
export function addStationnews(data) {
  return request({
    url: '/renda/stationnews',
    method: 'post',
    data: data
  })
}

// 修改联络站动态
export function updateStationnews(data) {
  return request({
    url: '/renda/stationnews',
    method: 'put',
    data: data
  })
}

// 删除联络站动态
export function delStationnews(id) {
  return request({
    url: '/renda/stationnews/' + id,
    method: 'delete'
  })
}
