package com.renda.core.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;
import java.util.List;

@Data
public class JobInfoVO {

    /** 履职工作类型 */
    private String jobType;

    /** 组织单位 */
    private String organizer;

    /** 起始时间 */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date beginDate;

    /** 截止时间 */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date endDate;

    /** 标题 */
    private String title;

    /** 工作内容 */
    private String content;

    /** 图片列表 */
    private List<String> imgList;

    /** 文件列表 */
    private List<AttachFileInfoVO> fileList;

}
