package com.renda.core.mapper;

import java.util.List;
import com.renda.core.domain.RdRecomAttachment;

/**
 * 代建议附件Mapper接口
 *
 * <AUTHOR>
 * @date 2023-11-23
 */
public interface RdRecomAttachmentMapper
{
    /**
     * 查询代建议附件
     *
     * @param id 代建议附件主键
     * @return 代建议附件
     */
    public RdRecomAttachment selectRdRecomAttachmentById(Long id);

    /**
     * 查询代建议附件列表
     *
     * @param rdRecomAttachment 代建议附件
     * @return 代建议附件集合
     */
    public List<RdRecomAttachment> selectRdRecomAttachmentList(RdRecomAttachment rdRecomAttachment);

    /**
     * 新增代建议附件
     *
     * @param rdRecomAttachment 代建议附件
     * @return 结果
     */
    public int insertRdRecomAttachment(RdRecomAttachment rdRecomAttachment);

    /**
     * 修改代建议附件
     *
     * @param rdRecomAttachment 代建议附件
     * @return 结果
     */
    public int updateRdRecomAttachment(RdRecomAttachment rdRecomAttachment);

    /**
     * 删除代建议附件
     *
     * @param id 代建议附件主键
     * @return 结果
     */
    public int deleteRdRecomAttachmentById(Long id);

    /**
     * 批量删除代建议附件
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRdRecomAttachmentByIds(Long[] ids);

    /**
     * 删除建议附件信息
     *
     * @param recomId 建议主键
     * @return 结果
     */
    int deleteRdRecomAttachmentByRecomId(Long recomId);

    /**
     * 删除建议附件信息
     *
     * @param ids 建议主键
     * @return 结果
     */
    int deleteRdRecomAttachmentByRecomIds(Long[] ids);

}
