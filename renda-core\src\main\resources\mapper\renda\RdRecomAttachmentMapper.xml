<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renda.core.mapper.RdRecomAttachmentMapper">

    <resultMap type="RdRecomAttachment" id="RdRecomAttachmentResult">
        <result property="id"    column="id"    />
        <result property="recomId"    column="recom_id"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileUrl"    column="file_url"    />
    </resultMap>

    <sql id="selectRdRecomAttachmentVo">
        select id, recom_id, file_type, file_name, file_url from rd_recom_attachment
    </sql>

    <select id="selectRdRecomAttachmentList" parameterType="RdRecomAttachment" resultMap="RdRecomAttachmentResult">
        <include refid="selectRdRecomAttachmentVo"/>
        <where>
            <if test="recomId != null "> and recom_id = #{recomId}</if>
            <if test="fileType != null "> and file_type = #{fileType}</if>
            <if test="fileName != null and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
        </where>
    </select>

    <select id="selectRdRecomAttachmentById" parameterType="Long" resultMap="RdRecomAttachmentResult">
        <include refid="selectRdRecomAttachmentVo"/>
        where id = #{id}
    </select>

    <insert id="insertRdRecomAttachment" parameterType="RdRecomAttachment" useGeneratedKeys="true" keyProperty="id">
        insert into rd_recom_attachment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recomId != null">recom_id,</if>
            <if test="fileType != null">file_type,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recomId != null">#{recomId},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
         </trim>
    </insert>

    <update id="updateRdRecomAttachment" parameterType="RdRecomAttachment">
        update rd_recom_attachment
        <trim prefix="SET" suffixOverrides=",">
            <if test="recomId != null">recom_id = #{recomId},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRdRecomAttachmentById" parameterType="Long">
        delete from rd_recom_attachment where id = #{id}
    </delete>

    <delete id="deleteRdRecomAttachmentByIds" parameterType="String">
        delete from rd_recom_attachment where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteRdRecomAttachmentByRecomId" parameterType="Long">
        delete from rd_recom_attachment where recom_id = #{recomId}
    </delete>
    <delete id="deleteRdRecomAttachmentByRecomIds">
        delete from rd_recom_attachment where recom_id in
        <foreach item="recomId" collection="array" open="(" separator="," close=")">
            #{recomId}
        </foreach>
    </delete>
</mapper>
