package com.renda.core.service.impl;

import com.renda.common.constant.UserConstants;
import com.renda.common.core.domain.TreeSelect;
import com.renda.common.core.domain.entity.*;
import com.renda.common.exception.ServiceException;
import com.renda.common.utils.SecurityUtils;
import com.renda.common.utils.StringUtils;
import com.renda.common.utils.spring.SpringUtils;
import com.renda.core.domain.RdDeputy;
import com.renda.core.mapper.RdStationMapper;
import com.renda.core.service.IRdDeputyService;
import com.renda.core.service.IRdStationService;
import com.renda.system.service.ISysDictDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 站点管理 服务实现
 *
 * <AUTHOR>
 */
@Service
public class RdStationServiceImpl implements IRdStationService
{
    @Autowired
    private RdStationMapper stationMapper;

    @Autowired
    private IRdDeputyService deputyService;

    @Autowired
    private ISysDictDataService sysDictDataService;

    /**
     * 查询站点管理数据
     *
     * @param station 站点信息
     * @return 站点信息集合
     */
    @Override
    public List<RdStation> selectStationList(RdStation station)
    {
        return stationMapper.selectStationList(station);
    }

    /**
     * 查询站点树结构信息
     *
     * @param station 站点信息
     * @return 站点树信息集合
     */
    @Override
    public List<TreeSelect> selectStationTreeList(RdStation station)
    {
//        List<RdStation> stations = SpringUtils.getAopProxy(this).selectStationList(station);
        List<RdStation> stations = stationMapper.selectStationList(station);
        return buildStationTreeSelect(stations);
    }

    /**
     * 查询站点树结构信息（附各站点人大代表）
     *
     * @param station 站点信息
     * @return 站点树信息集合（附各站点人大代表）
     */
    @Override
    public List<TreeSelect> selectStationTreeListWithDeputy(RdStation station)
    {
        // 生成站点树
        List<RdStation> stations = stationMapper.selectStationList(station);
        List<TreeSelect> treeSelects = buildStationTreeSelect(stations);

        // 将人大代表添加到站点树中
        deputyService.selectRdDeputyList(new RdDeputy()).forEach(deputy -> {
            // 遍历站点树，将人大代表添加到对应的站点下
            treeSelects.forEach(treeSelect -> {
                TreeSelect node = treeSelect.findNode(treeSelect, deputy.getStationId());
                if (node != null) {
                    TreeSelect deputyNode = new TreeSelect();
                    deputyNode.setId(deputy.getId());
                    deputyNode.setLabel(deputy.getName());
                    deputyNode.setPid(deputy.getStationId());
                    deputyNode.setLeaf(true);
                    node.getChildren().add(deputyNode);
                }
            });
        });

        return treeSelects;

    }

    /**
     * 获取代表团树列表（附各代表团人大代表）
     *
     * @return 代表团树信息集合（附各代表团人大代表）
     */
    public List<TreeSelect> groupTreeSelectWithDeputy() {
        // 生成代表团树
        List<TreeSelect> groupTree = new ArrayList<>();
        SysDictData dictData = new SysDictData();
        dictData.setDictType("rd_deputy_group");
        List<SysDictData> groupList = sysDictDataService.selectDictDataList(dictData);
        groupList.forEach(group -> {
            TreeSelect groupNode = new TreeSelect();
            groupNode.setId(new Long(group.getDictValue()));
            groupNode.setLabel(group.getDictLabel());
            groupNode.setPid(0L);
            groupNode.setLeaf(false);
            groupTree.add(groupNode);
        });

        // 将人大代表添加到站点树中
        deputyService.selectRdDeputyList(new RdDeputy()).forEach(deputy -> {
            // 遍历站点树，将人大代表添加到对应的站点下
            groupTree.forEach(groupNode -> {
                if (groupNode.getId().toString().equals(deputy.getGroupId())) {
                    TreeSelect deputyNode = new TreeSelect();
                    deputyNode.setId(deputy.getId());
                    deputyNode.setLabel(deputy.getName());
                    deputyNode.setPid(groupNode.getId());
                    deputyNode.setLeaf(true);
                    if (groupNode.getChildren() == null) {
                        groupNode.setChildren(new ArrayList<>());
                    }
                    groupNode.getChildren().add(deputyNode);
                }
            });
        });

        return groupTree;
    }

    /**
     * 构建前端所需要树结构
     *
     * @param stations 站点列表
     * @return 树结构列表
     */
    @Override
    public List<RdStation> buildStationTree(List<RdStation> stations)
    {
        List<RdStation> returnList = new ArrayList<RdStation>();
        List<Long> tempList = stations.stream().map(RdStation::getStationId).collect(Collectors.toList());
        for (RdStation station : stations)
        {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(station.getParentId()))
            {
                recursionFn(stations, station);
                returnList.add(station);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = stations;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param stations 站点列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildStationTreeSelect(List<RdStation> stations)
    {
        List<RdStation> stationTrees = buildStationTree(stations);
        return stationTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 根据站点ID查询信息
     *
     * @param stationId 站点ID
     * @return 站点信息
     */
    @Override
    public RdStation selectStationById(Long stationId)
    {
        return stationMapper.selectStationById(stationId);
    }

    /**
     * 根据ID查询所有子站点（正常状态）
     *
     * @param stationId 站点ID
     * @return 子站点数
     */
    @Override
    public int selectNormalChildrenStationById(Long stationId)
    {
        return stationMapper.selectNormalChildrenStationById(stationId);
    }

    /**
     * 是否存在子节点
     *
     * @param stationId 站点ID
     * @return 结果
     */
    @Override
    public boolean hasChildByStationId(Long stationId)
    {
        int result = stationMapper.hasChildByStationId(stationId);
        return result > 0;
    }

    /**
     * 查询站点是否存在用户
     *
     * @param stationId 站点ID
     * @return 结果 true 存在 false 不存在
     */
    @Override
    public boolean checkStationExistDeputy(Long stationId)
    {
        int result = stationMapper.checkStationExistDeputy(stationId);
        return result > 0;
    }

    /**
     * 校验站点名称是否唯一
     *
     * @param station 站点信息
     * @return 结果
     */
    @Override
    public boolean checkStationNameUnique(RdStation station)
    {
        Long stationId = StringUtils.isNull(station.getStationId()) ? -1L : station.getStationId();
        RdStation info = stationMapper.checkStationNameUnique(station.getStationName(), station.getParentId());
        if (StringUtils.isNotNull(info) && info.getStationId().longValue() != stationId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验站点是否有数据权限
     *
     * @param stationId 站点id
     */
    @Override
    public void checkStationDataScope(Long stationId)
    {
        if (!SysUser.isAdmin(SecurityUtils.getUserId()))
        {
            RdStation station = new RdStation();
            station.setStationId(stationId);
            List<RdStation> stations = SpringUtils.getAopProxy(this).selectStationList(station);
            if (StringUtils.isEmpty(stations))
            {
                throw new ServiceException("没有权限访问站点数据！");
            }
        }
    }

    /**
     * 新增保存站点信息
     *
     * @param station 站点信息
     * @return 结果
     */
    @Override
    public int insertStation(RdStation station)
    {
        RdStation info = stationMapper.selectStationById(station.getParentId());
        station.setAncestors(info.getAncestors() + "," + station.getParentId());
        return stationMapper.insertStation(station);
    }

    /**
     * 修改保存站点信息
     *
     * @param station 站点信息
     * @return 结果
     */
    @Override
    public int updateStation(RdStation station)
    {
        RdStation newParentStation = stationMapper.selectStationById(station.getParentId());
        RdStation oldStation = stationMapper.selectStationById(station.getStationId());
        if (StringUtils.isNotNull(newParentStation) && StringUtils.isNotNull(oldStation))
        {
            String newAncestors = newParentStation.getAncestors() + "," + newParentStation.getStationId();
            String oldAncestors = oldStation.getAncestors();
            station.setAncestors(newAncestors);
            updateStationChildren(station.getStationId(), newAncestors, oldAncestors);
        }
        int result = stationMapper.updateStation(station);
        return result;
    }

    /**
     * 修改子元素关系
     *
     * @param stationId 被修改的站点ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateStationChildren(Long stationId, String newAncestors, String oldAncestors)
    {
        List<RdStation> children = stationMapper.selectChildrenStationById(stationId);
        for (RdStation child : children)
        {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        if (children.size() > 0)
        {
            stationMapper.updateStationChildren(children);
        }
    }

    /**
     * 删除站点管理信息
     *
     * @param stationId 站点ID
     * @return 结果
     */
    @Override
    public int deleteStationById(Long stationId)
    {
        return stationMapper.deleteStationById(stationId);
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<RdStation> list, RdStation t)
    {
        // 得到子节点列表
        List<RdStation> childList = getChildList(list, t);
        t.setChildren(childList);
        for (RdStation tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<RdStation> getChildList(List<RdStation> list, RdStation t)
    {
        List<RdStation> tlist = new ArrayList<RdStation>();
        Iterator<RdStation> it = list.iterator();
        while (it.hasNext())
        {
            RdStation n = (RdStation) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getStationId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<RdStation> list, RdStation t)
    {
        return getChildList(list, t).size() > 0;
    }

}
