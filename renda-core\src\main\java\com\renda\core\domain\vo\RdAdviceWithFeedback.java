package com.renda.core.domain.vo;

import com.renda.common.annotation.Excel;
import com.renda.common.core.domain.BaseEntity;
import com.renda.core.domain.RdAdviceAttachment;
import com.renda.core.domain.RdFeedback;
import com.renda.core.domain.RdMass;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

@Data
public class RdAdviceWithFeedback extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 人民代表ID */
    private Long deputyId;

    /** 代表姓名 */
    private String deputyName;

    /** 代表头像 */
    private String deputyAvatar;

    /** 标题 */
    private String title;

    /** 建议内容 */
    private String content;

    /** 姓名 */
    private String name;

    /** 电话 */
    private String phone;

    /** 群众ID */
    private Long massId;

    /** 群众 */
    private String massAvatar;

    /** 建议类别 */
    private String category;

    /** 建议类别名称 */
    private String categoryName;

    /** 处理状态 (0-待处理, 1-处理中, 2-已处理) */
    private String status;

    /** 服务评分 (1-5分) */
    private Integer serviceRating;

    /** 建议附件 */
    private List<RdAdviceAttachment> attachmentList;

    /** 反馈信息 */
    private List<RdFeedback> feedbackList;

}
