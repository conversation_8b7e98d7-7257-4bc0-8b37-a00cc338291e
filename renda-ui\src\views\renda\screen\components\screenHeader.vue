<template>
  <div class="header">
    <el-image class="header-bg" :src="require('@/assets/images/screen/header-bg.png')" @click="onClick"></el-image>
    <el-image class="gh" :src="require('@/assets/images/screen/gh256.png')"></el-image>
    <div class="title1">哈密市人大代表智慧管理平台</div>
    <div class="title2">管理成就卓越<span class="space" />智慧引领未来</div>
    <div :class="isPlaying ? 'music rolling' : 'music'" @click="onPlay">
      <el-image class="music-logo" :src="require('@/assets/images/screen/music.png')"></el-image>
    </div>
  </div>
</template>

<script>

import Bus from "@/api/renda/bus";

export default {
  name: "ScreenHeader",
  props: {
    lastPageNum: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      isPlaying: false, // 是否正在播放
    };
  },
  activated() {
    this.isPlaying = !this.$parent.$parent.$parent.$parent.$refs.MusicPlay.paused;
  },
  methods: {
    onClick() {
      // 返回并定位至上次页面
      if (this.lastPageNum) {
        Bus.$emit('lastPageNum', this.lastPageNum);
      }
      this.$router.go(-1);
    },
    onPlay() {
      let player = this.$parent.$parent.$parent.$parent.$refs.MusicPlay;
      if (player.paused) {
        player.play();
        this.isPlaying = true;
      } else {
        player.pause();
        this.isPlaying = false;
      }
    }
  }
};
</script>

<style lang="scss" scoped>

@import '/src/common/font/font.css';

.header {
  .header-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
  }
  .gh {
    position: absolute;
    width: 160px;
    height: 160px;
    top: 30px;
    left: 1440px;
  }
  .title1 {
    position: absolute;
    top: 45px;
    left: 1620px;
    text-align: center;
    font-family: AL-BL;
    opacity: 0.9;
    text-shadow: 0px 2px 6px rgba(0,0,0,0.5);
    color: #FFFFFF;
    font-weight: 900;
    font-size: 60px;
    font-style: normal;
    text-decoration: none;
    text-align: left;
  }
  .title2 {
    position: absolute;
    top: 130px;
    left: 1772px;
    text-shadow: 0px 2px 6px rgba(0,0,0,0.5);
    font-family: AL-L;
    color: #FFFFFF;
    font-weight: 300;
    font-size: 34px;
    font-style: normal;
    text-decoration: none;
    text-align: left;
  }
  .music {
    position: absolute;
    top: 45px;
    right: 45px;
    width: 60px;
    height: 60px;
    text-align: center;
    background-color: red;
    border-radius: 30px;
    box-shadow: 0px 5px 10px rgba(0,0,0,0.5);
    z-index: 99999;
    .music-logo {
      margin-top: 10px;
      width: 40px;
      height: 40px;
    }
  }
  .rolling {
    animation: rotateAnimation 5s infinite linear;
  }
  @keyframes rotateAnimation {
    0% { transform: rotate(0deg); } /* 初始状态，不进行任何旋转 */
    100% { transform: rotate(360deg); } /* 最后状态，将图像旋转到360度（一周） */
  }

}

</style>
