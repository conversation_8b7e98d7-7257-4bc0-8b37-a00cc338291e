import request from '@/utils/request'

// 查询群众建议列表
export function listAdvice(query) {
  return request({
    url: '/renda/advice/list',
    method: 'get',
    params: query
  })
}

// 查询群众建议详细
export function getAdvice(id) {
  return request({
    url: '/renda/advice/' + id,
    method: 'get'
  })
}

// 新增群众建议
export function addAdvice(data) {
  return request({
    url: '/renda/advice',
    method: 'post',
    data: data
  })
}

// 修改群众建议
export function updateAdvice(data) {
  return request({
    url: '/renda/advice',
    method: 'put',
    data: data
  })
}

// 删除群众建议
export function delAdvice(id) {
  return request({
    url: '/renda/advice/' + id,
    method: 'delete'
  })
}

// 查询群众建议列表
export function listAdviceWithFeedback(query) {
  return request({
    url: '/renda/advice/listWithFeedback',
    method: 'get',
    params: query
  })
}
