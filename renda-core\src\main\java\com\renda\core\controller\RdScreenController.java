package com.renda.core.controller;

import com.renda.common.annotation.Anonymous;
import com.renda.common.annotation.Log;
import com.renda.common.core.controller.BaseController;
import com.renda.common.core.domain.AjaxResult;
import com.renda.common.core.domain.entity.RdStation;
import com.renda.common.core.page.TableDataInfo;
import com.renda.common.enums.BusinessType;
import com.renda.common.utils.poi.ExcelUtil;
import com.renda.core.domain.RdAdvice;
import com.renda.core.domain.RdDeputy;
import com.renda.core.domain.RdJob;
import com.renda.core.domain.RdRecom;
import com.renda.core.domain.vo.RdAdviceWithFeedback;
import com.renda.core.service.*;
import com.renda.system.domain.SysNotice;
import com.renda.system.service.ISysNoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 人大代表管理Controller
 *
 * <AUTHOR>
 * @date 2023-10-23
 */
@RestController
@RequestMapping("/renda/screen")
public class RdScreenController extends BaseController
{
    @Autowired
    private IRdAdviceService rdAdviceService;

    @Autowired
    private IRdDeputyService rdDeputyService;

    @Autowired
    private IRdRecomService rdRecomService;

    @Autowired
    private IRdJobService rdJobService;

    @Autowired
    private IRdStationService stationService;

    @Autowired
    private ISysNoticeService noticeService;

    /**
     * 获取群众建议详细信息
     */
    @Anonymous
    @GetMapping(value = "/advice/{id}")
    public AjaxResult getAdvice(@PathVariable("id") Long id)
    {
        return success(rdAdviceService.selectRdAdviceById(id));
    }

    /**
     * 查询群众建议列表
     */
    @Anonymous
    @GetMapping("/advice/listWithFeedback")
    public TableDataInfo listWithFeedback(RdAdvice rdAdvice)
    {
        startPage();
        List<RdAdviceWithFeedback> list = rdAdviceService.selectRdAdviceWithFeedbackList(rdAdvice);
        return getDataTable(list);
    }

    /**
     * 获取人大代表管理详细信息
     */
    @Anonymous
    @GetMapping(value = "/deputy/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rdDeputyService.selectRdDeputyById(id));
    }

    /**
     * 获取人大代表履职统计信息
     */
    @Anonymous
    @GetMapping(value = "/deputy/deputyStat/{id}")
    public AjaxResult getDeputyStat(@PathVariable("id") Long id)
    {
        return success(rdDeputyService.getDeputyStat(id));
    }

    /**
     * 查询代表建议列表
     */
    @Anonymous
    @GetMapping("/recom/list")
    public TableDataInfo list(RdRecom rdRecom)
    {
        startPage();
        List<RdRecom> list = rdRecomService.selectRdRecomList(rdRecom);
        return getDataTable(list);
    }

    /**
     * 查询履职工作列表
     */
    @Anonymous
    @GetMapping("/Job/list")
    public TableDataInfo list(RdJob rdJob)
    {
        startPage();
        List<RdJob> list = rdJobService.selectRdJobList(rdJob);
        return getDataTable(list);
    }

    /**
     * 查询人大代表管理列表
     */
    @Anonymous
    @GetMapping("/deputy/list2")
    public TableDataInfo list2(RdDeputy rdDeputy)
    {
        startPage();
        List<RdDeputy> list = rdDeputyService.selectRdDeputyList2(rdDeputy);
        return getDataTable(list);
    }

    /**
     * 获取站点树列表
     */
    @Anonymous
    @GetMapping("/station/stationTree")
    public AjaxResult stationTree(RdStation station)
    {
        return success(stationService.selectStationTreeList(station));
    }

    /**
     * 获取履职工作详细信息
     */
    @Anonymous
    @GetMapping(value = "/Job/Ext/{id}")
    public AjaxResult getJobExtInfo(@PathVariable("id") Long id)
    {
        return success(rdJobService.selectJobExtById(id));
    }

    /**
     * 获取代表履职统计信息
     */
    @Anonymous
    @GetMapping(value = "/Job/stat/")
    public AjaxResult getJobStat()
    {
        return success(rdJobService.getJobStat());
    }

    /**
     * 获取通知公告列表
     */
    @Anonymous
    @GetMapping("/notice/list")
    public TableDataInfo list(SysNotice notice)
    {
        startPage();
        List<SysNotice> list = noticeService.selectNoticeList(notice);
        return getDataTable(list);
    }

    /**
     * 根据通知公告编号获取详细信息
     */
    @Anonymous
    @GetMapping(value = "/notice/{noticeId}")
    public AjaxResult getNoticeInfo(@PathVariable Long noticeId)
    {
        return success(noticeService.selectNoticeById(noticeId));
    }

}
