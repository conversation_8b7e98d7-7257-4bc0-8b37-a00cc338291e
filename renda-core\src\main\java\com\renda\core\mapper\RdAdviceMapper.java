package com.renda.core.mapper;

import java.util.List;
import com.renda.core.domain.RdAdvice;
import com.renda.core.domain.vo.AdviceExtVO;
import com.renda.core.domain.vo.RdAdviceWithFeedback;
import com.renda.core.domain.vo.TodoInfoVO;

/**
 * 群众建议Mapper接口
 *
 * <AUTHOR>
 * @date 2023-10-30
 */
public interface RdAdviceMapper
{
    /**
     * 查询群众建议
     *
     * @param id 群众建议主键
     * @return 群众建议
     */
    public RdAdvice selectRdAdviceById(Long id);

    /**
     * 查询群众建议
     *
     * @param id 群众建议主键
     * @return 群众建议
     */
    public RdAdviceWithFeedback selectRdAdviceExtById(Long id);

    /**
     * 查询群众建议列表
     *
     * @param rdAdvice 群众建议
     * @return 群众建议集合
     */
    public List<RdAdvice> selectRdAdviceList(RdAdvice rdAdvice);

    /**
     * 新增群众建议
     *
     * @param rdAdvice 群众建议
     * @return 结果
     */
    public int insertRdAdvice(RdAdvice rdAdvice);

    /**
     * 修改群众建议
     *
     * @param rdAdvice 群众建议
     * @return 结果
     */
    public int updateRdAdvice(RdAdvice rdAdvice);

    /**
     * 删除群众建议
     *
     * @param id 群众建议主键
     * @return 结果
     */
    public int deleteRdAdviceById(Long id);

    /**
     * 批量删除群众建议
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRdAdviceByIds(Long[] ids);

    /***
     * 获取意见建议接口-所有
     * @param advice 意见建议信息
     * @return 意见建议列表
     */
    List<AdviceExtVO> selectAdviceExtList(RdAdvice advice);

    /**
     * 查询群众建议列表
     */
    List<RdAdviceWithFeedback> selectRdAdviceWithFeedbackList(RdAdvice rdAdvice);

    /***
     * 获取待办信息接口
     * @return 待办信息
     */
    TodoInfoVO getTodoInfo(Long deputyId);

}
