{"remainingRequest": "D:\\renda\\renda-back\\renda-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\renda\\renda-back\\renda-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\renda\\renda-back\\renda-ui\\src\\api\\renda\\advice.js", "dependencies": [{"path": "D:\\renda\\renda-back\\renda-ui\\src\\api\\renda\\advice.js", "mtime": 1752728908510}, {"path": "D:\\renda\\renda-back\\renda-ui\\babel.config.js", "mtime": 1697858667444}, {"path": "D:\\renda\\renda-back\\renda-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\renda\\renda-back\\renda-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\renda\\renda-back\\renda-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9yZW5kYS9yZW5kYS1iYWNrL3JlbmRhLXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5hZGRBZHZpY2UgPSBhZGRBZHZpY2U7CmV4cG9ydHMuZGVsQWR2aWNlID0gZGVsQWR2aWNlOwpleHBvcnRzLmdldEFkdmljZSA9IGdldEFkdmljZTsKZXhwb3J0cy5saXN0QWR2aWNlID0gbGlzdEFkdmljZTsKZXhwb3J0cy5saXN0QWR2aWNlV2l0aEZlZWRiYWNrID0gbGlzdEFkdmljZVdpdGhGZWVkYmFjazsKZXhwb3J0cy51cGRhdGVBZHZpY2UgPSB1cGRhdGVBZHZpY2U7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyDmn6Xor6LnvqTkvJflu7rorq7liJfooagKZnVuY3Rpb24gbGlzdEFkdmljZShxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3JlbmRhL2FkdmljZS9saXN0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOafpeivoue+pOS8l+W7uuiuruivpue7hgpmdW5jdGlvbiBnZXRBZHZpY2UoaWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9yZW5kYS9hZHZpY2UvJyArIGlkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmlrDlop7nvqTkvJflu7rorq4KZnVuY3Rpb24gYWRkQWR2aWNlKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9yZW5kYS9hZHZpY2UnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOS/ruaUuee+pOS8l+W7uuiurgpmdW5jdGlvbiB1cGRhdGVBZHZpY2UoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3JlbmRhL2FkdmljZScsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDliKDpmaTnvqTkvJflu7rorq4KZnVuY3Rpb24gZGVsQWR2aWNlKGlkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvcmVuZGEvYWR2aWNlLycgKyBpZCwKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQoKLy8g5p+l6K+i576k5LyX5bu66K6u5YiX6KGoCmZ1bmN0aW9uIGxpc3RBZHZpY2VXaXRoRmVlZGJhY2socXVlcnkpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9yZW5kYS9hZHZpY2UvbGlzdFdpdGhGZWVkYmFjaycsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listAdvice", "query", "request", "url", "method", "params", "getAdvice", "id", "addAdvice", "data", "updateAdvice", "delAdvice", "listAdviceWithFeedback"], "sources": ["D:/renda/renda-back/renda-ui/src/api/renda/advice.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询群众建议列表\r\nexport function listAdvice(query) {\r\n  return request({\r\n    url: '/renda/advice/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询群众建议详细\r\nexport function getAdvice(id) {\r\n  return request({\r\n    url: '/renda/advice/' + id,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增群众建议\r\nexport function addAdvice(data) {\r\n  return request({\r\n    url: '/renda/advice',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改群众建议\r\nexport function updateAdvice(data) {\r\n  return request({\r\n    url: '/renda/advice',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除群众建议\r\nexport function delAdvice(id) {\r\n  return request({\r\n    url: '/renda/advice/' + id,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 查询群众建议列表\r\nexport function listAdviceWithFeedback(query) {\r\n  return request({\r\n    url: '/renda/advice/listWithFeedback',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,SAASA,CAACC,EAAE,EAAE;EAC5B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB,GAAGI,EAAE;IAC1BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,SAASA,CAACC,IAAI,EAAE;EAC9B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,YAAYA,CAACD,IAAI,EAAE;EACjC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,SAASA,CAACJ,EAAE,EAAE;EAC5B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB,GAAGI,EAAE;IAC1BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,sBAAsBA,CAACX,KAAK,EAAE;EAC5C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ"}]}