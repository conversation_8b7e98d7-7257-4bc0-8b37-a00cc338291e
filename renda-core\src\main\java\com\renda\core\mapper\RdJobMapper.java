package com.renda.core.mapper;

import java.util.List;
import com.renda.core.domain.RdJob;
import com.renda.core.domain.vo.JobStatVO;

/**
 * 履职工作Mapper接口
 *
 * <AUTHOR>
 * @date 2023-11-12
 */
public interface RdJobMapper
{
    /**
     * 查询履职工作
     *
     * @param id 履职工作主键
     * @return 履职工作
     */
    public RdJob selectRdJobById(Long id);

    /**
     * 查询履职工作列表
     *
     * @param rdJob 履职工作
     * @return 履职工作集合
     */
    public List<RdJob> selectRdJobList(RdJob rdJob);

    /**
     * 新增履职工作
     *
     * @param rdJob 履职工作
     * @return 结果
     */
    public int insertRdJob(RdJob rdJob);

    /**
     * 修改履职工作
     *
     * @param rdJob 履职工作
     * @return 结果
     */
    public int updateRdJob(RdJob rdJob);

    /**
     * 删除履职工作
     *
     * @param id 履职工作主键
     * @return 结果
     */
    public int deleteRdJobById(Long id);

    /**
     * 批量删除履职工作
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRdJobByIds(Long[] ids);

    /***
     * 获取履职工作详情
     * @param id 履职工作ID
     */
    RdJob selectJobExtById(Long id);

    /**
     * 获取人大代表履职统计信息
     */
    List<JobStatVO> getJobStat();

}
