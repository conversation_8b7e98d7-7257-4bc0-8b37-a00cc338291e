package com.renda.core.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.renda.common.annotation.Excel;
import com.renda.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 活动报名对象 rd_activity_registration
 *
 * <AUTHOR>
 * @date 2023-11-14
 */
public class RdActivityRegistration extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 活动ID */
    private Long activityId;

    /** 代表ID */
    private Long deputyId;

    /** 代表姓名 */
    @Excel(name = "代表姓名")
    private String deputyName;

    /** 电话号码 */
    @Excel(name = "电话号码")
    private String phone;

    /** 报名类型 1-报名；2-请假 */
    @Excel(name = "报名类型", readConverterExp = "0=未报名,1=报名,2=请假")
    private String registrationType;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报名时间", width = 20, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Excel.Type.EXPORT)
    private Date createTime;

    /** 短信发送次数 */
    @Excel(name = "短信发送次数")
    private Integer smsSendCount;

    /** 短信发送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "短信发送时间", width = 20, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Excel.Type.EXPORT)
    private Date smsSendTime;

    /** 短信发送状态 */
    @Excel(name = "短信发送状态", readConverterExp = "-1=未发送,0=发送成功")
    private Integer smsSendStatus;

    /** 短信回执编号 */
    private String smsMsgNo;

    /** 短信接口回执状态描述 */
    private String smsMsg;



    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setActivityId(Long activityId)
    {
        this.activityId = activityId;
    }

    public Long getActivityId()
    {
        return activityId;
    }
    public void setDeputyId(Long deputyId)
    {
        this.deputyId = deputyId;
    }

    public Long getDeputyId()
    {
        return deputyId;
    }
    public void setRegistrationType(String registrationType)
    {
        this.registrationType = registrationType;
    }

    public String getRegistrationType()
    {
        return registrationType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("activityId", getActivityId())
            .append("deputyId", getDeputyId())
            .append("registrationType", getRegistrationType())
            .append("remark", getRemark())
            .append("createTime", getCreateTime())
            .toString();
    }

    public Integer getSmsSendCount() {
        return smsSendCount;
    }

    public void setSmsSendCount(Integer smsSendCount) {
        this.smsSendCount = smsSendCount;
    }

    public Integer getSmsSendStatus() {
        return smsSendStatus;
    }

    public void setSmsSendStatus(Integer smsSendStatus) {
        this.smsSendStatus = smsSendStatus;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getSmsMsgNo() {
        return smsMsgNo;
    }

    public void setSmsMsgNo(String smsMsgNo) {
        this.smsMsgNo = smsMsgNo;
    }

    public String getSmsMsg() {
        return smsMsg;
    }

    public void setSmsMsg(String smsMsg) {
        this.smsMsg = smsMsg;
    }

    public String getDeputyName() {
        return deputyName;
    }

    public void setDeputyName(String deputyName) {
        this.deputyName = deputyName;
    }

    public Date getSmsSendTime() {
        return smsSendTime;
    }

    public void setSmsSendTime(Date smsSendTime) {
        this.smsSendTime = smsSendTime;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
