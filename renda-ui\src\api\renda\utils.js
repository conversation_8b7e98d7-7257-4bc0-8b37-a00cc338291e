// 格式化日期
export function formatYearMonth(adate) {
  if (adate) {
    let date = new Date(adate);
    let year = date.getFullYear();
    let month = date.getMonth() + 1;
    return year + '年' + month + '月';
  }
}

// 格式化可读性强的日期
export function formatReadableDate(adate) {
  if (adate) {
    let now = new Date();
    let nowYear = now.getFullYear();
    let date = new Date(adate);
    let year = date.getFullYear();
    let month = date.getMonth() + 1;
    let day = date.getDate();
    if (nowYear === year) {
      return month + '月' + day + '日';
    } else {
      return year + '年' + month + '月' + day + '日';
    }
  }
}
