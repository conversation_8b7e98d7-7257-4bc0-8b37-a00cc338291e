<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renda.core.mapper.RdRecomMapper">

    <resultMap type="RdRecom" id="RdRecomResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="recomType"    column="recom_type"    />
        <result property="session"    column="session"    />
        <result property="times"    column="times"    />
        <result property="host"    column="host"    />
        <result property="cohost"    column="cohost"    />
        <result property="recomFileUrl"    column="recom_file_url"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <collection property="deputyList" javaType="List" ofType="RdRecomDeputy"
                    column="id" select="selectRecomDeputyListById"  />
        <collection property="attachmentList" javaType="List" ofType="RdRecomAttachment"
                    column="id" select="selectRecomAttachmentListById"  />
    </resultMap>

    <resultMap id="RdRecomDeputyResult" type="RdRecomDeputy">
        <result property="id"    column="id"    />
        <result property="recomId"    column="d_recom_id"    />
        <result property="deputyId"    column="deputy_id"    />
        <result property="hasReply"    column="has_reply"    />
        <result property="replyRate"    column="reply_rate"    />
        <result property="deputyName"    column="deputy_name"    />
        <result property="deputyAvatar"    column="deputy_avatar"    />
    </resultMap>

    <resultMap id="RdRecomAttachmentResult" type="RdRecomAttachment">
        <result property="id"    column="id"    />
        <result property="recomId"    column="recom_id"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileUrl"    column="file_url"    />
    </resultMap>

    <select id="selectRecomDeputyListById" resultMap="RdRecomDeputyResult">
        select id, recom_id, deputy_id, has_reply, reply_rate,
               (select name from rd_deputy where id = d.deputy_id) as deputy_name,
               (select avatar from rd_deputy where id = d.deputy_id) as deputy_avatar
        from rd_recom_deputy d
        where recom_id = #{id}
    </select>

    <select id="selectRecomAttachmentListById" resultMap="RdRecomAttachmentResult">
        select id, recom_id, file_type, file_name, file_url
        from rd_recom_attachment
        where recom_id = #{id}
    </select>

    <sql id="selectRdRecomVo">
        select r.id, r.title, r.recom_type, r.session, r.times, r.host, r.cohost, r.recom_file_url, r.create_by,
               r.create_time, r.update_by, r.update_time
        from rd_recom r
    </sql>

    <select id="selectRdRecomList" parameterType="RdRecom" resultMap="RdRecomResult">
        <include refid="selectRdRecomVo"/>
        <where>
            <if test="title != null and title != ''"> and r.title like concat('%', #{title}, '%')</if>
            <if test="host != null and host != ''"> and r.host like concat('%', #{host}, '%')</if>
            <if test="cohost != null and cohost != ''"> and r.cohost like concat('%', #{cohost}, '%')</if>
            <if test="recomType != null and recomType != ''"> and r.recom_type = #{recomType}</if>
            <if test="session != null and session != ''"> and r.session = #{session}</if>
            <if test="times != null and times != ''"> and r.times = #{times}</if>
            <if test="deputyId != null and deputyId != ''"> and r.id in (select recom_id from rd_recom_deputy where deputy_id = #{deputyId})</if>
        </where>
        order by r.id desc
    </select>

    <select id="selectRdRecomById" parameterType="Long" resultMap="RdRecomResult">
        <include refid="selectRdRecomVo"/>
        where r.id = #{id}
    </select>

    <select id="selectHostList" parameterType="String" resultType="java.lang.String">
        SELECT `host` FROM (
            SELECT `host` AS `host` FROM `rd_recom` WHERE `host` IS NOT NULL
            UNION
            SELECT `cohost` AS `host` FROM `rd_recom` WHERE `cohost` IS NOT NULL
            ) AS A
        WHERE `host` LIKE concat('%', #{keyword}, '%')
    </select>

    <insert id="insertRdRecom" parameterType="RdRecom" useGeneratedKeys="true" keyProperty="id">
        insert into rd_recom
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="recomType != null and recomType != ''">recom_type,</if>
            <if test="session != null and session != ''">session,</if>
            <if test="times != null and times != ''">times,</if>
            <if test="host != null and host != ''">host,</if>
            <if test="cohost != null and cohost != ''">cohost,</if>
            <if test="recomFileUrl != null">recom_file_url,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="recomType != null and recomType != ''">#{recomType},</if>
            <if test="session != null and session != ''">#{session},</if>
            <if test="times != null and times != ''">#{times},</if>
            <if test="host != null and host != ''">#{host},</if>
            <if test="cohost != null and cohost != ''">#{cohost},</if>
            <if test="recomFileUrl != null">#{recomFileUrl},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRdRecom" parameterType="RdRecom">
        update rd_recom
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="recomType != null and recomType != ''">recom_type = #{recomType},</if>
            <if test="session != null and session != ''">session = #{session},</if>
            <if test="times != null and times != ''">times = #{times},</if>
            <if test="host != null and host != ''">host = #{host},</if>
            <if test="cohost != null and cohost != ''">cohost = #{cohost},</if>
            <if test="recomFileUrl != null">recom_file_url = #{recomFileUrl},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRdRecomById" parameterType="Long">
        delete from rd_recom where id = #{id}
    </delete>

    <delete id="deleteRdRecomByIds" parameterType="String">
        delete from rd_recom where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
