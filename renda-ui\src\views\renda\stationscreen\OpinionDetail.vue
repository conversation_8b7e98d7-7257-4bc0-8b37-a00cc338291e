<template>
  <div class="opinion-detail">
    <app-header :showBackBtn="true" />

    <main class="content">
      <div class="background-elements">
        <div class="circle circle-1"></div>
        <div class="circle circle-2"></div>
        <div class="circle circle-3"></div>
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
      </div>

      <div class="page-title">
        <h1>民意详情</h1>
        <div class="title-decoration"></div>
      </div>

      <div class="detail-container" v-if="!loading && opinion">
        <!-- 意见信息和详细内容 -->
        <div class="opinion-main-section">
          <div class="opinion-header">
            <div class="opinion-meta">
              <span class="opinion-id">#{{ opinion.id }}</span>
              <span class="opinion-category">{{ opinion.category }}</span>
              <span class="opinion-date">{{ opinion.submitDate }}</span>
              <span class="opinion-status" :class="opinion.status">{{ opinion.status }}</span>
            </div>
            <!-- 已处理意见显示群众服务评分 -->
            <div class="service-rating" v-if="opinion.statusCode === '2' && opinion.rating">
              <div class="rating-stars">
                <span v-for="n in 5" :key="n" class="star" :class="{ filled: n <= (opinion.rating || 0) }">★</span>
              </div>
              <span class="rating-text">{{ opinion.rating }}分</span>
            </div>
          </div>
          <h1 class="opinion-title">{{ opinion.title }}</h1>

          <div class="content-box">
            <p class="opinion-text">{{ opinion.content }}</p>
            <div class="opinion-submitter">
              <div class="submitter-info">
                <span><strong>提交人：</strong>{{ opinion.submitter ? opinion.submitter : '匿名' }}</span>
                <span><strong>联系方式：</strong>{{ opinion.contact ? opinion.contact : '未填写' }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 代表反馈和对话交流 -->
        <div class="feedback-replies-section" v-if="opinion.feedback || (opinion.replies && opinion.replies.length > 0)">
          <div class="section-title">
            <h2>代表反馈与交流</h2>
          </div>

          <!-- 代表反馈 -->
          <div class="feedback-card" v-if="opinion.feedback">
            <div class="feedback-header">
              <div class="representative-info">
                <h3>{{ opinion.feedback.representative }}</h3>
                <span class="feedback-date">{{ opinion.feedback.date }}</span>
              </div>
            </div>
            <div class="feedback-content">
              <p>{{ opinion.feedback.content }}</p>
            </div>
          </div>

          <!-- 对话交流 -->
          <div class="replies-container" v-if="opinion.replies && opinion.replies.length > 0">
            <div class="replies-title" v-if="opinion.feedback">
              <h4>对话交流</h4>
            </div>
            <div
              class="reply-item"
              v-for="reply in opinion.replies"
              :key="reply.id"
              :class="reply.type"
            >
              <div class="reply-header">
                <span class="reply-author">{{ reply.author }}</span>
                <span class="reply-date">{{ reply.date }}</span>
              </div>
              <div class="reply-content">{{ reply.content }}</div>
            </div>
          </div>
        </div>

        <!-- 如果没有反馈，显示状态信息 -->
        <div class="no-feedback-section" v-if="!opinion.feedback">
          <div class="section-title">
            <h2>处理状态</h2>
          </div>
          <div class="status-info">
            <div class="status-card">
              <i class="el-icon-time"></i>
              <div class="status-content">
                <h3>{{ opinion.status }}</h3>
                <p v-if="opinion.status === '待处理'">您的建议已提交成功，我们将尽快安排相关代表进行处理。</p>
                <p v-else-if="opinion.status === '处理中'">相关代表正在处理您的建议，请耐心等待反馈结果。</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <button class="btn btn-secondary" @click="goBack">
            <i class="el-icon-arrow-left"></i>
            返回列表
          </button>
        </div>
      </div>

      <!-- 加载状态 -->
      <div class="loading-section" v-if="loading || !opinion">
        <div class="loading-card">
          <i class="el-icon-loading"></i>
          <p>正在加载建议详情...</p>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import AppHeader from '@/components/StationScreen/AppHeader.vue'
import { getAdviceDetail } from '@/api/renda/stationscreen'

export default {
  name: 'OpinionDetail',
  components: {
    AppHeader
  },
  data() {
    return {
      opinion: null,
      loading: false
    }
  },
  created() {

  },
  mounted() {
    this.$nextTick(() => {
      // 禁用右键菜单
      document.oncontextmenu = function() {
        return false;
      };
    });

    document.addEventListener('keydown', this.handleKeydown)
  },
  activated() {
    // 每次激活页面时都刷新数据
    // 重新获取路由参数
    this.opinionId = this.$route.query.opinionId || this.$route.params.id || '0'
    this.loadOpinionDetail()
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.handleKeydown)
  },
  methods: {
    // 处理键盘事件
    handleKeydown(event) {
      // ESC键返回列表
      if (event.key === 'Escape') {
        this.goBack()
      }
    },

    async loadOpinionDetail() {
      if (!this.opinionId || this.opinionId === '0') {
        this.$message.error('建议ID不能为空')
        this.goBack()
        return
      }

      this.loading = true
      try {
        const response = await getAdviceDetail(this.opinionId)
        if (response.code === 200) {
          this.opinion = this.formatOpinionData(response.data)
        } else {
          this.$message.error(response.msg || '获取建议详情失败')
          this.goBack()
        }
      } catch (error) {
        console.error('获取建议详情失败:', error)
        this.$message.error('获取建议详情失败')
        this.goBack()
      } finally {
        this.loading = false
      }
    },

    // 格式化后端返回的数据，适配前端展示格式
    formatOpinionData(data) {
      const opinion = {
        id: data.id,
        category: this.getCategoryName(data.category),
        title: data.title,
        content: data.content,
        submitter: data.submitter,
        contact: data.contact,
        submitDate: this.formatDate(data.submitDate),
        status: this.getStatusText(data.status),
        statusCode: data.status, // 保存原始状态码，用于条件判断
        rating: data.rating
      }

             // 处理反馈信息
       if (data.feedbacks && data.feedbacks.length > 0) {
         // 第一个反馈作为主要反馈显示
         const firstFeedback = data.feedbacks[0]
         opinion.feedback = {
           representative: firstFeedback.representative,
           date: this.formatDate(firstFeedback.date),
           content: firstFeedback.content
         }

         // 如果有多个反馈，其余的作为对话交流
         if (data.feedbacks.length > 1) {
           opinion.replies = data.feedbacks.slice(1).map((feedback, index) => ({
             id: index + 2,
             type: this.getUserType(feedback.userType),
             author: feedback.representative,
             date: this.formatDate(feedback.date),
             content: feedback.content
           }))
         }
       }

      return opinion
    },

    // 获取分类名称
    getCategoryName(category) {
      const categoryMap = {
        '1': '经济发展',
        '2': '民生保障',
        '3': '城市建设',
        '4': '环境保护',
        '5': '教育文化',
        '6': '法治建设',
        '7': '矛盾化解',
        '8': '窗口服务'
      }
      return categoryMap[category] || category
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        '0': '待处理',
        '1': '处理中',
        '2': '已处理'
      }
      return statusMap[status] || '待处理'
    },

         // 获取用户类型
     getUserType(userType) {
       // 后端定义：0-代表；1-群众
       if (userType === 1) {
         return 'citizen'
       }
       return 'representative'
     },

    // 格式化日期 - 与OpinionCollection.vue保持一致
    formatDate(dateString) {
      if (!dateString) return ''

      try {
        const date = new Date(dateString)

        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          return dateString
        }

        // 格式化为 YYYY-MM-DD HH:mm 格式
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')

        return `${year}-${month}-${day} ${hours}:${minutes}`
      } catch (error) {
        console.error('日期格式化失败:', error)
        return dateString
      }
    },

    goBack() {
      this.$router.back()
    }
  },
  beforeRouteEnter(to, from, next) {
    // 进入路由时
    next(vm => {
      vm.opinionId = to.query.opinionId || to.params.id || '0'
      vm.loadOpinionDetail()
    })
  },
  beforeRouteUpdate(to, from, next) {
    // 路由参数变化时刷新数据
    this.opinionId = to.query.opinionId || to.params.id || '0'
    this.loadOpinionDetail()
    next()
  },
  watch: {
    '$route'() {
      this.loadOpinionDetail()
    }
  }
}
</script>

<style lang="scss" scoped>
.opinion-detail {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f8f8f8, #efefef);
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  margin: 0;
  padding: 0;

  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.content {
  flex: 1;
  padding: 20px 800px 40px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;

  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }

  .background-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 0;
    pointer-events: none;

    .circle {
      position: absolute;
      border-radius: 50%;
      opacity: 0.06;

      &.circle-1 {
        width: 600px;
        height: 600px;
        background: #d71718;
        top: -200px;
        left: -200px;
      }

      &.circle-2 {
        width: 500px;
        height: 500px;
        background: #d71718;
        bottom: 20%;
        right: 15%;
      }

      &.circle-3 {
        width: 300px;
        height: 300px;
        background: #d71718;
        bottom: 35%;
        left: 5%;
      }
    }

    .shape {
      position: absolute;
      opacity: 0.04;
      background: #d71718;

      &.shape-1 {
        width: 500px;
        height: 500px;
        transform: rotate(45deg);
        top: 15%;
        right: -300px;
      }

      &.shape-2 {
        width: 400px;
        height: 400px;
        transform: rotate(30deg);
        bottom: -50px;
        left: 30%;
      }
    }
  }

  .page-title {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
    z-index: 1;

    h1 {
      color: #d71718;
      font-size: 48px;
      font-weight: 500;
      margin: 0 0 20px;
      letter-spacing: 2px;
      font-family: 'AL-BL' !important;
    }

    .title-decoration {
      width: 120px;
      height: 4px;
      background: linear-gradient(135deg, #d71718, #b41616);
      margin: 0 auto 20px;
      border-radius: 2px;
    }

    p {
      color: #666;
      font-size: 18px;
      margin: 0 0 30px;
      font-family: 'AL-L' !important;
    }
  }

  .detail-container {
    position: relative;
    z-index: 1;
    max-width: calc(100% - 200px);
    margin: 0 auto;
  }

  .opinion-main-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #d71718;
    font-family: 'AL-L';

    .opinion-header {
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
    }

    .opinion-meta {
      display: flex;
      gap: 20px;
      align-items: center;
      flex-wrap: wrap;

      .opinion-id {
        color: #d71718;
        font-weight: 600;
        font-size: 20px;
      }

      .opinion-category {
        padding: 8px 16px;
        background: rgba(215, 23, 24, 0.1);
        color: #d71718;
        border-radius: 16px;
        font-size: 16px;
        font-weight: 500;
      }

      .opinion-date {
        color: #666;
        font-size: 18px;
      }

      .opinion-status {
        padding: 8px 18px;
        border-radius: 16px;
        font-size: 16px;
        font-weight: 500;

        &.待处理 {
          background: #fff3cd;
          color: #856404;
        }

        &.处理中 {
          background: #cce5ff;
          color: #004085;
        }

        &.已处理 {
          background: #d4edda;
          color: #155724;
        }
      }

    }

    .service-rating {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 5px;

      .rating-stars {
        display: flex;
        gap: 2px;

        .star {
          font-size: 18px;
          color: #ddd;

          &.filled {
            color: #ffd700;
          }
        }
      }

      .rating-text {
        font-size: 14px;
        color: #666;
        font-weight: 500;
      }
    }

    .opinion-title {
      color: #d71718;
      font-size: 36px;
      font-weight: 600;
      margin: 0 0 25px;
      font-family: 'AL-B' !important;
      line-height: 1.4;
    }

    .content-box {
      .opinion-text {
        color: #555;
        font-size: 24px;
        line-height: 1.6;
        margin: 0 0 25px;
        text-indent: 2em;
        text-align: left;
      }

      .opinion-submitter {
        .submitter-info {
          display: flex;
          gap: 30px;
          color: #666;
          font-size: 30px;
          flex-wrap: wrap;

          strong {
            color: #333;
          }
        }
      }
    }
  }

  .section-title {
    margin-bottom: 25px;

    h2 {
      color: #d71718;
      font-size: 28px;
      font-weight: 600;
      margin: 0 0 15px;
      font-family: 'AL-B' !important;
    }

    .title-line {
      width: 80px;
      height: 4px;
      background: linear-gradient(90deg, #d71718, #b41616);
      border-radius: 2px;
    }
  }

  .feedback-replies-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    font-family: 'AL-L';

    .feedback-card {
      background: rgba(215, 23, 24, 0.05);
      border-radius: 16px;
      padding: 25px;
      border-left: 4px solid #d71718;
      margin-bottom: 25px;

      .feedback-header {
        margin-bottom: 20px;

        .representative-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-wrap: wrap;
          gap: 15px;

          h3 {
            color: #d71718;
            font-size: 32px;
            font-weight: 600;
            margin: 0;
            font-family: 'AL-B' !important;
          }

          .feedback-date {
            color: #666;
            font-size: 24px;
          }
        }
      }

              .feedback-content {
          p {
            color: #666;
            font-size: 32px;
            line-height: 1.6;
            margin: 0;
            text-align: left;
            text-indent: 2em;
          }
        }
    }

    .replies-container {
        font-family: 'AL-L';

      .replies-title {
        margin-bottom: 20px;

        h4 {
          color: #d71718;
          font-size: 32px;
          font-weight: 600;
          margin: 0;
          font-family: 'AL-B' !important;
        }
      }

      .reply-item {
        padding: 18px 20px;
        margin-bottom: 15px;
        border-radius: 12px;
        position: relative;

        &.citizen {
          background: rgba(108, 117, 125, 0.1);
          margin-left: 0;
          margin-right: 50px;
        }

        &.representative {
          background: rgba(215, 23, 24, 0.05);
          margin-left: 50px;
          margin-right: 0;
        }

        .reply-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .reply-author {
            font-weight: 600;
            font-size: 28px;
            color: #333;
          }

          .reply-date {
            font-size: 24px;
            color: #999;
          }
        }

        .reply-content {
          font-size: 30px;
          line-height: 1.6;
          color: #555;
          text-align: left;
          text-indent: 2em;
        }
      }
    }
  }

  .no-feedback-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    font-family: 'AL-L';

    .status-info {
      .status-card {
        display: flex;
        align-items: center;
        gap: 20px;
        padding: 20px;
        background: rgba(215, 23, 24, 0.05);
        border-radius: 12px;
        border-left: 4px solid #d71718;

        i {
          font-size: 36px;
          color: #d71718;
        }

        .status-content {
          h3 {
            color: #d71718;
            font-size: 30px;
            font-weight: 600;
            margin: 0 0 8px;
            text-align: left;
          }

          p {
            color: #666;
            font-size: 30px;
            margin: 0;
            line-height: 1.5;
          }
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-top: 40px;
    flex-wrap: wrap;

    .btn {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 15px 30px;
      border: none;
      border-radius: 12px;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s;
      font-family: 'AL-L';

      &.btn-secondary {
        background: #6c757d;
        color: #fff;

        &:hover {
          background: #5a6268;
          transform: translateY(-2px);
        }
      }

      &.btn-primary {
        background: linear-gradient(135deg, #d71718, #b41616);
        color: #fff;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(215, 23, 24, 0.3);
        }
      }
    }
  }

  .loading-section {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 60vh;
    position: relative;
    z-index: 1;

    .loading-card {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 12px;
      padding: 40px;
      text-align: center;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);

      i {
        font-size: 48px;
        color: #d71718;
        margin-bottom: 20px;
        display: block;
      }

      p {
        color: #666;
        font-size: 18px;
        margin: 0;
      }
    }
  }
}

/* 4K分辨率适配 */
@media (min-width: 3000px) {
  .content {
    padding: 80px 800px;

    .page-title {
      margin-bottom: 80px;

      h1 {
        font-size: 96px;
        margin: 0 0 30px;
      }

      .title-decoration {
        width: 160px;
        height: 6px;
        margin: 0 auto 30px;
      }

      p {
        font-size: 42px;
      }
    }

    .detail-container {
      max-width: calc(100% - 400px);
    }

    .opinion-main-section {
      padding: 50px;
      margin-bottom: 50px;
      border-radius: 20px;
      border-left-width: 6px;

      .opinion-header {
        margin-bottom: 25px;

        .service-rating {
          gap: 8px;

          .rating-stars {
            gap: 4px;

            .star {
              font-size: 32px;
            }
          }

          .rating-text {
            font-size: 24px;
          }
        }
      }

      .opinion-meta {
        gap: 30px;

        .opinion-id {
          font-size: 32px;
        }

        .opinion-category {
          padding: 12px 24px;
          font-size: 24px;
          border-radius: 20px;
        }

        .opinion-date {
          font-size: 24px;
        }

        .opinion-status {
          padding: 12px 24px;
          font-size: 24px;
          border-radius: 20px;
        }
      }

      .opinion-title {
        font-size: 48px;
        margin: 0 0 35px;
        text-align: center;
      }

      .content-box .opinion-text {
        font-size: 32px;
        margin: 0 0 35px;
      }

      .submitter-info {
        gap: 40px;
        font-size: 28px;
      }
    }

    .section-title {
      margin-bottom: 40px;

      h2 {
        font-size: 36px;
        margin: 0 0 20px;
      }

      .title-line {
        width: 100px;
        height: 6px;
      }
    }

    .feedback-replies-section,
    .no-feedback-section {
      padding: 50px;
      margin-bottom: 50px;
      border-radius: 20px;
    }

    .feedback-card {
      padding: 40px;
      border-radius: 20px;
      border-left-width: 6px;
      margin-bottom: 35px;

      .representative-info {
        h3 {
          font-size: 32px;
        }

        .feedback-date {
          font-size: 24px;
        }
      }

      .feedback-content {
        p {
          font-size: 32px;
          margin: 0;
        }
      }
    }

    .replies-container {
      .replies-title {
        margin-bottom: 30px;

        h4 {
          font-size: 32px;
        }
      }
    }

    .replies-container .reply-item {
      padding: 25px 30px;
      margin-bottom: 20px;
      border-radius: 16px;

      &.citizen {
        margin-right: 80px;
      }

      &.representative {
        margin-left: 80px;
      }

      .reply-header {
        margin-bottom: 15px;

        .reply-author {
          font-size: 28px;
        }

        .reply-date {
          font-size: 24px;
        }
      }

      .reply-content {
        font-size: 26px;
      }
    }

    .status-card {
      padding: 30px;
      border-radius: 16px;
      border-left-width: 6px;
      gap: 30px;

      i {
        font-size: 48px;
      }

      .status-content {
        h3 {
          font-size: 32px;
          margin: 0 0 12px;
        }

        p {
          font-size: 28px;
        }
      }
    }

    .action-buttons {
      gap: 30px;
      margin-top: 60px;

      .btn {
        gap: 12px;
        padding: 20px 40px;
        font-size: 28px;
        border-radius: 16px;
      }
    }

    .loading-card {
      padding: 60px;
      border-radius: 20px;

      i {
        font-size: 64px;
        margin-bottom: 30px;
      }

      p {
        font-size: 28px;
      }
    }
  }
}
</style>
