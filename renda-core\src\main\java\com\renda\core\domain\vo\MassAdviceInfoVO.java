package com.renda.core.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class MassAdviceInfoVO {

    /** 建议ID */
    private Long id;

    /** 人大代表ID */
    private Long deputyId;

    /** 人大代表头像 */
    private String avatar;

    /** 标题 */
    private String title;

    /** 内容 */
    private String content;

    /** 建议时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    /** 反馈数 */
    private Integer feedbackCount;

}
