package com.renda.core.service;

import java.util.List;
import com.renda.core.domain.RdNoticeFeedbackAttachment;

/**
 * 工作通知反馈意见附件Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-19
 */
public interface IRdNoticeFeedbackAttachmentService 
{
    /**
     * 查询工作通知反馈意见附件
     * 
     * @param id 工作通知反馈意见附件主键
     * @return 工作通知反馈意见附件
     */
    public RdNoticeFeedbackAttachment selectRdNoticeFeedbackAttachmentById(Long id);

    /**
     * 查询工作通知反馈意见附件列表
     * 
     * @param rdNoticeFeedbackAttachment 工作通知反馈意见附件
     * @return 工作通知反馈意见附件集合
     */
    public List<RdNoticeFeedbackAttachment> selectRdNoticeFeedbackAttachmentList(RdNoticeFeedbackAttachment rdNoticeFeedbackAttachment);

    /**
     * 新增工作通知反馈意见附件
     * 
     * @param rdNoticeFeedbackAttachment 工作通知反馈意见附件
     * @return 结果
     */
    public int insertRdNoticeFeedbackAttachment(RdNoticeFeedbackAttachment rdNoticeFeedbackAttachment);

    /**
     * 修改工作通知反馈意见附件
     * 
     * @param rdNoticeFeedbackAttachment 工作通知反馈意见附件
     * @return 结果
     */
    public int updateRdNoticeFeedbackAttachment(RdNoticeFeedbackAttachment rdNoticeFeedbackAttachment);

    /**
     * 批量删除工作通知反馈意见附件
     * 
     * @param ids 需要删除的工作通知反馈意见附件主键集合
     * @return 结果
     */
    public int deleteRdNoticeFeedbackAttachmentByIds(Long[] ids);

    /**
     * 删除工作通知反馈意见附件信息
     * 
     * @param id 工作通知反馈意见附件主键
     * @return 结果
     */
    public int deleteRdNoticeFeedbackAttachmentById(Long id);
}
