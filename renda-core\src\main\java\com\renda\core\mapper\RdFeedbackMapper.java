package com.renda.core.mapper;

import java.util.List;
import com.renda.core.domain.RdFeedback;
import com.renda.core.domain.vo.FeedbackInfoVO;

/**
 * 人大代反馈意见Mapper接口
 *
 * <AUTHOR>
 * @date 2023-11-04
 */
public interface RdFeedbackMapper
{
    /**
     * 查询人大代反馈意见
     *
     * @param id 人大代反馈意见主键
     * @return 人大代反馈意见
     */
    public RdFeedback selectRdFeedbackById(Long id);

    /**
     * 查询人大代反馈意见列表
     *
     * @param rdFeedback 人大代反馈意见
     * @return 人大代反馈意见集合
     */
    public List<RdFeedback> selectRdFeedbackList(RdFeedback rdFeedback);

    /**
     * 新增人大代反馈意见
     *
     * @param rdFeedback 人大代反馈意见
     * @return 结果
     */
    public int insertRdFeedback(RdFeedback rdFeedback);

    /**
     * 修改人大代反馈意见
     *
     * @param rdFeedback 人大代反馈意见
     * @return 结果
     */
    public int updateRdFeedback(RdFeedback rdFeedback);

    /**
     * 删除人大代反馈意见
     *
     * @param id 人大代反馈意见主键
     * @return 结果
     */
    public int deleteRdFeedbackById(Long id);

    /**
     * 批量删除人大代反馈意见
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRdFeedbackByIds(Long[] ids);

    /***
     * 获取建议回复列表
     * @param adviceId
     * @return
     */
    List<FeedbackInfoVO> selectFeedbackListWithAttachs(Long adviceId);

}
