package com.renda.core.mapper;

import java.util.List;
import com.renda.core.domain.RdJobAttachment;

/**
 * 履职工作附件Mapper接口
 *
 * <AUTHOR>
 * @date 2023-11-12
 */
public interface RdJobAttachmentMapper
{
    /**
     * 查询履职工作附件
     *
     * @param id 履职工作附件主键
     * @return 履职工作附件
     */
    public RdJobAttachment selectRdJobAttachmentById(Long id);

    /**
     * 查询履职工作附件列表
     *
     * @param rdJobAttachment 履职工作附件
     * @return 履职工作附件集合
     */
    public List<RdJobAttachment> selectRdJobAttachmentList(RdJobAttachment rdJobAttachment);

    /**
     * 新增履职工作附件
     *
     * @param rdJobAttachment 履职工作附件
     * @return 结果
     */
    public int insertRdJobAttachment(RdJobAttachment rdJobAttachment);

    /**
     * 修改履职工作附件
     *
     * @param rdJobAttachment 履职工作附件
     * @return 结果
     */
    public int updateRdJobAttachment(RdJobAttachment rdJobAttachment);

    /**
     * 删除履职工作附件
     *
     * @param id 履职工作附件主键
     * @return 结果
     */
    public int deleteRdJobAttachmentById(Long id);

    /**
     * 批量删除履职工作附件
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRdJobAttachmentByIds(Long[] ids);

    /**
     * 删除履职工作附件
     *
     * @param jobId 履职工作ID
     * @return 结果
     */
    void deleteJobAttachments(Long jobId);
}
