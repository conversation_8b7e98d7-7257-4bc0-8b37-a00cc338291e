package com.renda.core.service;

import java.util.List;
import com.renda.core.domain.RdStationPolicy;

/**
 * 联络站政策Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface IRdStationPolicyService 
{
    /**
     * 查询联络站政策
     * 
     * @param id 联络站政策主键
     * @return 联络站政策
     */
    public RdStationPolicy selectRdStationPolicyById(Long id);

    /**
     * 查询联络站政策列表
     * 
     * @param rdStationPolicy 联络站政策
     * @return 联络站政策集合
     */
    public List<RdStationPolicy> selectRdStationPolicyList(RdStationPolicy rdStationPolicy);

    /**
     * 新增联络站政策
     * 
     * @param rdStationPolicy 联络站政策
     * @return 结果
     */
    public int insertRdStationPolicy(RdStationPolicy rdStationPolicy);

    /**
     * 修改联络站政策
     * 
     * @param rdStationPolicy 联络站政策
     * @return 结果
     */
    public int updateRdStationPolicy(RdStationPolicy rdStationPolicy);

    /**
     * 批量删除联络站政策
     * 
     * @param ids 需要删除的联络站政策主键集合
     * @return 结果
     */
    public int deleteRdStationPolicyByIds(Long[] ids);

    /**
     * 删除联络站政策信息
     * 
     * @param id 联络站政策主键
     * @return 结果
     */
    public int deleteRdStationPolicyById(Long id);
}
