package com.renda.core.service.impl;

import java.util.List;

import com.renda.common.core.domain.model.LoginUser;
import com.renda.common.utils.SecurityUtils;
import com.renda.core.domain.RdDeputy;
import com.renda.core.domain.RdFeedback;
import com.renda.core.domain.vo.AdviceReportVO;
import com.renda.core.mapper.RdFeedbackMapper;
import com.renda.core.service.IRdDeputyService;
import com.renda.core.service.IRdFeedbackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.renda.core.mapper.RdAdviceReportMapper;
import com.renda.core.domain.RdAdviceReport;
import com.renda.core.service.IRdAdviceReportService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 建议上报Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@Service
public class RdAdviceReportServiceImpl implements IRdAdviceReportService
{
    @Autowired
    private RdAdviceReportMapper rdAdviceReportMapper;
    @Autowired
    private IRdFeedbackService feedbackService;
    @Autowired
    private IRdDeputyService deputyService;

    /**
     * 查询建议上报
     *
     * @param id 建议上报主键
     * @return 建议上报
     */
    @Override
    public RdAdviceReport selectRdAdviceReportById(Long id)
    {
        return rdAdviceReportMapper.selectRdAdviceReportById(id);
    }

    /**
     * 查询建议上报列表
     *
     * @param rdAdviceReport 建议上报
     * @return 建议上报
     */
    @Override
    public List<RdAdviceReport> selectRdAdviceReportList(RdAdviceReport rdAdviceReport)
    {
        return rdAdviceReportMapper.selectRdAdviceReportList(rdAdviceReport);
    }

    /**
     * 新增建议上报
     *
     * @param rdAdviceReport 建议上报
     * @return 结果
     */
    @Override
    public int insertRdAdviceReport(RdAdviceReport rdAdviceReport)
    {
        return rdAdviceReportMapper.insertRdAdviceReport(rdAdviceReport);
    }

    /**
     * 修改建议上报
     *
     * @param rdAdviceReport 建议上报
     * @return 结果
     */
    @Override
    public int updateRdAdviceReport(RdAdviceReport rdAdviceReport)
    {
        return rdAdviceReportMapper.updateRdAdviceReport(rdAdviceReport);
    }

    /**
     * 批量删除建议上报
     *
     * @param ids 需要删除的建议上报主键
     * @return 结果
     */
    @Override
    public int deleteRdAdviceReportByIds(Long[] ids)
    {
        return rdAdviceReportMapper.deleteRdAdviceReportByIds(ids);
    }

    /**
     * 删除建议上报信息
     *
     * @param id 建议上报主键
     * @return 结果
     */
    @Override
    public int deleteRdAdviceReportById(Long id)
    {
        return rdAdviceReportMapper.deleteRdAdviceReportById(id);
    }

    /***
     *
     * @param adviceReportVO 建议上报vo
     * @return
     */
    @Override
    @Transactional
    public int reportAdvice(AdviceReportVO adviceReportVO) {

        // 获取当前登录人大代表信息
        Long deputyId = SecurityUtils.getLoginUser().getUserId();
        RdDeputy deputy = deputyService.selectRdDeputyById(deputyId);

        for (long id : adviceReportVO.getIds()) {
            RdAdviceReport rdAdviceReport = new RdAdviceReport();
            rdAdviceReport.setAdviceId(adviceReportVO.getAdviceId());
            rdAdviceReport.setDeputyId(id);
            rdAdviceReport.setCreator(deputyId);
            insertRdAdviceReport(rdAdviceReport);
        }

        RdFeedback feedback = new RdFeedback();
        feedback.setAdviceId(adviceReportVO.getAdviceId());
        feedback.setFeedbackType(2);
        feedback.setUserType(2);
        feedback.setUserId(deputyId);
        feedback.setName(deputy.getName());
        feedback.setAvatar(deputy.getAvatar());
        feedback.setContent("已将本意见上报至上级人大机构，请继续等待答复，上报原因：【" + adviceReportVO.getReason() + "】");
        return feedbackService.insertRdFeedback(feedback);

    }

}
