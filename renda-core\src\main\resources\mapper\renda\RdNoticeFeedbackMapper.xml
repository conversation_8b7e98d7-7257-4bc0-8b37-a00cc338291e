<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renda.core.mapper.RdNoticeFeedbackMapper">

    <resultMap type="RdNoticeFeedback" id="RdNoticeFeedbackResult">
        <result property="id"    column="id"    />
        <result property="noticeId"    column="notice_id"    />
        <result property="deputyId"    column="deputy_id"    />
        <result property="content"    column="content"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <resultMap id="FeedbackWithAttachmentsResult" type="RdNoticeFeedback">
        <result property="id"    column="id"    />
        <result property="noticeId"    column="notice_id"    />
        <result property="deputyId"    column="deputy_id"    />
        <result property="content"    column="content"    />
        <result property="createTime"    column="create_time"    />
        <result property="deputyName"    column="deputyName"    />
        <result property="phone"    column="phone"    />
        <result property="avatar"    column="avatar"    />
        <collection property="attachments" javaType="List" ofType="com.renda.core.domain.RdNoticeFeedbackAttachment"
                    column="id" select="selectAttachmentsById"  />
    </resultMap>

    <resultMap type="RdNoticeFeedbackAttachment" id="RdNoticeFeedbackAttachmentResult">
        <result property="id"    column="id"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileUrl"    column="file_url"    />
    </resultMap>

    <sql id="selectRdNoticeFeedbackVo">
        select id, notice_id, deputy_id, content, create_time from rd_notice_feedback
    </sql>

    <select id="selectRdNoticeFeedbackList" parameterType="RdNoticeFeedback" resultMap="RdNoticeFeedbackResult">
        <include refid="selectRdNoticeFeedbackVo"/>
        <where>
            <if test="noticeId != null "> and notice_id = #{noticeId}</if>
            <if test="deputyId != null "> and deputy_id = #{deputyId}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
        </where>
    </select>

    <select id="selectRdNoticeFeedbackById" parameterType="Long" resultMap="RdNoticeFeedbackResult">
        <include refid="selectRdNoticeFeedbackVo"/>
        where id = #{id}
    </select>

    <select id="selectAttachmentsById" resultMap="RdNoticeFeedbackAttachmentResult">
        select id, file_type, file_name, file_url
        from rd_notice_feedback_attachment
        where feedback_id = #{id}
    </select>

    <select id="getNoticeFeedback" parameterType="SysNotice" resultMap="FeedbackWithAttachmentsResult">
        select f.id, f.notice_id, f.deputy_id, f.content, f.create_time,
               (select name from rd_deputy where id = f.deputy_id) as deputyName,
               (select phone from rd_deputy where id = f.deputy_id) as phone,
               (select avatar from rd_deputy where id = f.deputy_id) as avatar
        from rd_notice_feedback f
        where notice_id = #{noticeId}
        order by f.create_time
    </select>

    <insert id="insertRdNoticeFeedback" parameterType="RdNoticeFeedback" useGeneratedKeys="true" keyProperty="id">
        insert into rd_notice_feedback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="noticeId != null">notice_id,</if>
            <if test="deputyId != null">deputy_id,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="noticeId != null">#{noticeId},</if>
            <if test="deputyId != null">#{deputyId},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateRdNoticeFeedback" parameterType="RdNoticeFeedback">
        update rd_notice_feedback
        <trim prefix="SET" suffixOverrides=",">
            <if test="noticeId != null">notice_id = #{noticeId},</if>
            <if test="deputyId != null">deputy_id = #{deputyId},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRdNoticeFeedbackById" parameterType="Long">
        delete from rd_notice_feedback where id = #{id}
    </delete>

    <delete id="deleteRdNoticeFeedbackByIds" parameterType="String">
        delete from rd_notice_feedback where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
