import request from '@/utils/request'

// 查询工作通知反馈列表
export function listNoticefeedback(query) {
  return request({
    url: '/renda/noticefeedback/list',
    method: 'get',
    params: query
  })
}

// 查询工作通知反馈详细
export function getNoticefeedback(id) {
  return request({
    url: '/renda/noticefeedback/' + id,
    method: 'get'
  })
}

// 新增工作通知反馈
export function addNoticefeedback(data) {
  return request({
    url: '/renda/noticefeedback',
    method: 'post',
    data: data
  })
}

// 修改工作通知反馈
export function updateNoticefeedback(data) {
  return request({
    url: '/renda/noticefeedback',
    method: 'put',
    data: data
  })
}

// 删除工作通知反馈
export function delNoticefeedback(id) {
  return request({
    url: '/renda/noticefeedback/' + id,
    method: 'delete'
  })
}

// 获取工作通知反馈
export function getNoticeFeedback(data) {
  return request({
    url: '/renda/noticefeedback/getNoticeFeedback',
    method: 'post',
    data: data
  })
}
