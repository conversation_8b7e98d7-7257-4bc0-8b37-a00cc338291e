<template>
  <div class="app-container bg">
    <ScreenHeader ref="headerComp" />
    <div class="my-screen">
      <ScreenTitle caption="代表建议" />
      <div class="job-container">
        <div class="job-table">
          <div class="column">
            <el-table
              :data="jobList1"
              v-el-table-infinite-scroll="loadJobList"
              infinite-scroll-distance="5"
              height="100%"
              :header-cell-style="headerCellStyle"
              :cell-style="cellStyle"
              @row-click="onPreview"
            >
              <el-table-column prop="id" label="建议代表" align="center" width="240">
                <template slot-scope="scope">
                  <el-image class="img" :src="baseURL + scope.row.deputyList[0].deputyAvatar"></el-image>
                </template>
              </el-table-column>
              <el-table-column prop="recomType" label="建议类型" align="center" width="170">
                <template slot-scope="scope">
                  <dict-tag :options="dict.type.rd_recom_type" :value="scope.row.recomType"/>
                </template>
              </el-table-column>
              <el-table-column prop="session" label="届次" align="center" width="190">
                <template slot-scope="scope">
                  <div style="display: flex; flex-direction: row; justify-content: center;">
                    <dict-tag :options="dict.type.rd_session" :value="scope.row.session"/>
                    <dict-tag :options="dict.type.rd_times" :value="scope.row.times"/>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="title" label="建议标题">
                <template slot-scope="scope">
                  <div class="title">{{scope.row.title}}</div>
                  <div class="host">主办：{{scope.row.host}}</div>
                  <div class="cohost">协办：{{scope.row.cohost}}</div>
                </template>
              </el-table-column>
            </el-table>
            <el-alert v-if="isLoadingJobList" title="加载中..." type="success" center :closable="false" show-icon></el-alert>
            <el-alert v-if="noMoreJobList" title="没有更多了" type="warning" center show-icon></el-alert>
          </div>
          <div class="column">
            <el-table
              :data="jobList2"
              v-el-table-infinite-scroll="loadJobList"
              infinite-scroll-distance="5"
              height="100%"
              :header-cell-style="headerCellStyle"
              :cell-style="cellStyle"
              @row-click="onPreview"
            >
              <el-table-column prop="id" label="建议代表" align="center" width="240">
                <template slot-scope="scope">
                  <el-image class="img" :src="baseURL + scope.row.deputyList[0].deputyAvatar"></el-image>
                </template>
              </el-table-column>
              <el-table-column prop="recomType" label="建议类型" align="center" width="170">
                <template slot-scope="scope">
                  <dict-tag :options="dict.type.rd_recom_type" :value="scope.row.recomType"/>
                </template>
              </el-table-column>
              <el-table-column prop="session" label="届次" align="center" width="200">
                <template slot-scope="scope">
                  <div style="display: flex; flex-direction: row; justify-content: center;">
                    <dict-tag :options="dict.type.rd_session" :value="scope.row.session"/>
                    <dict-tag :options="dict.type.rd_times" :value="scope.row.times"/>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="title" label="建议标题">
                <template slot-scope="scope">
                  <div class="title">{{scope.row.title}}</div>
                  <div class="host">主办：{{scope.row.host}}</div>
                  <div class="cohost">协办：{{scope.row.cohost}}</div>
                </template>
              </el-table-column>
            </el-table>
            <el-alert v-if="isLoadingJobList" title="加载中..." type="success" center :closable="false" show-icon></el-alert>
            <el-alert v-if="noMoreJobList" title="没有更多了" type="warning" center show-icon></el-alert>
          </div>
        </div>
      </div>
    </div>
    <el-image class="return-btn" :src="require('@/assets/images/screen/return.png')" @click="onBack"></el-image>

    <!-- 预览代表建议对话框 -->
    <el-dialog :visible.sync="openPreview" modal width="1240px" height="1640px" top="300px!important">
      <div slot="title" style="font-family: AL-R; font-size: 34px; text-align: center;">{{recomTitle}}</div>
      <iframe
        v-if="previewUrl"
        :src="previewUrl"
        width="1200px"
        height="1500px"
        frameborder="0"
        scrolling="yes" />
    </el-dialog>

  </div>
</template>

<script>

import ScreenHeader from './components/screenHeader';
import ScreenTitle from './components/screenTitle';
import ElTableInfiniteScroll from 'el-table-infinite-scroll';
import { formatReadableDate } from '@/api/renda/utils';
import { listRecom } from '@/api/renda/screen';
import { Base64 } from 'js-base64'

export default {
  name: "RecomList",
  components: { ScreenHeader, ScreenTitle },
  dicts: ['yw_mz', 'rd_recom_type', 'rd_session', 'rd_times', 'job_type'],
  directives: {
    "el-table-infinite-scroll": ElTableInfiniteScroll,
  },
  data() {
    return {
      defaultAvatar: require("@/assets/images/avatar.png"), // 默认头像
      defaultDblz: require('@/assets/images/screen/lzgz.png'), // 默认代表履职图片
      baseURL: process.env.VUE_APP_BASE_API, // 图片上传地址头
      jobList1: [], // 履职信息列表
      jobList2: [], // 履职信息列表
      jobPage: 0, // 履职信息页码
      pageSize: 30, // 履职信息每页条数
      lastTimeLoadJobList: new Date().getTime() - 2000, // 上次加载时间
      isLoadingJobList: false,
      noMoreJobList: false,
      openPreview: false,
      recomTitle: '', // 预览文件标题
      previewUrl: '', // 预览文件地址
    };
  },
  activated() {
    this.jobList1 = [];
    this.jobList2 = [];
    this.jobPage = 0;
    this.jobType = null;
    this.loadJobList();
  },
  mounted() {
    this.$nextTick(() => {
      document.oncontextmenu = function() {
        return false;
      };
    });
  },
  methods: {
    loadJobList() {
      let nowTime = new Date().getTime()
      let diffTime = ( nowTime - this.lastTimeLoadJobList ) / 1000
      if (diffTime > 0.3) {
        this.isLoadingJobList = true;
        this.jobPage++;
        listRecom({
          pageNum: this.jobPage,
          pageSize: this.pageSize,
        }).then(res => {
          this.isLoadingJobList = false;
          if (this.jobPage > Math.floor(res.total / this.pageSize) + 1 ) {
            this.jobPage = Math.floor(res.total / this.pageSize) + 1;
            this.noMoreJobList = true;
            setTimeout(()=>{
              this.noMoreJobList = false
            },1000)
          } else {
            let count = Math.floor(res.rows.length / 2);
            if (res.rows.length % 2 === 1) {
              count++;
            }
            this.jobList1 = this.jobList1.concat(res.rows.slice(0, count));
            this.jobList2 = this.jobList2.concat(res.rows.slice(count));
          }
        });
      }
      this.lastTimeLoadJobList = nowTime
    },
    formatReadableDate(date) {
      return formatReadableDate(date);
    },
    onPreview(row) {
      let fileUrl = row.recomFileUrl;
      if (fileUrl) {
        let file = 'https://rd.juruifeng.cn:9000/prod-api' + fileUrl; //要预览文件的访问地址
        let url = 'https://rd.juruifeng.cn:8888/onlinePreview?url='+encodeURIComponent(Base64.encode(file));
        // let file = 'http://localhost:9000/dev-api' + fileUrl; //要预览文件的访问地址
        // let url = 'http://localhost:8888/onlinePreview?url='+encodeURIComponent(Base64.encode(file));
        this.previewUrl = url;
        this.recomTitle = row.title;
        this.openPreview = true;
      }
    },
    onBack() {
      this.$refs.headerComp.onClick();
    },
    selectJobType(jobType) {
      this.jobType = jobType;
      this.jobList1 = [];
      this.jobList2 = [];
      this.jobPage = 0;
      this.loadJobList();
    },
    allJobType() {
      this.selectJobType(null);
    },
    headerCellStyle() {
      return 'padding: 10px 20px; font-family: AL-R; font-size: 26px; font-weight: 600; line-height: 50px !important; color: #5a5e66; background-color: rgba(240, 64, 64, 0.2);';
    },
    cellStyle() {
      return 'padding: 10px 20px; font-family: AL-R; font-size: 26px; font-weight: 400; line-height: 50px !important; color: #5a5e66; background-color: #fff;';
    },
  }
};
</script>

<style lang="scss" scoped>

@import '../../../common/font/font.css';

.my-screen {
  margin: 250px 550px 100px 550px;
  padding: 50px 50px;
  height: 1800px;
  display: flex;
  flex-direction: column;

  .job-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 1590px;
    background: #FFF6EF;
    margin-top: 20px;
    padding: 50px;
    overflow: hidden;

    .job-table {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      .column {
        display: flex;
        flex-direction: column;
        width: 48%;
        padding: 30px;
        height: 1452px;
        .img {
          width: 180px;
          height: 240px;
          border: #ffffff solid 10px;
          box-shadow: 0px 0px 12px 0px rgba(0,0,0,0.1);
          flex-shrink: 0;
        }
      }

      .title {
        font-family: AL-B;
      }
      .host {
        margin-top: 20px;
        font-family: AL-L;
        font-size: 24px;
      }
      .cohost {
        font-family: AL-L;
        font-size: 24px;
      }
    }
  }
}

</style>
