package com.renda.core.mapper;

import java.util.List;
import com.renda.core.domain.RdActivity;

/**
 * 活动Mapper接口
 *
 * <AUTHOR>
 * @date 2023-11-13
 */
public interface RdActivityMapper
{
    /**
     * 查询活动
     *
     * @param id 活动主键
     * @return 活动
     */
    public RdActivity selectRdActivityById(Long id);

    /**
     * 查询活动列表
     *
     * @param rdActivity 活动
     * @return 活动集合
     */
    public List<RdActivity> selectRdActivityList(RdActivity rdActivity);

    /**
     * 新增活动
     *
     * @param rdActivity 活动
     * @return 结果
     */
    public int insertRdActivity(RdActivity rdActivity);

    /**
     * 修改活动
     *
     * @param rdActivity 活动
     * @return 结果
     */
    public int updateRdActivity(RdActivity rdActivity);

    /**
     * 删除活动
     *
     * @param id 活动主键
     * @return 结果
     */
    public int deleteRdActivityById(Long id);

    /**
     * 批量删除活动
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRdActivityByIds(Long[] ids);

    /***
     * 获取活动信息接口-前5条
     * @param activity 活动信息
     * @return 活动信息列表-前5条
     */
    List<RdActivity> getActivities(RdActivity activity);

    /***
     * 获取活动信息接口
     * @return 活动信息列表
     */
    List<RdActivity> selectActivityList(RdActivity activity);

    /***
     * 获取代表活动接口
     * @return
     */
    List<RdActivity> getDeputyActivityList(Long userId);

}
