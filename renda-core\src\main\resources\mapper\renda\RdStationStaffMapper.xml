<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renda.core.mapper.RdStationStaffMapper">

    <resultMap type="RdStationStaff" id="RdStationStaffResult">
        <result property="id"    column="id"    />
        <result property="stationId"    column="station_id"    />
        <result property="stationName"    column="station_name"    />
        <result property="name"    column="name"    />
        <result property="unit"    column="unit"    />
        <result property="position"    column="position"    />
        <result property="phone"    column="phone"    />
        <result property="avatar"    column="avatar"    />
        <result property="orderNum"    column="order_num"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectRdStationStaffVo">
        select s.id, s.station_id, st.station_name, s.name, s.unit, s.position, s.phone, s.avatar, s.order_num, s.status, s.create_by, s.create_time, s.update_by, s.update_time
        from rd_station_staff s
        left join rd_station st on s.station_id = st.station_id
    </sql>

    <select id="selectRdStationStaffList" parameterType="RdStationStaff" resultMap="RdStationStaffResult">
        <include refid="selectRdStationStaffVo"/>
        <where>
            <if test="stationId != null "> and s.station_id = #{stationId}</if>
            <if test="name != null  and name != ''"> and s.name like concat('%', #{name}, '%')</if>
            <if test="unit != null  and unit != ''"> and s.unit like concat('%', #{unit}, '%')</if>
            <if test="position != null  and position != ''"> and s.position like concat('%', #{position}, '%')</if>
            <if test="phone != null  and phone != ''"> and s.phone like concat('%', #{phone}, '%')</if>
            <if test="avatar != null  and avatar != ''"> and s.avatar = #{avatar}</if>
            <if test="orderNum != null "> and s.order_num = #{orderNum}</if>
            <if test="status != null  and status != ''"> and s.status = #{status}</if>
        </where>
        order by s.order_num asc, s.create_time desc
    </select>

    <select id="selectRdStationStaffById" parameterType="Long" resultMap="RdStationStaffResult">
        <include refid="selectRdStationStaffVo"/>
        where s.id = #{id}
    </select>

    <insert id="insertRdStationStaff" parameterType="RdStationStaff" useGeneratedKeys="true" keyProperty="id">
        insert into rd_station_staff
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stationId != null">station_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="unit != null and unit != ''">unit,</if>
            <if test="position != null and position != ''">position,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="avatar != null">avatar,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stationId != null">#{stationId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="unit != null and unit != ''">#{unit},</if>
            <if test="position != null and position != ''">#{position},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRdStationStaff" parameterType="RdStationStaff">
        update rd_station_staff
        <trim prefix="SET" suffixOverrides=",">
            <if test="stationId != null">station_id = #{stationId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="unit != null and unit != ''">unit = #{unit},</if>
            <if test="position != null and position != ''">position = #{position},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRdStationStaffById" parameterType="Long">
        delete from rd_station_staff where id = #{id}
    </delete>

    <delete id="deleteRdStationStaffByIds" parameterType="String">
        delete from rd_station_staff where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
