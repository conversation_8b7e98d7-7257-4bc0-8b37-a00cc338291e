<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renda.core.mapper.RdStationNewsMapper">

    <resultMap type="RdStationNews" id="RdStationNewsResult">
        <result property="id"    column="id"    />
        <result property="stationId"    column="station_id"    />
        <result property="stationName"    column="station_name"    />
        <result property="title"    column="title"    />
        <result property="summary"    column="summary"    />
        <result property="content"    column="content"    />
        <result property="type"    column="type"    />
        <result property="coverUrl"    column="cover_url"    />
        <result property="publisher"    column="publisher"    />
        <result property="publishTime"    column="publish_time"    />
        <result property="isTop"    column="is_top"    />
        <result property="status"    column="status"    />
        <result property="viewCount"    column="view_count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectRdStationNewsVo">
        select n.id, n.station_id, s.station_name, n.title, n.summary, n.content, n.type, n.cover_url, n.publisher, n.publish_time, n.is_top, n.status, n.view_count, n.create_by, n.create_time, n.update_by, n.update_time
        from rd_station_news n
        left join rd_station s on n.station_id = s.station_id
    </sql>

    <select id="selectRdStationNewsList" parameterType="RdStationNews" resultMap="RdStationNewsResult">
        <include refid="selectRdStationNewsVo"/>
        <where>
            <if test="stationId != null "> and n.station_id = #{stationId}</if>
            <if test="title != null  and title != ''"> and n.title like concat('%', #{title}, '%')</if>
            <if test="summary != null  and summary != ''"> and n.summary like concat('%', #{summary}, '%')</if>
            <if test="content != null  and content != ''"> and n.content like concat('%', #{content}, '%')</if>
            <if test="type != null "> and n.type = #{type}</if>
            <if test="coverUrl != null  and coverUrl != ''"> and n.cover_url = #{coverUrl}</if>
            <if test="publisher != null  and publisher != ''"> and n.publisher like concat('%', #{publisher}, '%')</if>
            <if test="publishTime != null "> and date_format(n.publish_time,'%y-%m-%d') = date_format(#{publishTime},'%y-%m-%d')</if>
            <if test="isTop != null  and isTop != ''"> and n.is_top = #{isTop}</if>
            <if test="status != null "> and n.status = #{status}</if>
            <if test="viewCount != null "> and n.view_count = #{viewCount}</if>
        </where>
        order by n.publish_time desc
    </select>

    <select id="selectRdStationNewsById" parameterType="Long" resultMap="RdStationNewsResult">
        <include refid="selectRdStationNewsVo"/>
        where n.id = #{id}
    </select>

    <insert id="insertRdStationNews" parameterType="RdStationNews">
        insert into rd_station_news
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="stationId != null">station_id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="summary != null and summary != ''">summary,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="type != null">type,</if>
            <if test="coverUrl != null">cover_url,</if>
            <if test="publisher != null and publisher != ''">publisher,</if>
            <if test="publishTime != null">publish_time,</if>
            <if test="isTop != null and isTop != ''">is_top,</if>
            <if test="status != null">status,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="stationId != null">#{stationId},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="summary != null and summary != ''">#{summary},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="type != null">#{type},</if>
            <if test="coverUrl != null">#{coverUrl},</if>
            <if test="publisher != null and publisher != ''">#{publisher},</if>
            <if test="publishTime != null">#{publishTime},</if>
            <if test="isTop != null and isTop != ''">#{isTop},</if>
            <if test="status != null">#{status},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRdStationNews" parameterType="RdStationNews">
        update rd_station_news
        <trim prefix="SET" suffixOverrides=",">
            <if test="stationId != null">station_id = #{stationId},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="summary != null and summary != ''">summary = #{summary},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="type != null">type = #{type},</if>
            <if test="coverUrl != null">cover_url = #{coverUrl},</if>
            <if test="publisher != null and publisher != ''">publisher = #{publisher},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            <if test="isTop != null and isTop != ''">is_top = #{isTop},</if>
            <if test="status != null">status = #{status},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRdStationNewsById" parameterType="Long">
        delete from rd_station_news where id = #{id}
    </delete>

    <delete id="deleteRdStationNewsByIds" parameterType="String">
        delete from rd_station_news where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
