package com.renda.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.renda.common.annotation.Log;
import com.renda.common.core.controller.BaseController;
import com.renda.common.core.domain.AjaxResult;
import com.renda.common.enums.BusinessType;
import com.renda.core.domain.RdFeedback;
import com.renda.core.service.IRdFeedbackService;
import com.renda.common.utils.poi.ExcelUtil;
import com.renda.common.core.page.TableDataInfo;

/**
 * 人大代反馈意见Controller
 *
 * <AUTHOR>
 * @date 2023-11-04
 */
@RestController
@RequestMapping("/renda/feedback")
public class RdFeedbackController extends BaseController
{
    @Autowired
    private IRdFeedbackService rdFeedbackService;

    /**
     * 查询人大代反馈意见列表
     */
    @PreAuthorize("@ss.hasPermi('renda:feedback:list')")
    @GetMapping("/list")
    public TableDataInfo list(RdFeedback rdFeedback)
    {
        startPage();
        List<RdFeedback> list = rdFeedbackService.selectRdFeedbackList(rdFeedback);
        return getDataTable(list);
    }

    /**
     * 导出人大代反馈意见列表
     */
    @PreAuthorize("@ss.hasPermi('renda:feedback:export')")
    @Log(title = "人大代反馈意见", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RdFeedback rdFeedback)
    {
        List<RdFeedback> list = rdFeedbackService.selectRdFeedbackList(rdFeedback);
        ExcelUtil<RdFeedback> util = new ExcelUtil<RdFeedback>(RdFeedback.class);
        util.exportExcel(response, list, "人大代反馈意见数据");
    }

    /**
     * 获取人大代反馈意见详细信息
     */
    @PreAuthorize("@ss.hasPermi('renda:advice:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(rdFeedbackService.selectRdFeedbackById(id));
    }

    /**
     * 新增人大代反馈意见
     */
    @PreAuthorize("@ss.hasPermi('renda:advice:add')")
    @Log(title = "人大代反馈意见", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RdFeedback rdFeedback)
    {
        return toAjax(rdFeedbackService.insertRdFeedback(rdFeedback));
    }

    /**
     * 修改人大代反馈意见
     */
    @PreAuthorize("@ss.hasPermi('renda:advice:edit')")
    @Log(title = "人大代反馈意见", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RdFeedback rdFeedback)
    {
        return toAjax(rdFeedbackService.updateRdFeedback(rdFeedback));
    }

    /**
     * 删除人大代反馈意见
     */
    @PreAuthorize("@ss.hasPermi('renda:feedback:remove')")
    @Log(title = "人大代反馈意见", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rdFeedbackService.deleteRdFeedbackByIds(ids));
    }
}
