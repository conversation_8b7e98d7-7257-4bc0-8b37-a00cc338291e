package com.renda.core.service.impl;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.renda.common.utils.DateUtils;
import com.renda.common.utils.SecurityUtils;
import com.renda.core.domain.RdActivityRegistration;
import com.renda.core.domain.RdDeputy;
import com.renda.core.service.IRdActivityRegistrationService;
import com.renda.core.service.IRdDeputyService;
import com.renda.core.service.SendSmsThread;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.renda.core.mapper.RdActivityMapper;
import com.renda.core.domain.RdActivity;
import com.renda.core.service.IRdActivityService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * 活动Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-13
 */
@Service
public class RdActivityServiceImpl implements IRdActivityService
{
    @Autowired
    private RdActivityMapper rdActivityMapper;

    @Autowired
    private IRdActivityRegistrationService activityRegistrationService;

    @Autowired
    private IRdDeputyService deputyService;

    @Value("${sms.feige.url}")
    private String url;
    @Value("${sms.feige.apikey}")
    private String apikey;
    @Value("${sms.feige.secret}")
    private String secret;
    @Value("${sms.feige.signId}")
    private String signId;
    @Value("${sms.feige.templateId}")
    private String templateId;

    /**
     * 查询活动
     *
     * @param id 活动主键
     * @return 活动
     */
    @Override
    public RdActivity selectRdActivityById(Long id)
    {
        return rdActivityMapper.selectRdActivityById(id);
    }

    /**
     * 查询活动列表
     *
     * @param rdActivity 活动
     * @return 活动
     */
    @Override
    public List<RdActivity> selectRdActivityList(RdActivity rdActivity)
    {
        return rdActivityMapper.selectRdActivityList(rdActivity);
    }

    /**
     * 新增活动
     *
     * @param rdActivity 活动
     * @return 结果
     */
    @Override
    @Transactional
    public int insertRdActivity(RdActivity rdActivity)
    {
        rdActivity.setCreateBy(SecurityUtils.getLoginUser().getUsername());
        rdActivity.setCreateTime(DateUtils.getNowDate());
        rdActivity.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        rdActivity.setUpdateTime(DateUtils.getNowDate());
        rdActivityMapper.insertRdActivity(rdActivity);

        // 新增活动报名信息
        rdActivity.getRegistrationList().forEach(reg -> {

            RdDeputy deputy = deputyService.selectRdDeputyById(reg.getDeputyId());
            if (deputy == null) {
                throw new RuntimeException("无此代表信息");
            }
            RdActivityRegistration registration = new RdActivityRegistration();
            registration.setActivityId(rdActivity.getId());
            registration.setDeputyId(reg.getDeputyId());
            registration.setRegistrationType("0"); // 默认未报名状态
            registration.setPhone(deputy.getPhone());
            registration.setSmsSendCount(0); // 发送0次
            registration.setSmsSendStatus(-1); // 状态-1
            activityRegistrationService.insertRdActivityRegistration(registration);
        });

        return 1;
    }

    /**
     * 修改活动
     *
     * @param rdActivity 活动
     * @return 结果
     */
    @Override
    @Transactional
    public int updateRdActivity(RdActivity rdActivity)
    {
        RdActivity activity = rdActivityMapper.selectRdActivityById(rdActivity.getId());
        if (activity == null) {
            throw new RuntimeException("无此活动信息");
        }
        if (!ObjectUtils.isEmpty(rdActivity.getStatus()) &&
                activity.getStatus().equals("1") &&
                rdActivity.getStatus().equals("1")) {
            throw new RuntimeException("活动已开启，不可修改");
        }
        // 更新活动信息
        rdActivity.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        rdActivity.setUpdateTime(DateUtils.getNowDate());
        rdActivityMapper.updateRdActivity(rdActivity);

        // 更新活动报名信息
        if (rdActivity.getRegistrationList() != null) {
            // 删除活动报名信息（已报名或已请假的报名信息不删除）
            activityRegistrationService.deleteRdActivityRegistrationByActivityId(rdActivity.getId(), "0");

            // 重新插入活动报名信息，已报名或已请假的报名信息不插入
            rdActivity.getRegistrationList().forEach(reg -> {
                RdActivityRegistration registration = activityRegistrationService.selectRdActivityRegistrationByActivityIdAndDeputyId(
                        rdActivity.getId(), reg.getDeputyId());
                if (registration == null) {
                    RdDeputy deputy = deputyService.selectRdDeputyById(reg.getDeputyId());
                    if (deputy == null) {
                        throw new RuntimeException("无此代表信息");
                    }
                    registration = new RdActivityRegistration();
                    registration.setActivityId(rdActivity.getId());
                    registration.setDeputyId(reg.getDeputyId());
                    registration.setRegistrationType("0"); // 默认未报名状态
                    registration.setPhone(deputy.getPhone());
                    registration.setSmsSendCount(0); // 发送0次
                    registration.setSmsSendStatus(-1); // 状态-1
                    activityRegistrationService.insertRdActivityRegistration(registration);
                }
            });
        }

        return 1;
    }

    /**
     * 批量删除活动
     *
     * @param ids 需要删除的活动主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteRdActivityByIds(Long[] ids)
    {
        // 循环ids，删除每个活动的报名信息
        for (Long id : ids) {
            activityRegistrationService.deleteRdActivityRegistrationByActivityId(id, null);
        }
        rdActivityMapper.deleteRdActivityByIds(ids);
        return 1;
    }

    /**
     * 删除活动信息
     *
     * @param id 活动主键
     * @return 结果
     */
    @Override
    public int deleteRdActivityById(Long id)
    {
        return rdActivityMapper.deleteRdActivityById(id);
    }

    /***
     * 获取活动信息接口-前5条
     * @param activity 活动信息
     * @return 活动信息列表-前5条
     */
    @Override
    public List<RdActivity> getActivities(RdActivity activity) {
        return rdActivityMapper.getActivities(activity);
    }

    /***
     * 获取活动信息接口
     * @return 活动信息列表
     */
    @Override
    public List<RdActivity> selectActivityList(RdActivity activity) {
        return rdActivityMapper.selectActivityList(activity);
    }

    /***
     * 活动报名请假接口
     * @param activityRegistration 活动报名请假信息
     * @return
     */
    @Override
    public void registerActivity(RdActivityRegistration activityRegistration) {
        // 获取当前登录人大代表信息
        Long userId = SecurityUtils.getLoginUser().getUserId();
        RdDeputy deputy = deputyService.selectRdDeputyById(userId);
        if (deputy == null) {
            throw new RuntimeException("无此代表信息");
        }

        // 判断是否已报名
        RdActivityRegistration registration = activityRegistrationService.selectRdActivityRegistrationByActivityIdAndDeputyId(
                activityRegistration.getActivityId(), userId);
        if (registration != null) {
            registration.setRegistrationType(activityRegistration.getRegistrationType());
            registration.setCreateTime(DateUtils.getNowDate());
            activityRegistrationService.updateRdActivityRegistration(registration);
        } else {
            RdActivity activity = rdActivityMapper.selectRdActivityById(activityRegistration.getActivityId());
            if (activity == null) {
                throw new RuntimeException("无此活动信息");
            }
            if (activity.getRegistrationMode().equals("1")) {
                throw new RuntimeException("不允许自主报名");
            }
            activityRegistration.setDeputyId(userId);
            activityRegistration.setPhone(deputy.getPhone());
            activityRegistration.setSmsSendCount(0);
            activityRegistration.setSmsSendStatus(-1);
            activityRegistration.setCreateTime(DateUtils.getNowDate());
            activityRegistrationService.insertRdActivityRegistration(activityRegistration);
        }
    }

    /***
     * 获取代表活动接口
     * @return
     */
    @Override
    public List<RdActivity> getDeputyActivityList(Long userId) {
        return rdActivityMapper.getDeputyActivityList(userId);
    }

    /**
     * 单发短信
     */
    @Override
    public int sendSms(RdActivityRegistration rdActivityRegistration) {

        // 获取报名信息
        RdActivityRegistration registration = activityRegistrationService.selectRdActivityRegistrationById(rdActivityRegistration.getId());
        if (registration == null) {
            throw new RuntimeException("无此报名信息");
        }
        if (!"0".equals(registration.getRegistrationType())) {
            throw new RuntimeException("该人大代表已报名或请假，无需再发短信通知");
        }

        try {
            // 初始化短信平台配置
            HttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(url);
            httpPost.addHeader("Content-Type","application/json");
            Map<String,Object> map = new HashMap<>();
            map.put("apikey", apikey);
            map.put("secret", secret);
            map.put("sign_id", signId);
            map.put("template_id", templateId);
            map.put("content", "");
            map.put("mobile",rdActivityRegistration.getPhone());
            String json = JSON.toJSONString(map);
            httpPost.setEntity(new StringEntity(json,"UTF-8"));
            HttpResponse response = null;
            try {
                response = httpClient.execute(httpPost);
            } catch (IOException e) {
                e.printStackTrace();
                throw new RuntimeException("短信平台连接异常");
            }
            HttpEntity entity = response.getEntity();
            String ret_json = null;
            try {
                ret_json = EntityUtils.toString(entity);
            } catch (IOException e) {
                e.printStackTrace();
                throw new RuntimeException("短信平台返回数据异常");
            }
            JSONObject result = JSON.parseObject(ret_json);

            // 更新数据库发送状态
            registration.setSmsSendCount(registration.getSmsSendCount() + 1);
            registration.setSmsSendStatus(result.getInteger("code"));
            registration.setSmsMsgNo(result.getString("msg_no"));
            registration.setSmsMsg(result.getString("msg"));
            registration.setSmsSendTime(DateUtils.getNowDate());
            activityRegistrationService.updateRdActivityRegistration(registration);

            return 1;

        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("发送短信异常");
        }
    }

    /**
     * 群发短信
     */
    @Override
    public int batchSendSms(RdActivity rdActivity) {
        RdActivity activity = rdActivityMapper.selectRdActivityById(rdActivity.getId());
        if (activity == null) {
            throw new RuntimeException("无此活动信息");
        }

        // 发送短信
        SendSmsThread sendSmsThread = new SendSmsThread(url, apikey, secret, signId, templateId,
                activityRegistrationService, activity);
        sendSmsThread.start();

        return 1;
    }

}
