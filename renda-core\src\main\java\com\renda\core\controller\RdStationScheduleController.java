package com.renda.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.renda.common.annotation.Log;
import com.renda.common.core.controller.BaseController;
import com.renda.common.core.domain.AjaxResult;
import com.renda.common.enums.BusinessType;
import com.renda.core.domain.RdStationSchedule;
import com.renda.core.service.IRdStationScheduleService;
import com.renda.common.utils.poi.ExcelUtil;
import com.renda.common.core.page.TableDataInfo;

/**
 * 排班表管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@RestController
@RequestMapping("/renda/stationschedule")
public class RdStationScheduleController extends BaseController
{
    @Autowired
    private IRdStationScheduleService rdStationScheduleService;

    /**
     * 查询排班表管理列表
     */
    @PreAuthorize("@ss.hasPermi('renda:stationschedule:list')")
    @GetMapping("/list")
    public TableDataInfo list(RdStationSchedule rdStationSchedule)
    {
        startPage();
        List<RdStationSchedule> list = rdStationScheduleService.selectRdStationScheduleList(rdStationSchedule);
        return getDataTable(list);
    }

    /**
     * 导出排班表管理列表
     */
    @PreAuthorize("@ss.hasPermi('renda:stationschedule:export')")
    @Log(title = "排班表管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RdStationSchedule rdStationSchedule)
    {
        List<RdStationSchedule> list = rdStationScheduleService.selectRdStationScheduleList(rdStationSchedule);
        ExcelUtil<RdStationSchedule> util = new ExcelUtil<RdStationSchedule>(RdStationSchedule.class);
        util.exportExcel(response, list, "排班表管理数据");
    }

    /**
     * 获取排班表管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('renda:stationschedule:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rdStationScheduleService.selectRdStationScheduleById(id));
    }

    /**
     * 检查指定联络站和日期是否已存在排班记录
     */
    @PreAuthorize("@ss.hasPermi('renda:stationschedule:query')")
    @GetMapping("/checkExists")
    public AjaxResult checkExists(RdStationSchedule rdStationSchedule)
    {
        boolean exists = rdStationScheduleService.checkStationScheduleExists(
            rdStationSchedule.getStationId(), 
            rdStationSchedule.getScheduleDate(), 
            rdStationSchedule.getId()
        );
        return success(exists);
    }

    /**
     * 新增排班表管理
     */
    @PreAuthorize("@ss.hasPermi('renda:stationschedule:add')")
    @Log(title = "排班表管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RdStationSchedule rdStationSchedule)
    {
        return toAjax(rdStationScheduleService.insertRdStationSchedule(rdStationSchedule));
    }

    /**
     * 修改排班表管理
     */
    @PreAuthorize("@ss.hasPermi('renda:stationschedule:edit')")
    @Log(title = "排班表管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RdStationSchedule rdStationSchedule)
    {
        return toAjax(rdStationScheduleService.updateRdStationSchedule(rdStationSchedule));
    }

    /**
     * 删除排班表管理
     */
    @PreAuthorize("@ss.hasPermi('renda:stationschedule:remove')")
    @Log(title = "排班表管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rdStationScheduleService.deleteRdStationScheduleByIds(ids));
    }
}
