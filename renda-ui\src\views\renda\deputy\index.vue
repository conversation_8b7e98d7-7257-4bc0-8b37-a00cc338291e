<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="4" :xs="24" v-if="showDeptTree">
        <div class="head-container">
          <el-input
            v-model="deptName"
            placeholder="请输入部门名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="deptOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            node-key="id"
            default-expand-all
            highlight-current
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <!--用户数据-->
      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="代表类型" prop="name">
            <el-select v-model="queryParams.type" placeholder="请选择" clearable filterable>
              <el-option
                v-for="dict in dict.type.rd_deputy_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="代表团" prop="name">
            <el-select v-model="queryParams.groupId" placeholder="请选择" clearable filterable>
              <el-option
                v-for="dict in dict.type.rd_deputy_group"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="所属站点" prop="stationId">
            <treeselect 
              v-model="queryParams.stationId" 
              :options="stationOptions" 
              :show-count="true" 
              placeholder="请选择所属站点"
              :searchable="true"
              :clearable="true"
              style="width: 300px;"
            />
          </el-form-item>
          <el-form-item label="姓名" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入姓名"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="民族" prop="nation">
            <el-select v-model="queryParams.nation" placeholder="请选择" clearable filterable>
              <el-option
                v-for="dict in dict.type.yw_mz"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="职务" prop="duty">
            <el-input
              v-model="queryParams.duty"
              placeholder="请输入职务"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="电话" prop="phone">
            <el-input
              v-model="queryParams.phone"
              placeholder="请输入电话"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="站内职务" prop="stationDuty">
            <el-select v-model="queryParams.stationDuty" placeholder="请选择" clearable filterable>
              <el-option
                v-for="dict in dict.type.rd_station_duty"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="是否进站代表" prop="isStationDeputy">
            <el-select v-model="queryParams.isStationDeputy" placeholder="请选择" clearable>
              <el-option label="是" value="1" />
              <el-option label="否" value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['renda:deputy:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['renda:deputy:edit']"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['renda:deputy:remove']"
            >删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['renda:deputy:export']"
            >导出</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleQrcode"
              v-hasPermi="['renda:deputy:qrcode']"
            >生成二维码</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="deputyList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="代表团" align="center" prop="groupId" width="150">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.rd_deputy_group" :value="scope.row.groupId"/>
            </template>
          </el-table-column>
          <el-table-column label="所属站点" align="center" prop="stationName" width="110" />
<!--          <el-table-column label="所属部门" align="center" prop="deptName" width="110" />-->
          <el-table-column label="代表类型" align="center" prop="type" width="80">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.rd_deputy_type" :value="scope.row.type"/>
            </template>
          </el-table-column>
          <el-table-column prop="openid" label="注册状态" align="center" width="80">
            <template slot-scope="scope">
              <div v-if="scope.row.openid !== null" class="success-icon"><i class="el-icon-success" /></div>
              <div v-if="scope.row.openid === null" class="error-icon"><i class="el-icon-error" /></div>
            </template>
          </el-table-column>
          <el-table-column label="头像" align="center" prop="avatar" width="80">
            <template slot-scope="scope">
              <el-image :src="scope.row.avatar ? baseURL + scope.row.avatar : defaultAvatar" fit="contain" />
            </template>
          </el-table-column>
          <el-table-column label="二维码" align="center" prop="avatar" width="80">
            <template slot-scope="scope">
              <el-image :src="baseURL + scope.row.qrcodeUrl" fit="contain" />
            </template>
          </el-table-column>
          <el-table-column label="姓名" align="center" prop="name" />
          <el-table-column label="职务" align="center" prop="duty" width="200px" />
          <el-table-column label="电话" align="center" prop="phone" width="110px" />
          <el-table-column label="民族" align="center" prop="nation" width="80">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.yw_mz" :value="scope.row.nation"/>
            </template>
          </el-table-column>
          <el-table-column label="站内职务" align="center" prop="stationDuty" width="120">
            <template slot-scope="scope">
              <dict-tag 
                v-if="scope.row.stationDuty" 
                :options="dict.type.rd_station_duty" 
                :value="scope.row.stationDuty"
              />
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column label="代表标签" align="center" prop="tags" width="150">
            <template slot-scope="scope">
              <template v-if="scope.row.tags && scope.row.tags.trim() !== ''">
                <dict-tag 
                  v-for="tag in scope.row.tags.split(',').filter(t => t.trim() !== '')" 
                  :key="tag" 
                  :options="dict.type.rd_deputy_tags" 
                  :value="tag.trim()"
                  style="margin-right: 5px;"
                />
              </template>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column label="是否进站代表" align="center" prop="isStationDeputy" width="120">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.isStationDeputy"
                active-value="1"
                inactive-value="0"
                @change="handleStationDeputyChange(scope.row)"
                v-hasPermi="['renda:deputy:edit']"
              />
            </template>
          </el-table-column>
          <el-table-column label="出生日期" align="center" prop="birthday" width="100">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.birthday, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['renda:deputy:edit']"
              >修改</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['renda:deputy:remove']"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改人大代表管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="635px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row align="middle" justify="center">
          <el-col :span="12">
<!--            <el-row>-->
<!--              <el-form-item label="归属部门" prop="deptId">-->
<!--                <treeselect v-model="form.deptId" :options="deptOptions" :show-count="true" placeholder="请选择归属部门" />-->
<!--              </el-form-item>-->
<!--            </el-row>-->
            <el-row>
              <el-form-item label="姓名" prop="name">
                <el-input v-model="form.name" placeholder="请输入姓名" />
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item label="手机" prop="phone">
                <el-input v-model="form.phone" placeholder="请输入手机，绑定小程序账号" maxlength="11" />
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item label="代表类型" prop="type">
                <el-select v-model="form.type" placeholder="请选择">
                  <el-option
                    v-for="dict in dict.type.rd_deputy_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-row>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="avatar">
              <div>
                <div class="user-info-head" @click="editCropper()">
<!--                  <img :src="form.avatar ? transURL(form.avatar) : defaultAvatar" title="点击上传头像" class="img-circle img-lg" />-->
                  <el-image class="avatar" :src="form.avatar ? transURL(form.avatar) : defaultAvatar" fit="cover"></el-image>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="所属站点" prop="stationId">
              <treeselect v-model="form.stationId" :options="stationOptions" :show-count="true" placeholder="请选择所属站点" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="代表团" prop="groupId">
              <el-select v-model="form.groupId" placeholder="请选择" clearable filterable>
                <el-option
                  v-for="dict in dict.type.rd_deputy_group"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="单位" prop="company">
              <el-input v-model="form.company" placeholder="请输入所在单位" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="职务" prop="duty">
              <el-input v-model="form.duty" placeholder="请输入职务" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示顺序" prop="orderNum">
              <el-input v-model="form.orderNum" placeholder="请输入显示顺序" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="站内职务" prop="stationDuty">
              <el-select 
                v-model="form.stationDuty" 
                placeholder="请选择" 
                clearable 
                filterable
                @change="handleStationDutyChange"
              >
                <el-option
                  v-for="dict in dict.type.rd_station_duty"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="代表标签" prop="tags">
              <el-select 
                v-model="form.tags" 
                placeholder="请选择" 
                clearable 
                filterable 
                multiple
                @change="handleTagsChange"
              >
                <el-option
                  v-for="dict in dict.type.rd_deputy_tags"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否进站代表" prop="isStationDeputy">
              <el-switch
                v-model="form.isStationDeputy"
                active-value="1"
                inactive-value="0"
                active-text="是"
                inactive-text="否"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="代表排序" prop="deputyOrderNum">
              <el-input-number 
                v-model="form.deputyOrderNum" 
                :min="0" 
                :max="999" 
                placeholder="请输入排序序号"
                controls-position="right"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="民族" prop="nation">
              <el-select v-model="form.nation" placeholder="请选择" clearable filterable>
                <el-option
                  v-for="dict in dict.type.yw_mz"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出生日期" prop="birthday">
              <el-date-picker clearable
                              v-model="form.birthday"
                              type="date"
                              value-format="yyyy-MM-dd"
                              placeholder="请选择出生日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="代表履历" prop="resume">
            <el-input v-model="form.resume" type="textarea" placeholder="请输入内容" :rows="3" />
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="title" :visible.sync="avatarSelectorOpen" width="800px" append-to-body @opened="modalOpened"  @close="closeDialog">
      <el-row>
        <el-col :xs="24" :md="12" :style="{height: '350px'}">
          <vue-cropper
            ref="cropper"
            :img="options.img"
            :output-size="options.size"
            :output-type="options.outputType"
            :info="true"
            :full="options.full"
            :fixed="options.fixed"
            :fixedNumber="options.fixedNumber"
            :can-move="options.canMove"
            :can-move-box="options.canMoveBox"
            :fixed-box="options.fixedBox"
            :original="options.original"
            :auto-crop="options.autoCrop"
            :auto-crop-width="options.autoCropWidth"
            :auto-crop-height="options.autoCropHeight"
            :center-box="options.centerBox"
            :high="options.high"
            mode="contain"
            :max-img-size="options.max"
            @realTime="realTime"
            v-if="visible"
          />
        </el-col>
        <el-col :xs="24" :md="12" :style="{height: '350px'}">
          <div style="width: 350px; height: 350px">
            <div class="show-preview" :style="{'width': previews.w + 'px', 'height': previews.h + 'px', 'overflow': 'hidden'}">
              <div :style="previews.div">
                <img :src="previews.url" :style="previews.img">
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <br />
      <el-row>
        <el-col :lg="2" :sm="3" :xs="3">
          <el-upload action="#" :http-request="requestUpload" :show-file-list="false" :before-upload="beforeUpload">
            <el-button size="small">
              选择
              <i class="el-icon-upload el-icon--right"></i>
            </el-button>
          </el-upload>
        </el-col>
        <el-col :lg="{span: 1, offset: 2}" :sm="2" :xs="2">
          <el-button icon="el-icon-plus" size="small" @click="changeScale(1)"></el-button>
        </el-col>
        <el-col :lg="{span: 1, offset: 1}" :sm="2" :xs="2">
          <el-button icon="el-icon-minus" size="small" @click="changeScale(-1)"></el-button>
        </el-col>
        <el-col :lg="{span: 1, offset: 1}" :sm="2" :xs="2">
          <el-button icon="el-icon-refresh-left" size="small" @click="rotateLeft()"></el-button>
        </el-col>
        <el-col :lg="{span: 1, offset: 1}" :sm="2" :xs="2">
          <el-button icon="el-icon-refresh-right" size="small" @click="rotateRight()"></el-button>
        </el-col>
        <el-col :lg="{span: 6, offset: 5}" :sm="2" :xs="2">
          <el-button type="primary" size="small" @click="confirmAvatar()">确 定</el-button>
          <el-button type="primary" size="small" @click="cancelAvatar()">取 消</el-button>
        </el-col>
      </el-row>
    </el-dialog>

  </div>
</template>

<script>

import { listDeputy, getDeputy, delDeputy, addDeputy, updateDeputy, genDeputyQrcode } from '@/api/renda/deputy'
import { debounce } from '@/utils'
import { VueCropper } from 'vue-cropper'
import { deptTreeSelect } from '@/api/system/user'
import Treeselect from '@riophae/vue-treeselect'
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { stationTreeSelect } from '@/api/renda/station'

export default {
  name: "Deputy",
  components: { VueCropper, Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 人大代表管理表格数据
      deputyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        type: null,
        groupId: null,
        stationId: null,
        name: null,
        nation: null,
        company: null,
        tel: null,
        duty: null,
        phone: null,
        stationDuty: null,
        deptId: null,
        isStationDeputy: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        deptId: [
          { required: true, message: "必须选择人大代表归属部门", trigger: "change" }
        ],
        stationId: [
          { required: true, message: "必须选择人大代表所属站点", trigger: "change" }
        ],
        type: [
          { required: true, message: "必须选择人大代表类型", trigger: "change" }
        ],
        groupId: [
          { required: true, message: "必须选择代表团", trigger: "change" }
        ],
        name: [
          { required: true, message: "姓名不能为空", trigger: "blur" }
        ],
        phone: [
          { required: true, message: "电话不能为空", trigger: "blur" }
        ],
      },
      defaultAvatar: require("@/assets/images/avatar.png"), // 默认头像
      baseURL: process.env.VUE_APP_BASE_API, // 图片上传地址头
      avatarSelectorOpen: false, // 是否显示弹出层
      avatarChanged: false, // 头像是否改变
      visible: false, // 是否显示cropper
      options: {
        img: '', //裁剪图片的地址
        size: 1, //导出图片的质量
        outputType:"png", // 默认生成截图为PNG格式
        full: false, // 是否输出原图比例的截图
        fixed: true, // 固定截图框比例 不允许改变
        fixedNumber: [1, 1.3333], // 截图框的宽高比例  1:1
        canMove: true, // 图片是否可以移动
        canMoveBox: true, // 截图框能否拖动
        fixedBox: false, // 固定截图框大小 不允许改变
        original: false, // 上传图片按照原始比例渲染
        autoCrop: true, // 是否默认生成截图框
        autoCropWidth: 200, // 默认生成截图框宽度
        autoCropHeight: 200,  // 默认生成截图框高度
        centerBox: true, // 截图框是否被限制在图片里面
        high: true, // 是否按照设备的dpr 输出等比例图片
        max: 99999, // 上传的最大图片数量
      },
      previews: {},
      resizeHandler: null,
      deptName: undefined, // 部门名称
      deptOptions: undefined, // 部门树选项
      defaultProps: {
        children: "children",
        label: "label"
      },
      stationOptions: undefined, // 站点树选项
      showDeptTree: true, // 是否展示部门树
    };
  },
  dicts: ['yw_mz', 'rd_deputy_type', 'rd_deputy_group', 'rd_station_duty', 'rd_deputy_tags'],
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    }
  },
  created() {
    this.getList();
    this.getDeptTree();
  },
  methods: {
    /** 查询人大代表管理列表 */
    getList() {
      this.loading = true;
      listDeputy(this.queryParams).then(response => {
        this.deputyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        deptId: 100,
        stationId: null,
        type: '0',
        groupId: '6522',
        name: null,
        orderNum: 0,
        nation: null,
        company: null,
        tel: null,
        duty: null,
        birthday: null,
        phone: null,
        resume: null,
        avatar: null,
        openid: null,
        stationDuty: '',
        tags: [],
        isStationDeputy: '0',
        deputyOrderNum: 0,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加人大代表管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getDeputy(id).then(response => {
        this.form = response.data;
        // 将逗号分隔的标签字符串转换为数组
        if (this.form.tags && typeof this.form.tags === 'string' && this.form.tags.trim() !== '') {
          this.form.tags = this.form.tags.split(',').filter(tag => tag.trim() !== '');
        } else {
          this.form.tags = [];
        }
        
        // 确保站内职务字段有值
        if (!this.form.stationDuty) {
          this.form.stationDuty = '';
        }
        
        // 确保是否进站代表字段有值
        if (!this.form.isStationDeputy) {
          this.form.isStationDeputy = '0';
        }
        
        // 确保代表排序字段有值
        if (!this.form.deputyOrderNum && this.form.deputyOrderNum !== 0) {
          this.form.deputyOrderNum = 0;
        }
        
        this.open = true;
        this.title = "修改人大代表管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 将代表标签数组转换为逗号分隔的字符串
          const formData = { ...this.form };
          if (Array.isArray(formData.tags)) {
            formData.tags = formData.tags.length > 0 ? formData.tags.join(',') : '';
          } else if (!formData.tags) {
            formData.tags = '';
          }
          
          // 确保站内职务字段不为undefined
          if (!formData.stationDuty) {
            formData.stationDuty = '';
          }
          
          // 确保是否进站代表字段不为undefined
          if (!formData.isStationDeputy) {
            formData.isStationDeputy = '0';
          }
          
          // 确保代表排序字段不为undefined
          if (!formData.deputyOrderNum && formData.deputyOrderNum !== 0) {
            formData.deputyOrderNum = 0;
          }
          
          if (this.form.id != null) {
            updateDeputy(formData).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDeputy(formData).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('确认删除人大代表？').then(function() {
        return delDeputy(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('renda/deputy/export', {
        ...this.queryParams
      }, `deputy_${new Date().getTime()}.xlsx`)
    },

    /** 批量生成二维码 */
    handleQrcode(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('生成人大代表二维码？').then(function() {
        return genDeputyQrcode(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("生成成功");
      }).catch(() => {});
    },

    // 头像管理
    // 编辑头像
    editCropper() {
      this.options.img = this.form.avatar ? this.baseURL + this.form.avatar : this.defaultAvatar;
      this.avatarSelectorOpen = true;
    },
    // 打开弹出层结束时的回调
    modalOpened() {
      this.visible = true;
      if (!this.resizeHandler) {
        this.resizeHandler = debounce(() => {
          this.refresh()
        }, 100)
      }
      window.addEventListener("resize", this.resizeHandler)
    },
    // 刷新组件
    refresh() {
      this.$refs.cropper.refresh();
    },
    // 覆盖默认的上传行为
    requestUpload() {
    },
    // 向左旋转
    rotateLeft() {
      this.$refs.cropper.rotateLeft();
    },
    // 向右旋转
    rotateRight() {
      this.$refs.cropper.rotateRight();
    },
    // 图片缩放
    changeScale(num) {
      num = num || 1;
      this.$refs.cropper.changeScale(num);
    },
    // 上传预处理
    beforeUpload(file) {
      if (file.type.indexOf("image/") == -1) {
        this.$modal.msgError("文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。");
      } else {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
          this.options.img = reader.result;
        };
      }
    },
    // 确定
    confirmAvatar() {
      this.$refs.cropper.getCropData(data => {
        this.form.avatar = data;
        this.avatarSelectorOpen = false;
      });
    },
    // 取消
    cancelAvatar() {
      this.visible = false;
      this.avatarSelectorOpen = false;
    },
    // 实时预览
    realTime(data) {
      this.previews = data;
    },
    // 关闭窗口
    closeDialog() {
      this.visible = false;
      window.removeEventListener("resize", this.resizeHandler)
    },
    transURL(avatar) {
      if (avatar.startsWith("data:image")) {
        return avatar
      } else {
        return this.baseURL + avatar
      }
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data;
      });
      stationTreeSelect().then(response => {
        this.stationOptions = response.data;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id;
      this.handleQuery();
    },
    // 处理标签变化
    handleTagsChange(value) {
      this.form.tags = value;
    },
    // 处理站内职务变化
    handleStationDutyChange(value) {
      this.form.stationDuty = value;
    },
    // 处理表格中是否进站代表开关变化
    handleStationDeputyChange(row) {
      const data = {
        id: row.id,
        isStationDeputy: row.isStationDeputy
      };
      updateDeputy(data).then(response => {
        this.$modal.msgSuccess("修改成功");
        this.getList();
      }).catch(() => {
        // 如果更新失败，回滚开关状态
        row.isStationDeputy = row.isStationDeputy === '1' ? '0' : '1';
      });
    },
  }
};
</script>

<style lang="scss" scoped>

  .success-icon{
    font-size: 20px;
    color: green;
  }

  .error-icon{
    font-size: 20px;
    color: red;
  }

  .avatar-small {
    width: 50px;
    height: 50px;
  }

  .show-preview {
    margin-left: 10px;
  }

  .user-info-head {
    position: relative;
    display: inline-block;
    height: 133px;
    width: 100px;
  }

  .user-info-head:hover:after {
    content: '+';
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    color: #eee;
    text-align: center;
    background: rgba(0, 0, 0, 0.5);
    font-size: 24px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    cursor: pointer;
    line-height: 133px;
  }

</style>
