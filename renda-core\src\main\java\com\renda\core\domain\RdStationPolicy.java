package com.renda.core.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.renda.common.annotation.Excel;
import com.renda.common.core.domain.BaseEntity;

/**
 * 联络站政策对象 rd_station_policy
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
public class RdStationPolicy extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 联络站ID */
    @Excel(name = "联络站ID")
    private Long stationId;

    /** 政策标题 */
    @Excel(name = "政策标题")
    private String title;

    /** 政策摘要 */
    @Excel(name = "政策摘要")
    private String summary;

    /** 政策内容 */
    @Excel(name = "政策内容")
    private String content;

    /** 政策类型 */
    @Excel(name = "政策类型")
    private Integer type;

    /** 发布部门 */
    @Excel(name = "发布部门")
    private String department;

    /** 发布时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发布时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date publishTime;

    /** 执行时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "执行时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date implementTime;

    /** 有效期 */
    @Excel(name = "有效期")
    private String validPeriod;

    /** 是否置顶（0-不置顶；1-置顶） */
    @Excel(name = "是否置顶", readConverterExp = "0=-不置顶；1-置顶")
    private String isTop;

    /** 状态（0-下线；1-上线） */
    @Excel(name = "状态", readConverterExp = "0=-下线；1-上线")
    private Integer status;

    /** 浏览次数 */
    @Excel(name = "浏览次数")
    private Integer viewCount;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setStationId(Long stationId)
    {
        this.stationId = stationId;
    }

    public Long getStationId()
    {
        return stationId;
    }
    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getTitle()
    {
        return title;
    }
    public void setSummary(String summary)
    {
        this.summary = summary;
    }

    public String getSummary()
    {
        return summary;
    }
    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }
    public void setType(Integer type)
    {
        this.type = type;
    }

    public Integer getType()
    {
        return type;
    }
    public void setDepartment(String department)
    {
        this.department = department;
    }

    public String getDepartment()
    {
        return department;
    }
    public void setPublishTime(Date publishTime)
    {
        this.publishTime = publishTime;
    }

    public Date getPublishTime()
    {
        return publishTime;
    }
    public void setImplementTime(Date implementTime)
    {
        this.implementTime = implementTime;
    }

    public Date getImplementTime()
    {
        return implementTime;
    }
    public void setValidPeriod(String validPeriod)
    {
        this.validPeriod = validPeriod;
    }

    public String getValidPeriod()
    {
        return validPeriod;
    }
    public void setIsTop(String isTop)
    {
        this.isTop = isTop;
    }

    public String getIsTop()
    {
        return isTop;
    }
    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public Integer getStatus()
    {
        return status;
    }
    public void setViewCount(Integer viewCount)
    {
        this.viewCount = viewCount;
    }

    public Integer getViewCount()
    {
        return viewCount;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("stationId", getStationId())
            .append("title", getTitle())
            .append("summary", getSummary())
            .append("content", getContent())
            .append("type", getType())
            .append("department", getDepartment())
            .append("publishTime", getPublishTime())
            .append("implementTime", getImplementTime())
            .append("validPeriod", getValidPeriod())
            .append("isTop", getIsTop())
            .append("status", getStatus())
            .append("viewCount", getViewCount())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
