package com.renda.core.service.impl;

import com.renda.common.annotation.DataScope;
import com.renda.common.config.RendaConfig;
import com.renda.common.utils.DateUtils;
import com.renda.common.utils.SecurityUtils;
import com.renda.common.utils.file.FileUploadUtils;
import com.renda.core.domain.RdFile;
import com.renda.core.mapper.RdFileMapper;
import com.renda.core.service.IRdFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;

/**
 * 制度文件Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
@Service
public class RdFileServiceImpl implements IRdFileService
{
    @Autowired
    private RdFileMapper erFileMapper;

    /**
     * 查询制度文件
     *
     * @param id 制度文件主键
     * @return 制度文件
     */
    @Override
    public RdFile selectRdFileById(Long id)
    {
        return erFileMapper.selectRdFileById(id);
    }

    /**
     * 查询制度文件列表
     *
     * @param erFile 制度文件
     * @return 制度文件
     */
    @Override
    public List<RdFile> selectRdFileList(RdFile erFile)
    {
        return erFileMapper.selectRdFileList(erFile);
    }

    /**
     * 新增制度文件
     *
     * @param erFile 制度文件
     * @return 结果
     */
    @Override
    public int insertRdFile(RdFile erFile)
    {
        // 判断erFile.getFileSize()是否是数字
        if (erFile.getFileSize().trim().matches("[0-9]+")) {
            erFile.setFileSize(FileUploadUtils.getSize(new Long(erFile.getFileSize())));
        }
        erFile.setCreateBy(SecurityUtils.getUsername());
        erFile.setCreateTime(DateUtils.getNowDate());
        erFile.setUpdateBy(SecurityUtils.getUsername());
        erFile.setUpdateTime(DateUtils.getNowDate());
        return erFileMapper.insertRdFile(erFile);
    }

    /**
     * 修改制度文件
     *
     * @param erFile 制度文件
     * @return 结果
     */
    @Override
    public int updateRdFile(RdFile erFile)
    {
        // 判断erFile.getFileSize()是否是数字
        if (erFile.getFileSize().trim().matches("[0-9]+")) {
            erFile.setFileSize(FileUploadUtils.getSize(new Long(erFile.getFileSize())));
        }
        erFile.setUpdateBy(SecurityUtils.getUsername());
        erFile.setUpdateTime(DateUtils.getNowDate());
        return erFileMapper.updateRdFile(erFile);
    }

    /**
     * 批量删除制度文件
     *
     * @param ids 需要删除的制度文件主键
     * @return 结果
     */
    @Override
    public int deleteRdFileByIds(Long[] ids)
    {
        // 删除文件
        for (Long id : ids) {
            RdFile erFile = erFileMapper.selectRdFileById(id);
            deleteRdFileByFilename(erFile.getFileUrl());
        }

        return erFileMapper.deleteRdFileByIds(ids);
    }

    /**
     * 删除制度文件信息
     *
     * @param id 制度文件主键
     * @return 结果
     */
    @Override
    public int deleteRdFileById(Long id)
    {
        // 删除文件
        RdFile erFile = erFileMapper.selectRdFileById(id);
        deleteRdFileByFilename(erFile.getFileUrl());

        return erFileMapper.deleteRdFileById(id);
    }

    /**
     * 根据文件名删除文件
     *
     * @param filename 文件名
     */
    @Override
    public void deleteRdFileByFilename(String filename) {
        // 处理文件名
        filename = filename.substring(8);
        filename = RendaConfig.getProfile() + filename;

        // 删除filename文件
        File file = new File(filename);
        if(file.exists()) {
            // 如果存在，则删除文件
            file.delete();
        }
    }
}
