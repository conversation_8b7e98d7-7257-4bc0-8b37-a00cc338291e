package com.renda.core.service;

import java.util.List;
import com.renda.core.domain.RdJob;
import com.renda.core.domain.vo.DeputyStatVO;
import com.renda.core.domain.vo.JobInfoVO;
import com.renda.core.domain.vo.JobStatVO;

/**
 * 履职工作Service接口
 *
 * <AUTHOR>
 * @date 2023-11-12
 */
public interface IRdJobService
{
    /**
     * 查询履职工作
     *
     * @param id 履职工作主键
     * @return 履职工作
     */
    public RdJob selectRdJobById(Long id);

    /**
     * 查询履职工作列表
     *
     * @param rdJob 履职工作
     * @return 履职工作集合
     */
    public List<RdJob> selectRdJobList(RdJob rdJob);

    /**
     * 新增履职工作
     *
     * @param rdJob 履职工作
     * @return 结果
     */
    public int insertRdJob(RdJob rdJob);

    /**
     * 修改履职工作
     *
     * @param rdJob 履职工作
     * @return 结果
     */
    public int updateRdJob(RdJob rdJob);

    /**
     * 批量删除履职工作
     *
     * @param ids 需要删除的履职工作主键集合
     * @return 结果
     */
    public int deleteRdJobByIds(Long[] ids);

    /**
     * 删除履职工作信息
     *
     * @param id 履职工作主键
     * @return 结果
     */
    public int deleteRdJobById(Long id);

    /***
     * 获取履职工作详情
     * @param id 履职工作ID
     */
    public RdJob selectJobExtById(Long id);

    /***
     *  发布履职工作接口
     * @return 建议意见详情
     */
    void saveJob(JobInfoVO Job);

    /***
     *  删除履职工作接口
     * @return 建议意见详情
     */
    void deleteJobById(Long id);

    /**
     * 获取人大代表履职统计信息
     */
    List<JobStatVO> getJobStat();

}
