package com.renda.core.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.renda.common.annotation.Excel;
import com.renda.common.core.domain.BaseEntity;

/**
 * 建议代对象 rd_recom_deputy
 *
 * <AUTHOR>
 * @date 2023-11-23
 */
public class RdRecomDeputy extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 建议ID */
    private Long recomId;

    /** 代表ID */
    private Long deputyId;

    /** 代表姓名 */
    private String deputyName;

    /** 是否得到答复 0-未答复；1-已答复 */
    private Integer hasReply;

    /** 答复满意度 */
    private Integer replyRate;

    /** 代表头像 */
    private String deputyAvatar;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setRecomId(Long recomId)
    {
        this.recomId = recomId;
    }

    public Long getRecomId()
    {
        return recomId;
    }
    public void setDeputyId(Long deputyId)
    {
        this.deputyId = deputyId;
    }

    public Long getDeputyId()
    {
        return deputyId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("recomId", getRecomId())
            .append("deputyId", getDeputyId())
            .toString();
    }

    public String getDeputyName() {
        return deputyName;
    }

    public void setDeputyName(String deputyName) {
        this.deputyName = deputyName;
    }

    public Integer getHasReply() {
        return hasReply;
    }

    public void setHasReply(Integer hasReply) {
        this.hasReply = hasReply;
    }

    public Integer getReplyRate() {
        return replyRate;
    }

    public void setReplyRate(Integer replyRate) {
        this.replyRate = replyRate;
    }

    public String getDeputyAvatar() {
        return deputyAvatar;
    }

    public void setDeputyAvatar(String deputyAvatar) {
        this.deputyAvatar = deputyAvatar;
    }
}
