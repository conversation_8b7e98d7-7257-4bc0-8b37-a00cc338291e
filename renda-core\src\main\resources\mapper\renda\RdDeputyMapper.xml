<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renda.core.mapper.RdDeputyMapper">

    <resultMap type="RdDeputy" id="RdDeputyResult">
        <result property="id"    column="id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="stationId"    column="station_id"    />
        <result property="stationName"    column="station_name"    />
        <result property="type"    column="type"    />
        <result property="groupId"    column="group_id"    />
        <result property="groupName"    column="group_name"    />
        <result property="level"    column="level"    />
        <result property="orderNum"    column="order_num"    />
        <result property="orderNum1"    column="order_num1"    />
        <result property="name"    column="name"    />
        <result property="nation"    column="nation"    />
        <result property="company"    column="company"    />
        <result property="tel"    column="tel"    />
        <result property="duty"    column="duty"    />
        <result property="birthday"    column="birthday"    />
        <result property="phone"    column="phone"    />
        <result property="resume"    column="resume"    />
        <result property="avatar"    column="avatar"    />
        <result property="openid"    column="openid"    />
        <result property="qrcodeUrl"    column="qrcode_url"    />
        <result property="stationDuty"    column="station_duty"    />
        <result property="tags"    column="tags"    />
        <result property="isStationDeputy"    column="is_station_deputy"    />
        <result property="deputyOrderNum"    column="deputy_order_num"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectRdDeputyVo">
        select id, dept_id,
               (select dept_name from sys_dept where dept_id = d.dept_id) as dept_name,
               station_id,
               (select station_name from rd_station where station_id = d.station_id) as station_name,
               level, order_num, round(order_num) as order_num1,
               type, group_id, name, nation, company, tel, duty, birthday, phone, resume, avatar, openid, qrcode_url,
               station_duty, tags, is_station_deputy, deputy_order_num,
               create_by, create_time, update_by, update_time,
               (SELECT dict_label FROM sys_dict_data where dict_type = 'rd_deputy_group' and dict_value = d.group_id) as group_name
        from rd_deputy d
    </sql>

    <select id="selectRdDeputyList" parameterType="RdDeputy" resultMap="RdDeputyResult">
        <include refid="selectRdDeputyVo"/>
        <where>
            <if test="type != null "> and type = #{type}</if>
            <if test="groupId != null  and groupId != ''"> and group_id like concat(#{groupId}, '%')</if>
            <if test="level != null "> and level = #{level}</if>
            <if test="deptId != null "> and (d.dept_id = #{deptId} OR d.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))</if>
            <if test="stationId != null "> and (d.station_id = #{stationId} OR d.station_id IN ( SELECT t.station_id FROM rd_station t WHERE find_in_set(#{stationId}, ancestors) ))</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="nation != null  and nation != ''"> and nation = #{nation}</if>
            <if test="company != null  and company != ''"> and company like concat('%', #{company}, '%')</if>
            <if test="tel != null  and tel != ''"> and tel like concat('%', #{tel}, '%')</if>
            <if test="duty != null  and duty != ''"> and duty like concat('%', #{duty}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone like concat('%', #{phone}, '%')</if>
            <if test="stationDuty != null  and stationDuty != ''"> and station_duty = #{stationDuty}</if>
            <if test="isStationDeputy != null  and isStationDeputy != ''"> and is_station_deputy = #{isStationDeputy}</if>
            ${params.dataScope}
        </where>
        <if test="isStationDeputy == 1">
            order by deputy_order_num
        </if>
        <if test="isStationDeputy == null or isStationDeputy != 1">
            order by order_num
        </if>
    </select>

    <select id="selectRdDeputyList2" parameterType="RdDeputy" resultMap="RdDeputyResult">
        <include refid="selectRdDeputyVo"/>
        <where>
            <if test="type != null "> and type = #{type}</if>
            <if test="groupId != null  and groupId != ''"> and group_id like concat(#{groupId}, '%')</if>
            <if test="level != null "> and level = #{level}</if>
            <if test="deptId != null "> and (d.dept_id = #{deptId} OR d.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId}, ancestors) ))</if>
            <if test="stationId != null "> and (d.station_id = #{stationId} OR d.station_id IN ( SELECT t.station_id FROM rd_station t WHERE find_in_set(#{stationId}, ancestors) ))</if>
            <if test="name != null  and name != ''">
                and ( name like concat('%', #{name}, '%')
                    or pinyin like concat('%', UPPER(TRIM(#{name})), '%')
                    or company like concat('%', #{name}, '%')
                    or tel like concat('%', #{name}, '%')
                    or duty like concat('%', #{name}, '%')
                    or phone like concat('%', #{name}, '%')
                )
            </if>
        </where>
        order by order_num
    </select>

    <select id="selectRdDeputyById" parameterType="Long" resultMap="RdDeputyResult">
        <include refid="selectRdDeputyVo"/>
        where id = #{id}
    </select>

    <select id="selectRdDeputyInfoById" resultType="com.renda.core.domain.vo.DeputyInfoVO">
        select id, name, type, group_id, level, order_num,
               (select dict_label from sys_dict_data where dict_type = 'yw_mz' and dict_value = d.nation) as nation,
               company, tel, duty, birthday, phone, resume, avatar, qrcode_url, is_station_deputy,
               (select station_name from rd_station where station_id = d.station_id) as stationName
        from rd_deputy d
        where id = #{id}
    </select>

    <select id="selectDeputyByPhone" resultMap="RdDeputyResult">
        <include refid="selectRdDeputyVo"/>
        where phone = #{phone}
    </select>

    <select id="getDeputyStatistics" resultType="com.renda.core.domain.vo.DeputyStatisticsInfoVO">
        select (select count(*) from rd_job where deputy_id = #{userId}) as jobCount,
               (select count(*) from rd_activity_registration where deputy_id = #{userId} and registration_type = 1) as activityCount,
               (select count(*) from rd_advice where deputy_id = #{userId}) as adviceCount
    </select>

    <select id="getDeputyStat" parameterType="Long" resultType="com.renda.core.domain.vo.DeputyStatVO">
        select d.dict_label as title, count(j.deputy_id) as `count`
        from sys_dict_data d
            left join rd_job j on j.job_type = d.dict_value and j.deputy_id = #{id}
        where dict_type = 'job_type'
          and d.`status` = '0'
        group by d.dict_label,
                 d.dict_value,
                 d.remark
        order by d.dict_value
    </select>

    <select id="selectRdDeputyListByDeptId" parameterType="Long" resultMap="RdDeputyResult">
        <include refid="selectRdDeputyVo"/>
        where dept_id = #{id}
    </select>

    <select id="countByStationIdAndStationDuty" resultType="int">
        select count(*)
        from rd_deputy
        where is_station_deputy = '1'
          and station_duty = #{stationDuty}
          and station_duty is not null
          and station_duty != ''
          <if test="excludeId != null">
              and id != #{excludeId}
          </if>
    </select>

    <insert id="insertRdDeputy" parameterType="RdDeputy" useGeneratedKeys="true" keyProperty="id">
        insert into rd_deputy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="deptId != null and deptId != ''">dept_id,</if>
            <if test="stationId != null and stationId != ''">station_id,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="groupId != null and groupId != ''">group_id,</if>
            <if test="level != null and level != ''">level,</if>
            <if test="orderNum != null and orderNum != ''">order_num,</if>
            <if test="name != null and name != ''">name, pinyin,</if>
            <if test="nation != null">nation,</if>
            <if test="company != null">company,</if>
            <if test="tel != null">tel,</if>
            <if test="duty != null">duty,</if>
            <if test="birthday != null">birthday,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="resume != null">resume,</if>
            <if test="avatar != null">avatar,</if>
            <if test="openid != null">openid,</if>
            <if test="stationDuty != null">station_duty,</if>
            <if test="tags != null">tags,</if>
            <if test="isStationDeputy != null">is_station_deputy,</if>
            <if test="deputyOrderNum != null">deputy_order_num,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="deptId != null and deptId != ''">#{deptId},</if>
            <if test="stationId != null and stationId != ''">#{stationId},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="groupId != null and groupId != ''">#{groupId},</if>
            <if test="level != null and level != ''">#{level},</if>
            <if test="orderNum != null and orderNum != ''">#{orderNum},</if>
            <if test="name != null and name != ''">#{name}, pinyin(#{name}),</if>
            <if test="nation != null">#{nation},</if>
            <if test="company != null">#{company},</if>
            <if test="tel != null">#{tel},</if>
            <if test="duty != null">#{duty},</if>
            <if test="birthday != null">#{birthday},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="resume != null">#{resume},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="openid != null">#{openid},</if>
            <if test="stationDuty != null">#{stationDuty},</if>
            <if test="tags != null">#{tags},</if>
            <if test="isStationDeputy != null">#{isStationDeputy},</if>
            <if test="deputyOrderNum != null">#{deputyOrderNum},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRdDeputy" parameterType="RdDeputy">
        update rd_deputy
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="groupId != null and groupId != ''">group_id = #{groupId},</if>
            <if test="level != null and level != ''">level = #{level},</if>
            <if test="orderNum != null and orderNum != ''">order_num = #{orderNum},</if>
            <if test="deptId != null and deptId != ''">dept_id = #{deptId},</if>
            <if test="stationId != null and stationId != ''">station_id = #{stationId},</if>
            <if test="name != null and name != ''">name = #{name}, pinyin = pinyin(#{name}),</if>
            <if test="nation != null">nation = #{nation},</if>
            <if test="company != null">company = #{company},</if>
            <if test="tel != null">tel = #{tel},</if>
            <if test="duty != null">duty = #{duty},</if>
            <if test="birthday != null">birthday = #{birthday},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="resume != null">resume = #{resume},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="openid != null">openid = #{openid},</if>
            <if test="qrcodeUrl != null">qrcode_url = #{qrcodeUrl},</if>
            <if test="stationDuty != null">station_duty = #{stationDuty},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="isStationDeputy != null">is_station_deputy = #{isStationDeputy},</if>
            <if test="deputyOrderNum != null">deputy_order_num = #{deputyOrderNum},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRdDeputyById" parameterType="Long">
        delete from rd_deputy where id = #{id}
    </delete>

    <delete id="deleteRdDeputyByIds" parameterType="String">
        delete from rd_deputy where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
