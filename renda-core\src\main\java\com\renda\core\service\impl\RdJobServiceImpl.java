package com.renda.core.service.impl;

import java.util.List;
import com.renda.common.utils.DateUtils;
import com.renda.common.utils.SecurityUtils;
import com.renda.common.utils.bean.BeanUtils;
import com.renda.core.domain.RdJobAttachment;
import com.renda.core.domain.vo.JobInfoVO;
import com.renda.core.domain.vo.JobStatVO;
import com.renda.core.service.IRdJobAttachmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.renda.core.mapper.RdJobMapper;
import com.renda.core.domain.RdJob;
import com.renda.core.service.IRdJobService;

/**
 * 履职工作Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-12
 */
@Service
public class RdJobServiceImpl implements IRdJobService
{
    @Autowired
    private RdJobMapper rdJobMapper;

    @Autowired
    private IRdJobAttachmentService JobAttachmentService;

    /**
     * 查询履职工作
     *
     * @param id 履职工作主键
     * @return 履职工作
     */
    @Override
    public RdJob selectRdJobById(Long id)
    {
        return rdJobMapper.selectRdJobById(id);
    }

    /**
     * 查询履职工作列表
     *
     * @param rdJob 履职工作
     * @return 履职工作
     */
    @Override
    public List<RdJob> selectRdJobList(RdJob rdJob)
    {
        return rdJobMapper.selectRdJobList(rdJob);
    }

    /**
     * 新增履职工作
     *
     * @param rdJob 履职工作
     * @return 结果
     */
    @Override
    public int insertRdJob(RdJob rdJob)
    {
        rdJob.setCreateTime(DateUtils.getNowDate());
        return rdJobMapper.insertRdJob(rdJob);
    }

    /**
     * 修改履职工作
     *
     * @param rdJob 履职工作
     * @return 结果
     */
    @Override
    public int updateRdJob(RdJob rdJob)
    {
        return rdJobMapper.updateRdJob(rdJob);
    }

    /**
     * 批量删除履职工作
     *
     * @param ids 需要删除的履职工作主键
     * @return 结果
     */
    @Override
    public int deleteRdJobByIds(Long[] ids)
    {
        for (Long id : ids) {
            deleteRdJobById(id);
        }
        return 1;
    }

    /**
     * 删除履职工作信息
     *
     * @param id 履职工作主键
     * @return 结果
     */
    @Override
    public int deleteRdJobById(Long id)
    {
        JobAttachmentService.deleteJobAttachments(id);
        return rdJobMapper.deleteRdJobById(id);
    }

    /***
     * 获取履职工作详情
     * @param id 履职工作ID
     */
    @Override
    public RdJob selectJobExtById(Long id) {
        return rdJobMapper.selectJobExtById(id);
    }

    /***
     *  发布履职工作接口
     * @return 建议意见详情
     */
    @Override
    public void saveJob(JobInfoVO job) {
        // 保存意见建议
        RdJob rdJob = new RdJob();
        BeanUtils.copyBeanProp(rdJob, job);
        rdJob.setDeputyId(SecurityUtils.getLoginUser().getUserId());
        rdJob.setCreateTime(DateUtils.getNowDate());
        insertRdJob(rdJob);

        // 保存附件图片
        job.getImgList().forEach(img->{
            RdJobAttachment attachment = new RdJobAttachment();
            attachment.setJobId(rdJob.getId());
            attachment.setFileType(1);
            attachment.setFileUrl(img);
            JobAttachmentService.insertRdJobAttachment(attachment);
        });

        // 保存附件文件
        job.getFileList().forEach(file->{
            RdJobAttachment attachment = new RdJobAttachment();
            attachment.setJobId(rdJob.getId());
            attachment.setFileType(2);
            attachment.setFileName(file.getFileName());
            attachment.setFileUrl(file.getFileUrl());
            JobAttachmentService.insertRdJobAttachment(attachment);
        });
    }

    /***
     *  删除履职工作接口
     * @return 建议意见详情
     */
    @Override
    public void deleteJobById(Long id) {
        JobAttachmentService.deleteJobAttachments(id);
        rdJobMapper.deleteRdJobById(id);
    }

    /**
     * 获取人大代表履职统计信息
     */
    @Override
    public List<JobStatVO> getJobStat() {
        return rdJobMapper.getJobStat();
    }

}
