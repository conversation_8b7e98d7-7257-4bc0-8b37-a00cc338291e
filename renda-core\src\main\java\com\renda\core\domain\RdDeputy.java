package com.renda.core.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.renda.common.annotation.Excel;
import com.renda.common.core.domain.BaseEntity;

/**
 * 人大代表管理对象 rd_deputy
 *
 * <AUTHOR>
 * @date 2023-10-23
 */
public class RdDeputy extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 人大代表ID */
    private Long id;

    /** 显示顺序 */
    private float orderNum;

    /** 显示顺序 */
    @Excel(name = "序号")
    private int orderNum1;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 单位 */
    @Excel(name = "单位")
    private String company;

    /** 职务 */
    @Excel(name = "职务")
    private String duty;

    /** 所属站点 */
    @Excel(name = "所属站点")
    private String stationName;

    /** 电话 */
    @Excel(name = "联系电话")
    private String phone;

    /** 代表团名称 */
    @Excel(name = "代表团")
    private String groupName;

    /** 部门ID */
    //@Excel(name = "部门编号", type = Excel.Type.IMPORT)
    private Long deptId;

    /** 所属部门 */
    //@Excel(name = "所属部门")
    private String deptName;

    /** 站点ID */
    //@Excel(name = "站点编号", type = Excel.Type.IMPORT)
    private Long stationId;

    /** 代表类型：0-代表；1-委员；2-管理； */
    //@Excel(name = "代表类型：0-代表；1-委员；2-管理；")
    private String type;

    /** 代表团 */
    private String groupId;

    /** 代表层级 */
    //@Excel(name = "代表层级")
    private String level;

    /** 民族 */
    //@Excel(name = "民族")
    private String nation;

    /** 联系电话 */
    //@Excel(name = "联系电话")
    private String tel;

    /** 出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    //@Excel(name = "出生日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date birthday;

    /** 代表履历 */
    private String resume;

    /** 头像 */
    //@Excel(name = "头像")
    private String avatar;

    /** openid */
    private String openid;

    /** 二维码地址 */
    private String qrcodeUrl;

    /** 站内职务 */
    private String stationDuty;

    /** 代表标签 */
    private String tags;

    /** 是否进站代表 */
    @Excel(name = "是否进站代表")
    private String isStationDeputy;

    /** 进站代表排序序号 */
    @Excel(name = "进站代表排序序号")
    private Integer deputyOrderNum;

    public void setId(Long id)
    {
        this.id = id;
    }
    public Long getId()
    {
        return id;
    }

    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }
    public Long getDeptId()
    {
        return deptId;
    }

    public void setDeptName(String deptName)
    {
        this.deptName = deptName;
    }
    public String getDeptName()
    {
        return deptName;
    }

    public void setType(String type)
    {
        this.type = type;
    }
    public String getType()
    {
        return type;
    }

    public void setName(String name)
    {
        this.name = name;
    }
    public String getName()
    {
        return name;
    }

    public void setNation(String nation)
    {
        this.nation = nation;
    }
    public String getNation()
    {
        return nation;
    }

    public void setDuty(String duty)
    {
        this.duty = duty;
    }
    public String getDuty()
    {
        return duty;
    }

    public void setBirthday(Date birthday)
    {
        this.birthday = birthday;
    }
    public Date getBirthday()
    {
        return birthday;
    }

    public void setPhone(String phone)
    {
        this.phone = phone;
    }
    public String getPhone()
    {
        return phone;
    }

    public void setResume(String resume)
    {
        this.resume = resume;
    }
    public String getResume()
    {
        return resume;
    }

    public void setAvatar(String avatar)
    {
        this.avatar = avatar;
    }
    public String getAvatar()
    {
        return avatar;
    }

    public void setOpenid(String openid)
    {
        this.openid = openid;
    }
    public String getOpenid()
    {
        return openid;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("deptName", getDeptName())
            .append("type", getType())
            .append("name", getName())
            .append("nation", getNation())
            .append("duty", getDuty())
            .append("birthday", getBirthday())
            .append("phone", getPhone())
            .append("resume", getResume())
            .append("avatar", getAvatar())
            .append("openid", getOpenid())
            .append("qrcodeUrl", getQrcodeUrl())
            .append("stationDuty", getStationDuty())
            .append("tags", getTags())
            .append("isStationDeputy", getIsStationDeputy())
            .append("deputyOrderNum", getDeputyOrderNum())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }

    public String getQrcodeUrl() {
        return qrcodeUrl;
    }

    public void setQrcodeUrl(String qrcodeUrl) {
        this.qrcodeUrl = qrcodeUrl;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public Long getStationId() {
        return stationId;
    }

    public void setStationId(Long stationId) {
        this.stationId = stationId;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public float getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(float orderNum) {
        this.orderNum = orderNum;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public float getOrderNum1() {
        return orderNum1;
    }

    public void setOrderNum1(int orderNum1) {
        this.orderNum1 = orderNum1;
    }

    public String getStationDuty() {
        return stationDuty;
    }

    public void setStationDuty(String stationDuty) {
        this.stationDuty = stationDuty;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getIsStationDeputy() {
        return isStationDeputy;
    }

    public void setIsStationDeputy(String isStationDeputy) {
        this.isStationDeputy = isStationDeputy;
    }

    public Integer getDeputyOrderNum() {
        return deputyOrderNum;
    }

    public void setDeputyOrderNum(Integer deputyOrderNum) {
        this.deputyOrderNum = deputyOrderNum;
    }
}
