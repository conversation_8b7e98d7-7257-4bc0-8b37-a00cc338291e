package com.renda.core.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class FeedbackInfoVO {

    /** 反馈ID */
    private Long id;

    /** 反馈类型：1-普通；2-上报 */
    private Integer feedbackType;

    /** 用户类型：0-代表；1-群众 */
    private  Integer userType;

    /** 用户ID */
    private Long userId;

    /** 姓名 */
    private String name;

    /** 头像 */
    private String avatar;

    /** 内容 */
    private String content;

    /** 反馈时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    /** 附件列表 */
    private List<FeedbackAttachmentVO> attachs;

}
