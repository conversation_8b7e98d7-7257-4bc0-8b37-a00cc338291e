{"remainingRequest": "D:\\renda\\renda-back\\renda-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\renda\\renda-back\\renda-ui\\src\\views\\renda\\advice\\index.vue?vue&type=style&index=0&id=7907d1ec&lang=scss&scoped=true", "dependencies": [{"path": "D:\\renda\\renda-back\\renda-ui\\src\\views\\renda\\advice\\index.vue", "mtime": 1752730489373}, {"path": "D:\\renda\\renda-back\\renda-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\renda\\renda-back\\renda-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\renda\\renda-back\\renda-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\renda\\renda-back\\renda-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\renda\\renda-back\\renda-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\renda\\renda-back\\renda-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmltZy1jb250YWluZXIgewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IHJvdzsKICBmbGV4LXdyYXA6IHdyYXA7Cn0KLmltZy1pdGVtIHsKICB3aWR0aDogMjAwcHg7CiAgaGVpZ2h0OiAxNTBweDsKICBtYXJnaW46IDEycHg7Cn0KLml0ZW0gewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IHJvdzsKICAubGFiZWwgewogICAgd2lkdGg6IDgwcHg7CiAgICBsaW5lLWhlaWdodDogMzZweDsKICAgIHRleHQtYWxpZ246IHJpZ2h0OwogICAgZm9udC1zaXplOiAxNHB4OwogICAgY29sb3I6ICM2MDYyNjY7CiAgICBwYWRkaW5nOiAwIDEycHggMCAwOwogICAgZm9udC13ZWlnaHQ6IDcwMDsKICB9CiAgLmNvbnRlbnQgewogICAgZmxleDogMTsKICAgIG1hcmdpbi10b3A6IDZweDsKICB9Cn0KLnN0ZXAtbGFiZWwgewogIGZvbnQtc2l6ZTogMTRweDsKICBjb2xvcjogIzYwNjI2NjsKfQouc3RlcC1kZXNjIHsKICBmb250LXNpemU6IDE0cHg7CiAgY29sb3I6ICM2MDYyNjY7Cn0KLmZlZWRiYWNrLWNvbnRlbnQgewogIG1hcmdpbjogMTBweCAwOwp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2rBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/renda/advice", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"标题\" prop=\"title\">\r\n        <el-input\r\n          v-model=\"queryParams.title\"\r\n          placeholder=\"请输入标题\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"姓名\" prop=\"name\">\r\n        <el-input\r\n          v-model=\"queryParams.name\"\r\n          placeholder=\"请输入姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"电话\" prop=\"phone\">\r\n        <el-input\r\n          v-model=\"queryParams.phone\"\r\n          placeholder=\"请输入电话\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['renda:advice:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['renda:advice:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"adviceList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"代表单位\" align=\"left\" prop=\"deputyCompany\" />\r\n      <el-table-column label=\"代表\" align=\"left\" prop=\"deputyName\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{scope.row.deputyName}}({{scope.row.deputyDuty}},{{scope.row.deputyPhone}})</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"标题\" align=\"left\" prop=\"title\" />\r\n      <el-table-column label=\"群众姓名\" align=\"center\" prop=\"name\" />\r\n      <el-table-column label=\"群众电话\" align=\"center\" prop=\"phone\" />\r\n      <el-table-column label=\"建议类别\" align=\"center\" prop=\"category\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.rd_advice_category\" :value=\"scope.row.category\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"处理状态\" align=\"center\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.rd_advice_status\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"服务评分\" align=\"center\" prop=\"serviceRating\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.serviceRating\">{{scope.row.serviceRating}}分</span>\r\n          <span v-else>-</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"提交时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleDetail(scope.row)\"\r\n            v-hasPermi=\"['renda:advice:list']\"\r\n          >详情</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['renda:advice:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['renda:advice:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 建议详情对话框 -->\r\n    <el-dialog title=\"群众建议详情\" :visible.sync=\"showAdviceDetail\" width=\"1000px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"adviceForm\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"建议标题\" prop=\"title\">\r\n              <el-input v-model=\"adviceForm.title\" readonly />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"建议时间\" prop=\"createTime\">\r\n              <el-date-picker clearable\r\n                              readonly\r\n                              v-model=\"adviceForm.createTime\"\r\n                              type=\"datetime\"\r\n                              value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                              placeholder=\"请选择活动时间\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"建议类别\" prop=\"category\">\r\n              <dict-tag :options=\"dict.type.rd_advice_category\" :value=\"adviceForm.category\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"处理状态\" prop=\"status\">\r\n              <dict-tag :options=\"dict.type.rd_advice_status\" :value=\"adviceForm.status\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"服务评分\" prop=\"serviceRating\">\r\n              <span v-if=\"adviceForm.serviceRating\">{{adviceForm.serviceRating}}分</span>\r\n              <span v-else>-</span>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"建议内容\" prop=\"content\">\r\n              <el-input v-model=\"adviceForm.content\" type=\"textarea\" :rows=\"10\" readonly />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row v-if=\"adviceForm && adviceForm.attachmentList && adviceForm.attachmentList.length > 0\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"附件\" prop=\"attachments\">\r\n              <div v-for=\"(item, index) of adviceForm.attachmentList\" :key=\"index\" >\r\n                <el-link v-if=\"item.fileType === 2\" type=\"primary\" icon=\"el-icon-document\" :href=\"item.fileUrl\" target=\"_blank\">{{item.fileName}}</el-link>\r\n              </div>\r\n              <div class=\"img-container\" >\r\n                <el-image v-for=\"(item, index) of adviceForm.attachmentList\"\r\n                          :key=\"index\"\r\n                          v-if=\"item.fileType === 1\"\r\n                          class=\"img-item\"\r\n                          :src=\"item.fileUrl\"\r\n                          :preview-src-list=\"getPreviewImgList(adviceForm.attachmentList, index)\"></el-image>\r\n              </div>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <div class=\"item\">\r\n              <div class=\"label\">答复</div>\r\n              <div class=\"content\">\r\n                <el-steps direction=\"vertical\" :active=\"3\">\r\n                  <el-step v-for=\"(item, index) of adviceForm.feedbackList\" :key=\"index\">\r\n                    <div slot=\"title\" class=\"step-label\">\r\n                      {{item.name}}({{item.createTime}})\r\n                      <el-button\r\n                        size=\"mini\"\r\n                        type=\"text\"\r\n                        icon=\"el-icon-edit\"\r\n                        @click=\"handleEditFeedback(item)\"\r\n                        v-hasPermi=\"['renda:feedback:edit']\"\r\n                        style=\"margin-left: 10px;\"\r\n                      >编辑</el-button>\r\n                    </div>\r\n                    <div slot=\"description\" class=\"step-desc\">\r\n                      <div class=\"feedback-content\">{{item.content}}</div>\r\n                      <div v-for=\"(fileItem, fileIndex) of item.attachmentList\" :key=\"fileIndex\" >\r\n                        <el-link v-if=\"fileItem.fileType === 2\" type=\"primary\" icon=\"el-icon-document\" :href=\"fileItem.fileUrl\" target=\"_blank\">{{fileItem.fileName}}</el-link>\r\n                      </div>\r\n                      <div class=\"img-container\" >\r\n                        <el-image v-for=\"(imgItem, imgIndex) of item.attachmentList\"\r\n                                  :key=\"imgIndex\"\r\n                                  v-if=\"imgItem.fileType === 1\"\r\n                                  class=\"img-item\"\r\n                                  :src=\"imgItem.fileUrl\"\r\n                                  :preview-src-list=\"getPreviewImgList(item.attachmentList, imgIndex)\"></el-image>\r\n                      </div>\r\n                    </div>\r\n                  </el-step>\r\n                </el-steps>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-edit\"\r\n          @click=\"handleUpdate(adviceForm)\"\r\n          v-hasPermi=\"['renda:advice:edit']\"\r\n        >编辑建议</el-button>\r\n        <el-button @click=\"showAdviceDetail = false\">关闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 修改建议对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"editForm\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"建议标题\" prop=\"title\">\r\n              <el-input v-model=\"form.title\" placeholder=\"请输入建议标题\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"建议内容\" prop=\"content\">\r\n              <el-input v-model=\"form.content\" type=\"textarea\" :rows=\"6\" placeholder=\"请输入建议内容\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"建议时间\" prop=\"createTime\">\r\n              <el-date-picker\r\n                v-model=\"form.createTime\"\r\n                type=\"datetime\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                placeholder=\"请选择建议时间\"\r\n                style=\"width: 100%\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"建议类别\" prop=\"category\">\r\n              <el-select v-model=\"form.category\" placeholder=\"请选择建议类别\" clearable style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"dict in dict.type.rd_advice_category\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"处理状态\" prop=\"status\">\r\n              <el-select v-model=\"form.status\" placeholder=\"请选择处理状态\" clearable style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"dict in dict.type.rd_advice_status\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"服务评分\" prop=\"serviceRating\">\r\n              <el-rate\r\n                v-model=\"form.serviceRating\"\r\n                :max=\"5\"\r\n                :colors=\"['#99A9BF', '#F7BA2A', '#FF9900']\"\r\n                :texts=\"['非常差', '差', '一般', '好', '非常好']\"\r\n                show-text>\r\n              </el-rate>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" :loading=\"loading\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 修改反馈对话框 -->\r\n    <el-dialog title=\"修改反馈\" :visible.sync=\"feedbackEditOpen\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"feedbackEditForm\" :model=\"feedbackForm\" :rules=\"feedbackRules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"反馈内容\" prop=\"content\">\r\n              <el-input v-model=\"feedbackForm.content\" type=\"textarea\" :rows=\"6\" placeholder=\"请输入反馈内容\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"反馈时间\" prop=\"createTime\">\r\n              <el-date-picker\r\n                v-model=\"feedbackForm.createTime\"\r\n                type=\"datetime\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                placeholder=\"请选择反馈时间\"\r\n                style=\"width: 100%\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"服务评分\" prop=\"serviceRating\">\r\n              <el-rate\r\n                v-model=\"feedbackForm.serviceRating\"\r\n                :max=\"5\"\r\n                :colors=\"['#99A9BF', '#F7BA2A', '#FF9900']\"\r\n                :texts=\"['非常差', '差', '一般', '好', '非常好']\"\r\n                show-text>\r\n              </el-rate>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" :loading=\"loading\" @click=\"submitFeedbackForm\">确 定</el-button>\r\n        <el-button @click=\"cancelFeedback\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listAdvice, getAdvice, delAdvice, updateAdvice } from '@/api/renda/advice'\r\nimport { getFeedback, updateFeedback } from '@/api/renda/feedback'\r\n\r\nexport default {\r\n  name: \"Advice\",\r\n  dicts: ['rd_advice_category', 'rd_advice_status'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 群众建议表格数据\r\n      adviceList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        title: null,\r\n        content: null,\r\n        name: null,\r\n        phone: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        title: [\r\n          { required: true, message: \"建议标题不能为空\", trigger: \"blur\" },\r\n          { min: 1, max: 200, message: \"标题长度应在1到200个字符之间\", trigger: \"blur\" },\r\n          { validator: this.validateTitle, trigger: \"blur\" }\r\n        ],\r\n        content: [\r\n          { required: true, message: \"建议内容不能为空\", trigger: \"blur\" },\r\n          { min: 1, max: 2000, message: \"内容长度应在1到2000个字符之间\", trigger: \"blur\" },\r\n          { validator: this.validateContent, trigger: \"blur\" }\r\n        ],\r\n        createTime: [\r\n          { required: true, message: \"建议时间不能为空\", trigger: \"change\" },\r\n          { validator: this.validateCreateTime, trigger: \"change\" }\r\n        ],\r\n        category: [\r\n          { required: true, message: \"建议类别不能为空\", trigger: \"change\" }\r\n        ],\r\n        status: [\r\n          { required: true, message: \"处理状态不能为空\", trigger: \"change\" }\r\n        ],\r\n      },\r\n      showAdviceDetail: false, // 显示群众建议详情对话框\r\n      adviceForm: {}, // 群众建议表单\r\n      baseURL: process.env.VUE_APP_BASE_API, // 图片上传地址头\r\n      srcList: [], // 图片预览列表\r\n      // 反馈编辑相关\r\n      feedbackEditOpen: false, // 显示反馈编辑对话框\r\n      feedbackForm: {}, // 反馈表单\r\n      feedbackRules: {\r\n        content: [\r\n          { required: true, message: \"反馈内容不能为空\", trigger: \"blur\" },\r\n          { min: 1, max: 2000, message: \"内容长度应在1到2000个字符之间\", trigger: \"blur\" },\r\n          { validator: this.validateFeedbackContent, trigger: \"blur\" }\r\n        ],\r\n        createTime: [\r\n          { required: true, message: \"反馈时间不能为空\", trigger: \"change\" },\r\n          { validator: this.validateFeedbackTime, trigger: \"change\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询群众建议列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listAdvice(this.queryParams).then(response => {\r\n        this.adviceList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n        console.log(this.adviceList)\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        title: null,\r\n        content: null,\r\n        createTime: null,\r\n        category: null,\r\n        status: null,\r\n        serviceRating: null,\r\n      };\r\n      this.resetForm(\"editForm\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids\r\n      getAdvice(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改群众建议\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"editForm\"].validate(valid => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          updateAdvice(this.form).then(response => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.open = false;\r\n            this.getList();\r\n          }).catch(error => {\r\n            console.error('更新建议失败:', error);\r\n            this.$modal.msgError(\"修改失败，请稍后重试\");\r\n          }).finally(() => {\r\n            this.loading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal.confirm('确定删除本条建议？').then(function() {\r\n        return delAdvice(ids);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('renda/advice/export', {\r\n        ...this.queryParams\r\n      }, `advice_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 查看详情 */\r\n    handleDetail(row) {\r\n      getAdvice(row.id).then(response => {\r\n        this.adviceForm = response.data;\r\n        this.srcList = [];\r\n        this.adviceForm.attachmentList.forEach(item => {\r\n          item.fileUrl = this.baseURL + item.fileUrl\r\n          if (item.fileType === 1) {\r\n            this.srcList.push(item.fileUrl)\r\n          }\r\n        });\r\n        this.adviceForm.feedbackList.forEach(fbitem => {\r\n          fbitem.attachmentList.forEach(fbitemImg =>{\r\n            fbitemImg.fileUrl = this.baseURL + fbitemImg.fileUrl\r\n          })\r\n        })\r\n        this.adviceForm.attachmentList.forEach(item => {\r\n          if (item.fileType === 2) {\r\n            // 文件\r\n            // 获取文件扩展名\r\n            const ext = item.fileUrl.substring(item.fileUrl.lastIndexOf('.') + 1).toLowerCase();\r\n            if (ext === 'pdf') {\r\n              item.icon = require('@/assets/images/screen/filetype/pdf.png');\r\n            } else if (ext === 'doc' || ext === 'docx') {\r\n              item.icon = require('@/assets/images/screen/filetype/doc.png');\r\n            } else if (ext === 'xls' || ext === 'xlsx') {\r\n              item.icon = require('@/assets/images/screen/filetype/xls.png');\r\n            } else if (ext === 'ppt' || ext === 'pptx') {\r\n              item.icon = require('@/assets/images/screen/filetype/ppt.png');\r\n            } else {\r\n              item.icon = require('@/assets/images/screen/filetype/unknow.png');\r\n            }\r\n          }\r\n        });\r\n        this.adviceForm.feedbackList.forEach(item => {\r\n          item.attachmentList.forEach(feedbackItem => {\r\n            if (feedbackItem.fileType === 2) {\r\n              // 文件\r\n              // 获取文件扩展名\r\n              const ext = feedbackItem.fileUrl.substring(feedbackItem.fileUrl.lastIndexOf('.') + 1).toLowerCase();\r\n              if (ext === 'pdf') {\r\n                feedbackItem.icon = require('@/assets/images/screen/filetype/pdf.png');\r\n              } else if (ext === 'doc' || ext === 'docx') {\r\n                feedbackItem.icon = require('@/assets/images/screen/filetype/doc.png');\r\n              } else if (ext === 'xls' || ext === 'xlsx') {\r\n                feedbackItem.icon = require('@/assets/images/screen/filetype/xls.png');\r\n              } else if (ext === 'ppt' || ext === 'pptx') {\r\n                feedbackItem.icon = require('@/assets/images/screen/filetype/ppt.png');\r\n              } else {\r\n                feedbackItem.icon = require('@/assets/images/screen/filetype/unknow.png');\r\n              }\r\n            }\r\n          });\r\n        });\r\n        this.showAdviceDetail = true;\r\n      });\r\n    },\r\n    // 大图预览，实现点击当前图片显示当前图片大图，可以随机切换到其他图片进行展示\r\n    getPreviewImgList:function(attachmentList, index) {\r\n      let arr = []\r\n      attachmentList.forEach(item => {\r\n        if (item.fileType == 1) {\r\n          arr.push(item.fileUrl)\r\n        }\r\n      })\r\n      return arr;\r\n    },\r\n    /** 编辑反馈按钮操作 */\r\n    handleEditFeedback(feedback) {\r\n      this.resetFeedback();\r\n      this.loading = true;\r\n      getFeedback(feedback.id).then(response => {\r\n        this.feedbackForm = response.data;\r\n        this.feedbackEditOpen = true;\r\n      }).catch(error => {\r\n        console.error('获取反馈详情失败:', error);\r\n        this.$modal.msgError(\"获取反馈详情失败，请稍后重试\");\r\n      }).finally(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 反馈表单重置\r\n    resetFeedback() {\r\n      this.feedbackForm = {\r\n        id: null,\r\n        content: null,\r\n        createTime: null,\r\n        serviceRating: null,\r\n      };\r\n      this.resetForm(\"feedbackEditForm\");\r\n    },\r\n    // 取消反馈编辑\r\n    cancelFeedback() {\r\n      this.feedbackEditOpen = false;\r\n      this.resetFeedback();\r\n    },\r\n    /** 提交反馈编辑表单 */\r\n    submitFeedbackForm() {\r\n      this.$refs[\"feedbackEditForm\"].validate(valid => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          updateFeedback(this.feedbackForm).then(response => {\r\n            this.$modal.msgSuccess(\"反馈修改成功\");\r\n            this.feedbackEditOpen = false;\r\n            // 重新加载建议详情以刷新反馈列表\r\n            if (this.showAdviceDetail && this.adviceForm.id) {\r\n              this.handleDetail({id: this.adviceForm.id});\r\n            }\r\n          }).catch(error => {\r\n            console.error('更新反馈失败:', error);\r\n            this.$modal.msgError(\"反馈修改失败，请稍后重试\");\r\n          }).finally(() => {\r\n            this.loading = false;\r\n          });\r\n        }\r\n      });\r\n    },\r\n    // 自定义验证方法\r\n    validateTitle(rule, value, callback) {\r\n      if (value && value.trim().length === 0) {\r\n        callback(new Error('建议标题不能为空白字符'));\r\n      } else {\r\n        callback();\r\n      }\r\n    },\r\n    validateContent(rule, value, callback) {\r\n      if (value && value.trim().length === 0) {\r\n        callback(new Error('建议内容不能为空白字符'));\r\n      } else {\r\n        callback();\r\n      }\r\n    },\r\n    validateCreateTime(rule, value, callback) {\r\n      if (value) {\r\n        const now = new Date();\r\n        const selectedTime = new Date(value);\r\n        if (selectedTime > now) {\r\n          callback(new Error('建议时间不能晚于当前时间'));\r\n        } else {\r\n          callback();\r\n        }\r\n      } else {\r\n        callback();\r\n      }\r\n    },\r\n    validateFeedbackContent(rule, value, callback) {\r\n      if (value && value.trim().length === 0) {\r\n        callback(new Error('反馈内容不能为空白字符'));\r\n      } else {\r\n        callback();\r\n      }\r\n    },\r\n    validateFeedbackTime(rule, value, callback) {\r\n      if (value) {\r\n        const now = new Date();\r\n        const selectedTime = new Date(value);\r\n        if (selectedTime > now) {\r\n          callback(new Error('反馈时间不能晚于当前时间'));\r\n        } else {\r\n          callback();\r\n        }\r\n      } else {\r\n        callback();\r\n      }\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .img-container {\r\n    display: flex;\r\n    flex-direction: row;\r\n    flex-wrap: wrap;\r\n  }\r\n  .img-item {\r\n    width: 200px;\r\n    height: 150px;\r\n    margin: 12px;\r\n  }\r\n  .item {\r\n    display: flex;\r\n    flex-direction: row;\r\n    .label {\r\n      width: 80px;\r\n      line-height: 36px;\r\n      text-align: right;\r\n      font-size: 14px;\r\n      color: #606266;\r\n      padding: 0 12px 0 0;\r\n      font-weight: 700;\r\n    }\r\n    .content {\r\n      flex: 1;\r\n      margin-top: 6px;\r\n    }\r\n  }\r\n  .step-label {\r\n    font-size: 14px;\r\n    color: #606266;\r\n  }\r\n  .step-desc {\r\n    font-size: 14px;\r\n    color: #606266;\r\n  }\r\n  .feedback-content {\r\n    margin: 10px 0;\r\n  }\r\n</style>\r\n"]}]}