import request from '@/utils/request'

// 查询制度文件列表
export function listRendafile(query) {
  return request({
    url: '/renda/rendafile/list',
    method: 'get',
    params: query
  })
}

// 查询制度文件详细
export function getRendafile(id) {
  return request({
    url: '/renda/rendafile/' + id,
    method: 'get'
  })
}

// 新增制度文件
export function addRendafile(data) {
  return request({
    url: '/renda/rendafile',
    method: 'post',
    data: data
  })
}

// 修改制度文件
export function updateRendafile(data) {
  return request({
    url: '/renda/rendafile',
    method: 'put',
    data: data
  })
}

// 删除制度文件
export function delRendafile(id) {
  return request({
    url: '/renda/rendafile/' + id,
    method: 'delete'
  })
}

// 删除已上传文件
export function deleteUploadedFile(data) {
  return request({
    url: '/renda/rendafile/deleteUploadedFile',
    method: 'post',
    data: data
  })
}
