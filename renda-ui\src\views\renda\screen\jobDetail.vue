<template>
  <div class="app-container bg">
    <ScreenHeader ref="headerComp" />
    <div class="my-screen">
      <ScreenTitle caption="代表履职信息" more="" />
      <div class="deputy-info-content">
        <div class="top-info">
          <el-image class="img" :src="baseURL + deputy.avatar" fit="cover"></el-image>
          <div class="right-info">
            <div class="base-info">
              <div class="name">{{deputy.name}}</div>
              <div class="duty">{{deputy.duty}}</div>
              <div class="info">
                <div class="info-1">「民族」<dict-tag :options="dict.type.yw_mz" :value="deputy.nation"/></div>
                <div class="info-2">「出生日期」{{ formatYearMonth(deputy.birthday) }}</div>
              </div>
              <div class="info">
                <div class="info-1">「电话」{{deputy.tel}}</div>
              </div>
            </div>
            <el-image class="code" :src="baseURL + deputy.qrcodeUrl" fit="contain"></el-image>
          </div>
        </div>
        <div class="job-container">
          <div class="job-title">{{job.title}}</div>
          <div class="job-desc">时间：{{formatReadableDate(job.beginDate)}}<span class="space" />来源：{{job.organizer}}</div>
          <div class="content" ref="content"></div>
          <div class="filelist" v-if="files.length > 0">
            <div class="attach-title">附件：</div>
            <div v-for="(item) in files">
              <div class="file">
                <el-image class="filetype" :src="item.icon" fit="contain"></el-image>
                <div class="filename" @click="onPreview(item)">{{item.fileName}}</div>
              </div>
            </div>
          </div>
          <div v-if="pics.length > 0" class="imglist">
            <div v-for="(item) in pics">
              <el-image :class="pics.length === 1 ? 'oneImg' : pics.length === 2 ? 'twoImg' : 'moreImg'"
                        :src="baseURL + item.fileUrl" fit="contain"></el-image>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-image class="return-btn" :src="require('@/assets/images/screen/return.png')" @click="onBack"></el-image>

    <!-- 预览代表建议对话框 -->
    <el-dialog :visible.sync="openPreview" modal width="1240px" height="1640px" top="300px!important">
      <div slot="title" style="font-family: AL-R; font-size: 34px; text-align: center;">{{fileTitle}}</div>
      <iframe
        v-if="previewUrl"
        :src="previewUrl"
        width="1200px"
        height="1500px"
        frameborder="0"
        scrolling="yes" />
    </el-dialog>

  </div>
</template>

<script>

import ScreenHeader from './components/screenHeader';
import ScreenTitle from './components/screenTitle';
import { formatYearMonth, formatReadableDate } from '@/api/renda/utils';
import { getDeputy, getJobExt } from '@/api/renda/screen';
import { Base64 } from 'js-base64'

export default {
  name: "JobDetail",
  components: { ScreenHeader, ScreenTitle },
  dicts: ['yw_mz', 'job_type'],
  data() {
    return {
      defaultAvatar: require("@/assets/images/avatar.png"), // 默认头像
      baseURL: process.env.VUE_APP_BASE_API, // 图片上传地址头
      deputyId: 0, // 人大代表id
      deputy: {}, // 人大代表信息
      jobId: 0, // 履职工作id
      job: {}, // 履职工作信息
      pics: [], // 图片列表
      files: [], // 文件列表
      openPreview: false, // 是否打开预览对话框
      fileTitle: '', // 附件标题
      previewUrl: '', // 预览地址
    };
  },
  activated() {
    this.deputyId = this.$route.query.deputyId;
    this.jobId = this.$route.query.jobId;
    this.loadData();
  },
  mounted() {
    this.$nextTick(() => {
      document.oncontextmenu = function() {
        return false;
      };
    });
  },
  methods: {
    loadData() {
      getDeputy(this.deputyId).then(res => {
        this.deputy = res.data;
      });
      getJobExt(this.jobId).then(res => {
        this.job = res.data;
        this.pics = [];
        this.files = [];
        this.job.attachments.forEach(item => {
          if (item.fileType === 1) {
            // 图片
            this.pics.push(item);
          } else {
            // 文件
            // 获取文件扩展名
            const ext = item.fileUrl.substring(item.fileUrl.lastIndexOf('.') + 1).toLowerCase();
            if (ext === 'pdf') {
              item.icon = require('@/assets/images/screen/filetype/pdf.png');
            } else if (ext === 'doc' || ext === 'docx') {
              item.icon = require('@/assets/images/screen/filetype/doc.png');
            } else if (ext === 'xls' || ext === 'xlsx') {
              item.icon = require('@/assets/images/screen/filetype/xls.png');
            } else if (ext === 'ppt' || ext === 'pptx') {
              item.icon = require('@/assets/images/screen/filetype/ppt.png');
            } else {
              item.icon = require('@/assets/images/screen/filetype/unknow.png');
            }
            this.files.push(item);
          }
        });
        this.$refs.content.innerText = this.job.content;
      });
    },
    formatYearMonth(date) {
      return formatYearMonth(date)
    },
    formatReadableDate(date) {
      return formatReadableDate(date)
    },
    onBack() {
      this.$refs.headerComp.onClick();
    },
    onPreview(item) {
      const fileUrl = item.fileUrl;
      if (fileUrl) {
        let file = 'https://rd.juruifeng.cn:9000/prod-api' + fileUrl; //要预览文件的访问地址
        let url = 'https://rd.juruifeng.cn:8888/onlinePreview?url=' + encodeURIComponent(Base64.encode(file));
        // let file = 'http://localhost:9000/dev-api' + fileUrl; //要预览文件的访问地址
        // let url = 'http://localhost:8888/onlinePreview?url='+encodeURIComponent(Base64.encode(file));
        this.previewUrl = url;
        this.fileTitle = item.fileName;
        this.openPreview = true;
      }
    },
  }
};
</script>

<style lang="scss" scoped>

@import '../../../common/font/font.css';

.my-screen {
  margin: 250px 850px 100px 850px;
  padding: 50px 50px;
  height: 1826px;
  display: flex;
  flex-direction: column;
  //background-color: #fff;

  .deputy-info-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background: #FFF6EF;
    margin-top: 17px;
    overflow: hidden;
    padding: 0 64px;
    .top-info {
      display: flex;
      flex-direction: row;
      margin: 32px;
      .img {
        width: 225px;
        height: 300px;
        border: #ffffff solid 10px;
        box-shadow: 0px 0px 12px 0px rgba(0,0,0,0.1);
        flex-shrink: 0;
      }
      .right-info {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
        padding: 10px 30px;
        .base-info {
          display: flex;
          flex-direction: column;
          width: 100%;
          text-align: left;
          .name {
            font-size: 48px;
            font-family: AL-B;
            font-weight: bold;
            color: #000000;
          }
          .duty {
            margin: 10px 0 30px 0;
            font-size: 26px;
            font-family: AL-R;
            font-weight: 300;
            color: #504B4A;
          }
          .info {
            margin: 10px 0;
            display: flex;
            flex-direction: row;
            font-size: 26px;
            font-family: AL-R;
            font-weight: 400;
            color: #504B4A;
            .info-1 {
              margin-left: 0;
              display: flex;
            }
            .info-2 {
              margin-left: 200px;
            }
          }
        }
        .code {
          width: 200px;
          height: 200px;
        }
      }
    }
    .job-container {
      display: flex;
      flex-direction: column;
      height: 1200px;
      overflow: scroll;
      background-color: #fff;
      box-shadow: 0px 0px 2px 0px rgba(0,0,0,0.1);
      margin: 0 30px;
      padding: 30px 60px;
      .job-title {
        margin: 48px;
        font-family: AL-B;
        font-size: 44px;
        text-align: center;
      }
      .job-desc {
        margin: 24px;
        font-family: AL-L;
        font-size: 26px;
        text-align: center;
        color: #504B4A;
      }
      .content {
        display: flex;
        //flex-grow: 1;
        margin: 40px 90px;
        box-sizing: border-box;
        font-size: 28px;
        font-family: AL-R;
        color: #504B4A;
        border: none;
        outline: none;
        resize: none;
      }
      .attach-title {
        margin: 10px 90px;
        font-size: 28px;
        font-family: AL-R;
        color: #504B4A;
      }
      .file {
        display: flex;
        flex-direction: row;
        height: 60px;
        margin: 0 90px;
        .filetype {
          width: 50px;
          height: 50px;
        }
        .filename {
          margin-left: 20px;
          line-height: 50px;
          font-size: 28px;
          font-family: AL-L;
          color: #504B4A;
          text-decoration: underline;
          cursor: pointer;
        }
      }
      .imglist {
        width: 100%;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        margin: 30px 0;
        justify-content: space-around;
        .oneImg {
          width: 1000px;
          max-height: 1200px;
          border-radius: 12px;
        }
        .twoImg {
          width: 700px;
          max-height: 1200px;
          border-radius: 12px;
        }
        .moreImg {
          width: 500px;
          max-height: 1200px;
          border-radius: 12px;
        }
      }
    }
  }
}

</style>
