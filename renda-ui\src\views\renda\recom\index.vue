<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="建议标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入建议标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="建议类型" prop="recomType">
        <el-select v-model="queryParams.recomType" placeholder="请选择建议类型" clearable>
          <el-option
            v-for="dict in dict.type.rd_recom_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="届" prop="session">
        <el-select v-model="queryParams.session" placeholder="" clearable>
          <el-option
            v-for="dict in dict.type.rd_session"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="次" prop="times">
        <el-select v-model="queryParams.times" placeholder="" clearable>
          <el-option
            v-for="dict in dict.type.rd_times"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['renda:recom:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['renda:recom:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['renda:recom:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['renda:recom:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="recomList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="届" align="center" prop="session" width="80">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.rd_session" :value="scope.row.session"/>
        </template>
      </el-table-column>
      <el-table-column label="次" align="center" prop="times" width="80">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.rd_times" :value="scope.row.times"/>
        </template>
      </el-table-column>
      <el-table-column label="建议类型" align="center" prop="recomType" width="80">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.rd_recom_type" :value="scope.row.recomType"/>
        </template>
      </el-table-column>
      <el-table-column label="建议标题" align="center" prop="title" min-width="200" />
      <el-table-column label="主办" align="center" prop="host" width="200" />
      <el-table-column label="协办" align="center" prop="cohost" width="150" />
      <el-table-column label="更新者" align="center" prop="updateBy" width="100" />
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['renda:recom:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['renda:recom:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改代表建议对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="890px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-position="top" class="recom-form">
        <el-row>
          <el-col style="width: 320px;">
            <el-row>
              <el-form-item label="建议标题" prop="title">
                <el-input v-model="form.title" placeholder="请输入建议标题" />
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item label="主办" prop="host">
                <el-select
                  v-model="form.host"
                  filterable
                  remote
                  allow-create
                  placeholder="请输入主办单位"
                  :remote-method="getHostOptions"
                  :loading="loadingHost">
                  <el-option
                    v-for="item in hostOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item label="协办" prop="cohost">
                <el-select
                  v-model="form.cohost"
                  filterable
                  remote
                  allow-create
                  placeholder="请输入协办单位"
                  :remote-method="getHostOptions"
                  :loading="loadingHost">
                  <el-option
                    v-for="item in hostOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item label="建议类型" prop="recomType">
                  <el-select v-model="form.recomType" placeholder="建议类型">
                    <el-option
                      v-for="dict in dict.type.rd_recom_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="届" prop="session">
                  <el-select v-model="form.session" placeholder="届">
                    <el-option
                      v-for="dict in dict.type.rd_session"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="次" prop="times">
                  <el-select v-model="form.times" placeholder="次">
                    <el-option
                      v-for="dict in dict.type.rd_times"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-form-item label="人大代表">
                <div slot="label" style="display: flex; margin-top: 12px; align-items: center;">
                  <div style="margin-right: 12px;">人大代表</div>
                  <el-button
                    style="height: 28px !important;"
                    size="mini"
                    type="primary"
                    icon="el-icon-plus"
                    @click="handleShowDeputyDialog()"
                  >添加</el-button>
                </div>
                <el-table :data="form.deputyList" size="mini" max-height="172">
                  <el-table-column label="序号" width="80" align="center">
                    <template slot-scope="scope">
                      {{ scope.$index + 1 }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="deputyName" label="姓名" align="center"></el-table-column>
                  <el-table-column label="操作" width="80" align="center">
                    <template slot-scope="scope">
                      <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        @click="handleDeleteDeputy(scope.row)"
                      >删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item label="建议附件">
                <el-upload
                  :headers="upload.headers"
                  :action="upload.url"
                  :on-preview="handlePreview"
                  :on-remove="handleRemove"
                  :on-success="handleAttachUploadSuccess"
                  multiple
                  :limit="attachLimit"
                  :on-exceed="handleExceed"
                  :file-list="form.attachmentList">
                  <el-button size="small" type="primary">点击上传</el-button>
                </el-upload>
              </el-form-item>
            </el-row>
          </el-col>
          <el-col style="width: 320px;">
            <el-row>
              <el-form-item>
                <el-upload
                  slot="label"
                  style="margin: 12px 24px;"
                  :headers="upload.headers"
                  :action="upload.url"
                  :show-file-list="false"
                  :disabled="upload.isUploading"
                  :on-progress="handleFileUploadProgress"
                  :multiple="false"
                  :on-success="handleUploadSuccess">
                  <el-button size="small" type="primary" icon="el-icon-upload2">上传正文</el-button>
                </el-upload>
                <div style="display: flex; align-items: center; justify-content: center; width: 500px; height: 600px; margin-left: 24px; border: #cdcdcd solid 1px;">
                  <el-empty description="未上传正文" v-if="!previewUrl"></el-empty>
                  <iframe
                    v-if="previewUrl"
                    :src="previewUrl"
                    width="100%"
                    height="100%"
                    frameborder="0"
                    scrolling="yes" />
                </div>
              </el-form-item>
            </el-row>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加代表对话框 -->
    <el-dialog title="选择人大代表" :visible.sync="openDeputyDialog" width="1200px" append-to-body>
      <!-- 查询条件 -->
      <el-form :model="queryDeputyParams" ref="queryDeputyForm" size="small" :inline="true" label-width="68px">
        <el-form-item label="代表类型" prop="type">
          <el-select v-model="queryDeputyParams.type" placeholder="请选择" clearable filterable>
            <el-option
              v-for="dict in dict.type.rd_deputy_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input
            v-model="queryDeputyParams.name"
            placeholder="请输入姓名"
            clearable
            @keyup.enter.native="handleQueryDeputy"
          />
        </el-form-item>
        <el-form-item label="民族" prop="nation">
          <el-select v-model="queryDeputyParams.nation" placeholder="请选择" clearable filterable>
            <el-option
              v-for="dict in dict.type.yw_mz"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="职务" prop="duty">
          <el-input
            v-model="queryDeputyParams.duty"
            placeholder="请输入职务"
            clearable
            @keyup.enter.native="handleQueryDeputy"
          />
        </el-form-item>
        <el-form-item label="电话" prop="phone">
          <el-input
            v-model="queryDeputyParams.phone"
            placeholder="请输入电话"
            clearable
            @keyup.enter.native="handleQueryDeputy"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQueryDeputy">查询</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQueryDeputy">重置</el-button>
        </el-form-item>
      </el-form>
      <!-- 代表列表 -->
      <el-table ref="deputyTable" v-loading="loadingDeputy" :data="deputyList" size="mini" @selection-change="handleDeputySelectionChange">
        <el-table-column label="序号" width="80" align="center">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="所属部门" align="center" prop="deptName" width="110" />
        <el-table-column label="代表类型" align="center" prop="type" width="80">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.rd_deputy_type" :value="scope.row.type"/>
          </template>
        </el-table-column>
<!--        <el-table-column label="头像" align="center" prop="avatar" width="80">-->
<!--          <template slot-scope="scope">-->
<!--            <el-image :src="scope.row.avatar ? baseURL + scope.row.avatar : defaultAvatar" fit="contain" />-->
<!--          </template>-->
<!--        </el-table-column>-->
<!--        <el-table-column label="二维码" align="center" prop="avatar" width="80">-->
<!--          <template slot-scope="scope">-->
<!--            <el-image :src="baseURL + scope.row.qrcodeUrl" fit="contain" />-->
<!--          </template>-->
<!--        </el-table-column>-->
        <el-table-column label="姓名" align="center" prop="name" />
        <el-table-column label="民族" align="center" prop="nation" width="80">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.yw_mz" :value="scope.row.nation"/>
          </template>
        </el-table-column>
        <el-table-column label="职务" align="center" prop="duty" width="200px" />
        <el-table-column label="出生日期" align="center" prop="birthday" width="100">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.birthday, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="电话" align="center" prop="phone" width="110px" />
      </el-table>
      <pagination
        v-show="totalDeputy>0"
        :total="totalDeputy"
        :page.sync="queryDeputyParams.pageNum"
        :limit.sync="queryDeputyParams.pageSize"
        @pagination="getDeputyList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleAddDeputy">确 定</el-button>
        <el-button @click="cancelAddDeputy">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listRecom,
  getRecom,
  delRecom,
  addRecom,
  updateRecom,
  deleteUploadedFile,
  getHostOptions
} from '@/api/renda/recom'
import { getToken } from '@/utils/auth'
import { Base64 } from 'js-base64'
import { listDeputy } from '@/api/renda/deputy'

export default {
  name: "Recom",
  dicts: ['rd_recom_type', 'rd_session', 'rd_times', 'rd_deputy_type', 'yw_mz'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 代表建议表格数据
      recomList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        recomType: null,
        session: null,
        times: null,
      },
      // 表单参数
      form: { },
      // 表单校验
      rules: {
        title: [
          { required: true, message: "建议标题不能为空", trigger: "blur" }
        ],
        recomType: [
          { required: true, message: "建议类型不能为空", trigger: "change" }
        ],
        session: [
          { required: true, message: "届不能为空", trigger: "change" }
        ],
        times: [
          { required: true, message: "次不能为空", trigger: "change" }
        ],
      },
      previewUrl: '', // 预览正文url
      // 上传文件参数
      upload: {
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/renda/recom/uploadFile",
        // 是否禁用上传
        isUploading: false,
      },
      attachLimit: 10, // 附件上传数量限制
      // 显示人大代表筛选对话框
      openDeputyDialog: false,
      // 查询人大代表参数
      queryDeputyParams: {
        pageNum: 1,
        pageSize: 10,
        type: null,
        name: null,
        nation: null,
        company: null,
        tel: null,
        duty: null,
        phone: null,
        deptId: null,
      },
      loadingDeputy: false, // 人大代表列表加载状态
      deputyList: [], // 人大代表列表
      deputyIds: [], // 选中的人大代表id
      totalDeputy: 0, // 人大代表总数
      baseURL: process.env.VUE_APP_BASE_API, // 图片上传地址头
      fileList: [], // 上传的文件列表
      hostOptions: [], // 主办协办列表
      loadingHost: false, // 主办协办列表加载状态
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询代表建议列表 */
    getList() {
      this.loading = true;
      listRecom(this.queryParams).then(response => {
        this.recomList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        title: null,
        recomType: null,
        session: null,
        times: null,
        host: null,
        cohost: null,
        recomFileUrl: null,
        deputyList: [],
        attachmentList: [],
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.previewUrl = "";
      this.open = true;
      this.title = "添加代表建议";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getRecom(id).then(response => {
        this.form = response.data;
        this.previewUrl = "";
        this.open = true;
        this.title = "修改代表建议";
        this.$nextTick(() => {
          this.previewFile(this.form.recomFileUrl, 'this');
        });
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateRecom(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRecom(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除代表建议编号为"' + ids + '"的数据项？').then(function() {
        return delRecom(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('renda/recom/export', {
        ...this.queryParams
      }, `recom_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleUploadSuccess(response, file, fileList) {
      this.upload.isUploading = false;
      if (response.code == 200) {
        this.form.recomFileUrl = response.msg
        this.previewFile(this.form.recomFileUrl, 'this');
      } else {
        this.$modal.msgError("上传失败");
      }
    },
    previewFile(fileUrl, type) {
      let file = 'https://rd.juruifeng.cn:9000/prod-api' + fileUrl; //要预览文件的访问地址
      let url = 'https://rd.juruifeng.cn:8888/onlinePreview?url='+encodeURIComponent(Base64.encode(file));
      if (type == 'this') {
        if (fileUrl && fileUrl.length > 0) {
          this.previewUrl = url;
        } else {
          this.previewUrl = "";
        }
      } else {
        if (fileUrl && fileUrl.length > 0) {
          window.open(url);
        }
      }
    },
    //
    handleShowDeputyDialog() {
      this.openDeputyDialog = true;
      this.$nextTick(() => {
        this.$refs.deputyTable.clearSelection();
      })
    },
    // 查询人大代表
    getDeputyList() {
      this.loadingDeputy = true;
      listDeputy(this.queryDeputyParams).then(response => {
        this.deputyList = response.rows;
        this.totalDeputy = response.total;
        this.loadingDeputy = false;
      });
    },
    /** 搜索按钮操作 */
    handleQueryDeputy() {
      this.queryDeputyParams.pageNum = 1;
      this.getDeputyList();
    },
    /** 重置按钮操作 */
    resetQueryDeputy() {
      this.deputyIds = [];
      this.resetForm("queryDeputyForm");
      this.handleQueryDeputy();
    },
    // 多选框选中数据
    handleDeputySelectionChange(selection) {
      this.deputyIds = selection.map(item => ({deputyId: item.id, deputyName: item.name}))
    },
    handleAddDeputy() {
      this.deputyIds.forEach(item => {
        let index = this.form.deputyList.findIndex(obj => obj.deputyId === item.deputyId); // 获取id为3的对象的索引
        if (index === -1) { // 如果该对象存在于数组中
          this.form.deputyList.push(item); // 使用splice方法删除对象，第一个参数是对象的索引，第二个参数是要删除的对象的数量
        }
      })
      this.openDeputyDialog = false;
    },
    cancelAddDeputy() {
      this.openDeputyDialog = false;
    },
    handleDeleteDeputy(row) {
      let index = this.form.deputyList.findIndex(obj => obj.deputyId === row.deputyId); // 获取id为3的对象的索引
      if (index !== -1) { // 如果该对象存在于数组中
        this.form.deputyList.splice(index, 1); // 使用splice方法删除对象，第一个参数是对象的索引，第二个参数是要删除的对象的数量
      }
    },
    handlePreview(file) {
      this.previewFile(file.fileUrl, 'newWindow');
    },
    handleRemove(file) {
      deleteUploadedFile(file.fileUrl).then(response => {
        if (response.code == 200) {
          let index = this.form.attachmentList.findIndex(item => item.fileUrl === file.fileUrl); // 获取id为3的对象的索引
          if (index !== -1) { // 如果该对象存在于数组中
            this.form.attachmentList.splice(index, 1); // 使用splice方法删除对象，第一个参数是对象的索引，第二个参数是要删除的对象的数量
          }
          this.$modal.msgSuccess("删除成功");
        } else {
          this.$modal.msgError("删除失败");
        }
      })
    },
    // 文件个数超出
    handleExceed() {
      this.$modal.msgError(`上传附件数量不能超过 ${this.attachLimit} 个!`);
    },
    // 文件上传成功处理
    handleAttachUploadSuccess(response, file, fileList) {
      if (response.code == 200) {
        this.form.attachmentList.push({
          name: file.name,
          fileType: 2,
          fileUrl: response.msg
        })
      } else {
        this.$modal.msgError("上传失败");
      }
    },
    getHostOptions(query) {
      getHostOptions(
        {
          keyword: query,
        }
      ).then(response => {
        this.hostOptions = [];
        response.rows.forEach(item => {
          this.hostOptions.push({
            value: item,
            label: item
          })
        });
      })
    },
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.recom-form .el-form-item {
  //margin: 8px 0 0 0;
}
.recom-form .el-form-item__label {
  margin-top: 0;
  padding: 0 !important;
  //line-height: 24px;
}
</style>
