package com.renda.core.service.impl;

import java.util.List;
import com.renda.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.renda.core.mapper.RdStationNewsMapper;
import com.renda.core.domain.RdStationNews;
import com.renda.core.service.IRdStationNewsService;

/**
 * 联络站动态Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Service
public class RdStationNewsServiceImpl implements IRdStationNewsService
{
    @Autowired
    private RdStationNewsMapper rdStationNewsMapper;

    /**
     * 查询联络站动态
     *
     * @param id 联络站动态主键
     * @return 联络站动态
     */
    @Override
    public RdStationNews selectRdStationNewsById(Long id)
    {
        return rdStationNewsMapper.selectRdStationNewsById(id);
    }

    /**
     * 查询联络站动态列表
     *
     * @param rdStationNews 联络站动态
     * @return 联络站动态
     */
    @Override
    public List<RdStationNews> selectRdStationNewsList(RdStationNews rdStationNews)
    {
        return rdStationNewsMapper.selectRdStationNewsList(rdStationNews);
    }

    /**
     * 新增联络站动态
     *
     * @param rdStationNews 联络站动态
     * @return 结果
     */
    @Override
    public int insertRdStationNews(RdStationNews rdStationNews)
    {
        rdStationNews.setCreateTime(DateUtils.getNowDate());
        return rdStationNewsMapper.insertRdStationNews(rdStationNews);
    }

    /**
     * 修改联络站动态
     *
     * @param rdStationNews 联络站动态
     * @return 结果
     */
    @Override
    public int updateRdStationNews(RdStationNews rdStationNews)
    {
        rdStationNews.setUpdateTime(DateUtils.getNowDate());
        return rdStationNewsMapper.updateRdStationNews(rdStationNews);
    }

    /**
     * 批量删除联络站动态
     *
     * @param ids 需要删除的联络站动态主键
     * @return 结果
     */
    @Override
    public int deleteRdStationNewsByIds(Long[] ids)
    {
        return rdStationNewsMapper.deleteRdStationNewsByIds(ids);
    }

    /**
     * 删除联络站动态信息
     *
     * @param id 联络站动态主键
     * @return 结果
     */
    @Override
    public int deleteRdStationNewsById(Long id)
    {
        return rdStationNewsMapper.deleteRdStationNewsById(id);
    }
}
