<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renda.core.mapper.RdJobMapper">

    <resultMap type="RdJob" id="RdJobResult">
        <result property="id"    column="id"    />
        <result property="deputyId"    column="deputy_id"    />
        <result property="jobType"    column="job_type"    />
        <result property="organizer"    column="organizer"    />
        <result property="beginDate"    column="begin_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="createTime"    column="create_time"    />
        <result property="deputyName"    column="deputyName"    />
        <result property="deputyAvatar"    column="deputyAvatar"    />
    </resultMap>

    <resultMap type="RdJob" id="JobExtResult">
        <result property="id"    column="id"    />
        <result property="deputyId"    column="deputy_id"    />
        <result property="jobType"    column="job_type"    />
        <result property="jobName"    column="job_name"    />
        <result property="organizer"    column="organizer"    />
        <result property="beginDate"    column="begin_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="createTime"    column="create_time"    />
        <result property="deputyName"    column="deputyName"    />
        <result property="deputyAvatar"    column="deputyAvatar"    />
        <collection property="attachments" javaType="List" ofType="com.renda.core.domain.RdJobAttachment"
                    column="id" select="selectAttachmentsById"  />
    </resultMap>

    <resultMap type="RdJobAttachment" id="RdJobAttachmentResult">
        <result property="id"    column="id"    />
        <result property="jobId"    column="job_id"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileUrl"    column="file_url"    />
    </resultMap>

    <sql id="selectRdJobVo">
        select j.id, j.deputy_id, j.job_type, j.organizer, j.begin_date, j.end_date, j.title, j.content, j.create_time,
               (select name from rd_deputy where id = j.deputy_id) as deputyName,
               (select avatar from rd_deputy where id = j.deputy_id) as deputyAvatar,
               (SELECT dict_label FROM sys_dict_data where dict_type = 'job_type' and dict_value = j.job_type) as job_name
        from rd_job j
    </sql>

    <select id="selectAttachmentsById" resultMap="RdJobAttachmentResult">
        select id, job_id, file_type, file_name, file_url
        from rd_job_attachment
        where job_id = #{id}
    </select>

    <select id="selectRdJobList" parameterType="RdJob" resultMap="JobExtResult">
        <include refid="selectRdJobVo"/>
        <where>
            <if test="deputyId != null "> and deputy_id = #{deputyId}</if>
            <if test="jobType != null "> and job_type = #{jobType}</if>
            <if test="beginDate != null"><!-- 开始时间检索 -->
                and begin_date &gt;= str_to_date(#{beginDate}, '%Y-%m-%d')
            </if>
            <if test="endDate != null"><!-- 结束时间检索 -->
                and end_date &lt;= str_to_date(#{endDate}, '%Y-%m-%d')
            </if>
            <if test="title != null  and title != ''">
                and (
                    title like concat('%', #{title}, '%')
                    or content like concat('%', #{title}, '%')
                    or organizer like concat('%', #{title}, '%')
                    or (select name from rd_deputy where id = j.deputy_id) like concat('%', #{title}, '%')
                )
            </if>
        </where>
        order by j.id desc
    </select>

    <select id="selectRdJobById" parameterType="Long" resultMap="RdJobResult">
        <include refid="selectRdJobVo"/>
        where j.id = #{id}
    </select>

    <select id="selectJobExtById" parameterType="Long" resultMap="JobExtResult">
        select j.id, j.deputy_id, j.job_type, j.organizer, j.begin_date, j.end_date, j.title, j.content, j.create_time,
               (select name from rd_deputy where id = j.deputy_id) as deputyName,
               (select avatar from rd_deputy where id = j.deputy_id) as deputyAvatar
        from rd_job j
        where j.id = #{id}
    </select>

    <select id="getJobStat" resultType="com.renda.core.domain.vo.JobStatVO">
        select d.dict_value as jobType, substring(d.remark, 5) as icon, substring(d.remark, 1, 4) as title,
               d.dict_label as `desc`, count(j.deputy_id) as `count`
        from sys_dict_data d
            left join rd_job j on j.job_type = d.dict_value
        where dict_type = 'job_type'
          and d.`status` = '0'
        group by d.dict_label,
                 d.dict_value,
                 d.remark
        order by d.dict_value
    </select>

    <insert id="insertRdJob" parameterType="RdJob" useGeneratedKeys="true" keyProperty="id">
        insert into rd_job
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deputyId != null">deputy_id,</if>
            <if test="jobType != null and jobType != ''">job_type,</if>
            <if test="organizer != null and organizer != ''">organizer,</if>
            <if test="beginDate != null">begin_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="content != null">content,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deputyId != null">#{deputyId},</if>
            <if test="jobType != null and jobType != ''">#{jobType},</if>
            <if test="organizer != null and organizer != ''">#{organizer},</if>
            <if test="beginDate != null">#{beginDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateRdJob" parameterType="RdJob">
        update rd_job
        <trim prefix="SET" suffixOverrides=",">
            <if test="deputyId != null">deputy_id = #{deputyId},</if>
            <if test="jobType != null and jobType != ''">job_type = #{jobType},</if>
            <if test="organizer != null and organizer != ''">organizer = #{organizer},</if>
            <if test="beginDate != null">begin_date = #{beginDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRdJobById" parameterType="Long">
        delete from rd_job where id = #{id}
    </delete>

    <delete id="deleteRdJobByIds" parameterType="String">
        delete from rd_job where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
