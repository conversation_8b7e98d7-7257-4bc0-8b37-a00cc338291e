package com.renda.core.controller;

import com.renda.common.annotation.Anonymous;
import com.renda.common.annotation.Log;
import com.renda.common.core.controller.BaseController;
import com.renda.common.core.domain.AjaxResult;
import com.renda.common.core.domain.entity.SysDictData;
import com.renda.common.enums.BusinessType;
import com.renda.common.utils.StringUtils;
import com.renda.system.service.ISysDictDataService;
import com.renda.core.service.IRdDeputyService;
import com.renda.core.service.IRdStationService;
import com.renda.core.service.IRdStationStaffService;
import com.renda.core.service.IRdStationScheduleService;
import com.renda.core.service.IRdAdviceService;
import com.renda.core.service.IRdFeedbackService;
import com.renda.core.service.IRdStationPolicyService;
import com.renda.core.service.IRdStationNewsService;
import com.renda.core.domain.RdDeputy;
import com.renda.core.domain.RdStationStaff;
import com.renda.core.domain.RdStationSchedule;
import com.renda.core.domain.RdAdvice;
import com.renda.core.domain.RdStationPolicy;
import com.renda.core.domain.RdStationNews;
import com.renda.core.domain.vo.AdviceExtVO;
import com.renda.core.domain.vo.FeedbackInfoVO;
import com.renda.common.core.domain.entity.RdStation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;
import java.text.SimpleDateFormat;
import java.util.Calendar;

/**
 * 联络站大屏控制器
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/renda/stationScreen")
public class RdStationScreenController extends BaseController
{
    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private IRdDeputyService deputyService;

    @Autowired
    private IRdStationService stationService;

    @Autowired
    private IRdStationStaffService stationStaffService;

    @Autowired
    private IRdStationScheduleService stationScheduleService;

    @Autowired
    private IRdAdviceService adviceService;

    @Autowired
    private IRdFeedbackService feedbackService;

    @Autowired
    private IRdStationPolicyService stationPolicyService;

    @Autowired
    private IRdStationNewsService stationNewsService;

    /**
     * 根据本机ID获取联络站ID
     */
    @Anonymous
    @PostMapping("/getStationByDevice")
    public AjaxResult getStationByDevice(@RequestBody Map<String, String> params)
    {
        String deviceId = params.get("deviceId");
        if (StringUtils.isEmpty(deviceId))
        {
            return error("设备ID不能为空");
        }

        try
        {
            // 创建查询条件
            SysDictData queryData = new SysDictData();
            queryData.setDictType("rd_station_device");
            queryData.setDictLabel(deviceId);
            queryData.setStatus("0"); // 只查询正常状态的数据

            // 查询是否存在该设备的绑定记录
            List<SysDictData> list = dictDataService.selectDictDataList(queryData);

            String stationId = "0"; // 默认值

            if (list != null && !list.isEmpty())
            {
                // 找到记录，返回联络站ID
                stationId = list.get(0).getDictValue();
            }
            else
            {
                // 没有找到记录，新增一条默认记录
                SysDictData newData = new SysDictData();
                newData.setDictType("rd_station_device");
                newData.setDictLabel(deviceId);
                newData.setDictValue("0"); // 默认未指定联络站
                newData.setDictSort(99L);
                newData.setStatus("0");
                newData.setIsDefault("N");
                newData.setRemark("系统自动创建的设备绑定记录");
                newData.setCreateBy("system");

                dictDataService.insertDictData(newData);
                stationId = "0";
            }

            Map<String, Object> result = new HashMap<>();
            result.put("stationId", stationId);
            result.put("deviceId", deviceId);

            return success(result);
        }
        catch (Exception e)
        {
            logger.error("获取联络站ID失败", e);
            return error("获取联络站ID失败：" + e.getMessage());
        }
    }

    /**
     * 根据联络站ID获取统计数据
     */
    @Anonymous
    @GetMapping("/getStationStatistics/{stationId}")
    public AjaxResult getStationStatistics(@PathVariable String stationId)
    {
        if (StringUtils.isEmpty(stationId))
        {
            return error("联络站ID不能为空");
        }

        try
        {
            Long stationIdLong = null;
            if (!"0".equals(stationId)) {
                stationIdLong = Long.parseLong(stationId);
            }

            Map<String, Object> statistics = new HashMap<>();

            // 查询意见建议数据
            RdAdvice queryAdvice = new RdAdvice();
            queryAdvice.setIsStationDeputy("1");
            List<AdviceExtVO> allAdvices = adviceService.selectAdviceExtList(queryAdvice);

            // 基础统计数据
            int totalAdvices = allAdvices.size();
            int processedAdvices = (int) allAdvices.stream().filter(advice -> "2".equals(advice.getStatus())).count();
            int processingAdvices = (int) allAdvices.stream().filter(advice -> "1".equals(advice.getStatus())).count();
            double completionRate = totalAdvices > 0 ? (double) processedAdvices / totalAdvices * 100 : 0;

            // 计算办理满意度（基于群众服务评分）
            double satisfactionRate = calculateSatisfactionRate(allAdvices);

            // 计算趋势数据
            Map<String, Object> trendCalculations = calculateTrends(allAdvices);

            Map<String, Object> basicStats = new HashMap<>();
            basicStats.put("totalAdvices", totalAdvices);
            basicStats.put("processedAdvices", processedAdvices);
            basicStats.put("completionRate", Math.round(completionRate * 10) / 10.0);
            basicStats.put("satisfactionRate", Math.round(satisfactionRate * 10) / 10.0);

            // 添加趋势数据
            basicStats.put("totalAdvicesTrend", trendCalculations.get("totalAdvicesTrend"));
            basicStats.put("processedAdvicesTrend", trendCalculations.get("processedAdvicesTrend"));
            basicStats.put("completionRateTrend", trendCalculations.get("completionRateTrend"));
            basicStats.put("satisfactionRateTrend", trendCalculations.get("satisfactionRateTrend"));

            // 意见类型分布 - 从数据字典获取分类
            List<Map<String, Object>> typeDistribution = getAdviceTypeDistribution(allAdvices);

            // 意见处理状态分布
            List<Map<String, Object>> statusDistribution = new ArrayList<>();
            if (totalAdvices > 0) {
                statusDistribution.add(createStatusData("已办结", processedAdvices));
                statusDistribution.add(createStatusData("办理中", processingAdvices));
                statusDistribution.add(createStatusData("待办理", Math.max(0, totalAdvices - processedAdvices - processingAdvices)));
            } else {
                statusDistribution.add(createStatusData("暂无数据", 1));
            }

            // 月度趋势数据 - 最近12个月
            Map<String, Object> trendData = getMonthlyTrendData(allAdvices);

            // 满意度详细统计信息
            Map<String, Object> satisfactionDetails = getSatisfactionDetails(allAdvices);

            statistics.put("basicStats", basicStats);
            statistics.put("typeDistribution", typeDistribution);
            statistics.put("statusDistribution", statusDistribution);
            statistics.put("trendData", trendData);
            statistics.put("satisfactionDetails", satisfactionDetails);

            logger.info("统计数据返回结果: 联络站ID={}, 总意见数={}, 已办结数={}, 办结率={}%, 满意度={}%, 类型分布数量={}, 状态分布数量={}, 趋势数据月份数={}",
                       stationId, totalAdvices, processedAdvices, Math.round(completionRate * 10) / 10.0,
                       Math.round(satisfactionRate * 10) / 10.0, typeDistribution.size(), statusDistribution.size(),
                       trendData.get("months") != null ? ((List)trendData.get("months")).size() : 0);

            return success(statistics);
        }
        catch (NumberFormatException e)
        {
            logger.error("联络站ID格式错误", e);
            return error("联络站ID格式错误");
        }
        catch (Exception e)
        {
            logger.error("获取统计数据失败", e);
            return error("获取统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 计算趋势数据 - 当前月与上月对比
     */
    private Map<String, Object> calculateTrends(List<AdviceExtVO> allAdvices) {
        Calendar calendar = Calendar.getInstance();
        Map<String, Object> trends = new HashMap<>();

        // 获取当前月和上月的数据
        Date now = new Date();
        calendar.setTime(now);

        // 当前月开始和结束
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date currentMonthStart = calendar.getTime();

        calendar.add(Calendar.MONTH, 1);
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        Date currentMonthEnd = calendar.getTime();

        // 上月开始和结束
        calendar.setTime(currentMonthStart);
        calendar.add(Calendar.MONTH, -1);
        Date lastMonthStart = calendar.getTime();

        calendar.add(Calendar.MONTH, 1);
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        Date lastMonthEnd = calendar.getTime();

        // 当前月统计
        List<AdviceExtVO> currentMonthAdvices = allAdvices.stream()
                .filter(advice -> advice.getCreateTime() != null &&
                        !advice.getCreateTime().before(currentMonthStart) &&
                        !advice.getCreateTime().after(currentMonthEnd))
                .collect(Collectors.toList());

        // 上月统计
        List<AdviceExtVO> lastMonthAdvices = allAdvices.stream()
                .filter(advice -> advice.getCreateTime() != null &&
                        !advice.getCreateTime().before(lastMonthStart) &&
                        !advice.getCreateTime().after(lastMonthEnd))
                .collect(Collectors.toList());

        // 计算各项指标的当前月和上月数据
        int currentTotalAdvices = currentMonthAdvices.size();
        int lastTotalAdvices = lastMonthAdvices.size();

        int currentProcessedAdvices = (int) currentMonthAdvices.stream()
                .filter(advice -> "2".equals(advice.getStatus())).count();
        int lastProcessedAdvices = (int) lastMonthAdvices.stream()
                .filter(advice -> "2".equals(advice.getStatus())).count();

        double currentCompletionRate = currentTotalAdvices > 0 ?
                (double) currentProcessedAdvices / currentTotalAdvices * 100 : 0;
        double lastCompletionRate = lastTotalAdvices > 0 ?
                (double) lastProcessedAdvices / lastTotalAdvices * 100 : 0;

        double currentSatisfactionRate = calculateSatisfactionRate(currentMonthAdvices);
        double lastSatisfactionRate = calculateSatisfactionRate(lastMonthAdvices);

        // 计算趋势
        trends.put("totalAdvicesTrend", calculateTrendInfo(currentTotalAdvices, lastTotalAdvices, false));
        trends.put("processedAdvicesTrend", calculateTrendInfo(currentProcessedAdvices, lastProcessedAdvices, false));
        trends.put("completionRateTrend", calculateTrendInfo(currentCompletionRate, lastCompletionRate, true));
        trends.put("satisfactionRateTrend", calculateTrendInfo(currentSatisfactionRate, lastSatisfactionRate, true));

        return trends;
    }

    /**
     * 计算单个指标的趋势信息
     */
    private Map<String, Object> calculateTrendInfo(double currentValue, double lastValue, boolean isPercentage) {
        Map<String, Object> trend = new HashMap<>();

        if (lastValue == 0) {
            if (currentValue > 0) {
                trend.put("type", "up");
                trend.put("text", "较上月新增");
            } else {
                trend.put("type", "neutral");
                trend.put("text", "暂无对比数据");
            }
        } else {
            double changeRate = ((currentValue - lastValue) / lastValue) * 100;

            if (Math.abs(changeRate) < 5) {
                // 变化幅度小于5%认为基本持平
                trend.put("type", "neutral");
                trend.put("text", "较上月持平");
            } else if (changeRate > 0) {
                trend.put("type", "up");
                if (isPercentage) {
                    trend.put("text", String.format("较上月上升%.1f%%", changeRate));
                } else {
                    trend.put("text", String.format("较上月增长%.1f%%", changeRate));
                }
            } else {
                trend.put("type", "down");
                if (isPercentage) {
                    trend.put("text", String.format("较上月下降%.1f%%", Math.abs(changeRate)));
                } else {
                    trend.put("text", String.format("较上月减少%.1f%%", Math.abs(changeRate)));
                }
            }
        }

        return trend;
    }

    /**
     * 计算办理满意度
     * 优化版本：更准确地反映群众满意程度
     */
    private double calculateSatisfactionRate(List<AdviceExtVO> advices) {
        if (advices.isEmpty()) {
            return 0;
        }

        // 只统计已办结的意见的满意度
        List<AdviceExtVO> processedAdvices = advices.stream()
                .filter(advice -> "2".equals(advice.getStatus()))
                .collect(Collectors.toList());

        if (processedAdvices.isEmpty()) {
            return 0;
        }

        // 统计各评分等级的数量
        int[] ratingCounts = new int[6]; // 索引0-5，对应评分0-5分
        int totalRatedCount = 0;
        int unratedCount = 0;

        for (AdviceExtVO advice : processedAdvices) {
            if (advice.getServiceRating() != null && advice.getServiceRating() >= 1 && advice.getServiceRating() <= 5) {
                ratingCounts[advice.getServiceRating()]++;
                totalRatedCount++;
            } else {
                unratedCount++;
            }
        }

        // 如果完全没有评分数据，根据办结率推测满意度
        if (totalRatedCount == 0) {
            // 基于办结率的满意度估算：办结率高说明处理效率好，满意度相对较高
            int totalAdvices = advices.size();
            int allProcessedAdvices = processedAdvices.size();
            double completionRate = totalAdvices > 0 ? (double) allProcessedAdvices / totalAdvices : 0;

            // 办结率转换为满意度的保守估算：
            // 办结率90%以上 -> 75%满意度
            // 办结率70-90% -> 65%满意度
            // 办结率50-70% -> 55%满意度
            // 办结率50%以下 -> 45%满意度
            if (completionRate >= 0.9) {
                return 75.0;
            } else if (completionRate >= 0.7) {
                return 65.0;
            } else if (completionRate >= 0.5) {
                return 55.0;
            } else {
                return 45.0;
            }
        }

        // 计算满意度：采用更合理的权重分配
        // 5分(非常满意)=100%, 4分(满意)=85%, 3分(一般)=60%, 2分(不满意)=25%, 1分(非常不满意)=0%
        double[] satisfactionWeights = {0, 0.0, 0.25, 0.60, 0.85, 1.0};

        double weightedSatisfaction = 0;
        for (int rating = 1; rating <= 5; rating++) {
            weightedSatisfaction += ratingCounts[rating] * satisfactionWeights[rating];
        }

        // 计算基础满意度
        double baseSatisfactionRate = (weightedSatisfaction / totalRatedCount) * 100;

        // 考虑未评分意见的影响：未评分可能暗示中等满意度
        if (unratedCount > 0) {
            double unratedWeight = (double) unratedCount / processedAdvices.size();
            // 未评分按照中等满意度(60%)处理，但权重较低
            double adjustedSatisfactionRate = baseSatisfactionRate * (1 - unratedWeight * 0.3) + 60 * (unratedWeight * 0.3);
            return Math.round(adjustedSatisfactionRate * 10) / 10.0;
        }

        // 质量评估：如果满意(4-5分)比例很高，给予bonus
        int highSatisfactionCount = ratingCounts[4] + ratingCounts[5];
        double highSatisfactionRatio = (double) highSatisfactionCount / totalRatedCount;

        if (highSatisfactionRatio >= 0.8) {
            // 80%以上高满意度，给予小幅提升
            baseSatisfactionRate = Math.min(100.0, baseSatisfactionRate * 1.05);
        } else if (highSatisfactionRatio <= 0.3) {
            // 高满意度比例低于30%，适度降低
            baseSatisfactionRate = baseSatisfactionRate * 0.95;
        }

        return Math.round(Math.max(0.0, Math.min(100.0, baseSatisfactionRate)) * 10) / 10.0;
    }

    /**
     * 获取满意度详细统计信息（可用于调试和分析）
     */
    private Map<String, Object> getSatisfactionDetails(List<AdviceExtVO> advices) {
        Map<String, Object> details = new HashMap<>();

        List<AdviceExtVO> processedAdvices = advices.stream()
                .filter(advice -> "2".equals(advice.getStatus()))
                .collect(Collectors.toList());

        int[] ratingCounts = new int[6];
        int totalRatedCount = 0;
        int unratedCount = 0;

        for (AdviceExtVO advice : processedAdvices) {
            if (advice.getServiceRating() != null && advice.getServiceRating() >= 1 && advice.getServiceRating() <= 5) {
                ratingCounts[advice.getServiceRating()]++;
                totalRatedCount++;
            } else {
                unratedCount++;
            }
        }

        details.put("totalProcessed", processedAdvices.size());
        details.put("totalRated", totalRatedCount);
        details.put("unratedCount", unratedCount);
        details.put("rating5Count", ratingCounts[5]); // 非常满意
        details.put("rating4Count", ratingCounts[4]); // 满意
        details.put("rating3Count", ratingCounts[3]); // 一般
        details.put("rating2Count", ratingCounts[2]); // 不满意
        details.put("rating1Count", ratingCounts[1]); // 非常不满意

        // 计算满意度分布百分比
        if (totalRatedCount > 0) {
            details.put("satisfiedRatio", Math.round(((double)(ratingCounts[4] + ratingCounts[5]) / totalRatedCount) * 1000) / 10.0);
            details.put("neutralRatio", Math.round(((double)ratingCounts[3] / totalRatedCount) * 1000) / 10.0);
            details.put("dissatisfiedRatio", Math.round(((double)(ratingCounts[1] + ratingCounts[2]) / totalRatedCount) * 1000) / 10.0);
        }

        return details;
    }

    /**
     * 满意度算法测试接口（仅开发调试使用）
     * 可以通过查看满意度详细统计来验证算法的准确性
     */
    @Anonymous
    @GetMapping("/testSatisfactionAlgorithm/{stationId}")
    public AjaxResult testSatisfactionAlgorithm(@PathVariable String stationId) {
        try {
            Long stationIdLong = null;
            if (!"0".equals(stationId)) {
                stationIdLong = Long.parseLong(stationId);
            }

            RdAdvice queryAdvice = new RdAdvice();
            queryAdvice.setIsStationDeputy("1");
            List<AdviceExtVO> allAdvices = adviceService.selectAdviceExtList(queryAdvice);

            Map<String, Object> testResult = new HashMap<>();
            testResult.put("满意度计算结果", calculateSatisfactionRate(allAdvices));
            testResult.put("满意度详细统计", getSatisfactionDetails(allAdvices));

            // 分步骤展示计算过程
            List<AdviceExtVO> processedAdvices = allAdvices.stream()
                    .filter(advice -> "2".equals(advice.getStatus()))
                    .collect(Collectors.toList());

            testResult.put("总意见数", allAdvices.size());
            testResult.put("已办结意见数", processedAdvices.size());

            return success(testResult);
        } catch (Exception e) {
            logger.error("满意度算法测试失败", e);
            return error("满意度算法测试失败：" + e.getMessage());
        }
    }

    /**
     * 获取意见类型分布统计
     */
    private List<Map<String, Object>> getAdviceTypeDistribution(List<AdviceExtVO> allAdvices) {
        try {
            // 获取意见分类字典数据
            SysDictData queryData = new SysDictData();
            queryData.setDictType("rd_advice_category");
            queryData.setStatus("0");
            List<SysDictData> categoryList = dictDataService.selectDictDataList(queryData);

            List<Map<String, Object>> typeDistribution = new ArrayList<>();

            // 统计各分类的意见数量
            for (SysDictData category : categoryList) {
                String categoryValue = category.getDictValue();
                long count = allAdvices.stream()
                        .filter(advice -> categoryValue.equals(advice.getCategory()))
                        .count();

                if (count > 0) { // 只添加有数据的分类
                    typeDistribution.add(createTypeData(category.getDictLabel(), (int) count));
                }
            }

            // 如果没有分类数据，添加默认分类
            if (typeDistribution.isEmpty()) {
                if (allAdvices.size() > 0) {
                    typeDistribution.add(createTypeData("未分类", allAdvices.size()));
                } else {
                    typeDistribution.add(createTypeData("暂无数据", 1));
                }
            }

            return typeDistribution;
        } catch (Exception e) {
            logger.error("获取意见类型分布失败", e);
            // 返回默认分类统计
            List<Map<String, Object>> typeDistribution = new ArrayList<>();
            if (allAdvices.size() > 0) {
                typeDistribution.add(createTypeData("未分类", allAdvices.size()));
            } else {
                typeDistribution.add(createTypeData("暂无数据", 1));
            }
            return typeDistribution;
        }
    }

    /**
     * 获取月度趋势数据
     */
    private Map<String, Object> getMonthlyTrendData(List<AdviceExtVO> allAdvices) {
        Calendar calendar = Calendar.getInstance();
        List<String> months = new ArrayList<>();
        List<Integer> receivedData = new ArrayList<>();
        List<Integer> processedData = new ArrayList<>();

        // 生成最近12个月的数据
        for (int i = 11; i >= 0; i--) {
            calendar.setTime(new Date());
            calendar.add(Calendar.MONTH, -i);

            int month = calendar.get(Calendar.MONTH) + 1;
            int year = calendar.get(Calendar.YEAR);
            months.add(month + "月");

            // 统计该月收到的意见数量
            int receivedCount = (int) allAdvices.stream()
                    .filter(advice -> {
                        if (advice.getCreateTime() == null) return false;
                        Calendar adviceCalendar = Calendar.getInstance();
                        adviceCalendar.setTime(advice.getCreateTime());
                        return adviceCalendar.get(Calendar.YEAR) == year &&
                               adviceCalendar.get(Calendar.MONTH) == calendar.get(Calendar.MONTH);
                    })
                    .count();

            // 统计该月已处理的意见数量
            int processedCount = (int) allAdvices.stream()
                    .filter(advice -> {
                        if (advice.getCreateTime() == null || !"2".equals(advice.getStatus())) return false;
                        Calendar adviceCalendar = Calendar.getInstance();
                        adviceCalendar.setTime(advice.getCreateTime());
                        return adviceCalendar.get(Calendar.YEAR) == year &&
                               adviceCalendar.get(Calendar.MONTH) == calendar.get(Calendar.MONTH);
                    })
                    .count();

            receivedData.add(receivedCount);
            processedData.add(processedCount);
        }

        Map<String, Object> trendData = new HashMap<>();
        trendData.put("months", months);
        trendData.put("received", receivedData);
        trendData.put("processed", processedData);

        return trendData;
    }

    /**
     * 创建类型数据
     */
    private Map<String, Object> createTypeData(String name, int value)
    {
        Map<String, Object> data = new HashMap<>();
        data.put("name", name);
        data.put("value", value);
        return data;
    }

    /**
     * 创建状态数据
     */
    private Map<String, Object> createStatusData(String name, int value)
    {
        Map<String, Object> data = new HashMap<>();
        data.put("name", name);
        data.put("value", value);
        return data;
    }

    /**
     * 根据联络站ID获取组织机构信息
     */
    @Anonymous
    @GetMapping("/getOrganizationData/{stationId}")
    public AjaxResult getOrganizationData(@PathVariable String stationId)
    {
        if (StringUtils.isEmpty(stationId))
        {
            return error("联络站ID不能为空");
        }

        try
        {
            Long stationIdLong = Long.parseLong(stationId);

            // 查询联络站信息
            RdStation station = stationService.selectStationById(stationIdLong);

            // 创建查询条件
            RdDeputy queryDeputy = new RdDeputy();
            queryDeputy.setIsStationDeputy("1");

            // 查询该联络站的所有人大代表
            List<RdDeputy> allDeputies = deputyService.selectRdDeputyList(queryDeputy);

            // 按照站内职务分类
            List<RdDeputy> directors = allDeputies.stream()
                    .filter(deputy -> "站长".equals(deputy.getStationDuty()))
                    .collect(Collectors.toList());

            List<RdDeputy> viceDirectors = allDeputies.stream()
                    .filter(deputy -> "副站长".equals(deputy.getStationDuty()))
                    .collect(Collectors.toList());

            List<RdDeputy> liaisons = allDeputies.stream()
                    .filter(deputy -> "联络员".equals(deputy.getStationDuty()))
                    .collect(Collectors.toList());

            // 获取代表分类字典数据
            SysDictData dictQuery = new SysDictData();
            dictQuery.setDictType("rd_deputy_tags");
            dictQuery.setStatus("0");
            List<SysDictData> categoryList = dictDataService.selectDictDataList(dictQuery);

            // 构造返回数据
            Map<String, Object> result = new HashMap<>();
            result.put("directors", directors);
            result.put("viceDirectors", viceDirectors);
            result.put("liaisons", liaisons);
            result.put("representatives", allDeputies);
            result.put("categories", categoryList);
            result.put("station", station); // 添加站点信息

            return success(result);
        }
        catch (NumberFormatException e)
        {
            logger.error("联络站ID格式错误", e);
            return error("联络站ID格式错误");
        }
        catch (Exception e)
        {
            logger.error("获取组织机构数据失败", e);
            return error("获取组织机构数据失败：" + e.getMessage());
        }
    }

    /**
     * 根据联络站ID获取排班信息（支持日期范围查询）
     */
    @Anonymous
    @GetMapping("/getScheduleData/{stationId}")
    public AjaxResult getScheduleData(@PathVariable String stationId,
                                     @RequestParam(value = "startDate", required = false) String startDate,
                                     @RequestParam(value = "endDate", required = false) String endDate)
    {
        if (StringUtils.isEmpty(stationId))
        {
            return error("联络站ID不能为空");
        }

        logger.info("获取排班数据请求参数 - stationId: {}, startDate: {}, endDate: {}", stationId, startDate, endDate);

        try
        {
            Long stationIdLong = Long.parseLong(stationId);

            // 获取日期范围
            Calendar calendar = Calendar.getInstance();
            Date queryStartDate;
            Date queryEndDate;

            if (StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate)) {
                // 使用传入的日期范围
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date tempStartDate = inputFormat.parse(startDate);
                Date tempEndDate = inputFormat.parse(endDate);

                // 设置开始时间为当天00:00:00
                calendar.setTime(tempStartDate);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                queryStartDate = calendar.getTime();

                // 设置结束时间为当天23:59:59
                calendar.setTime(tempEndDate);
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                calendar.set(Calendar.MILLISECOND, 999);
                queryEndDate = calendar.getTime();
            } else {
                // 默认使用今天到未来7天（向后兼容）
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                queryStartDate = calendar.getTime();

                calendar.add(Calendar.DAY_OF_MONTH, 6);
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                calendar.set(Calendar.MILLISECOND, 999);
                queryEndDate = calendar.getTime();
            }

            // 计算今天的日期，用于判断isToday
            Calendar todayCalendar = Calendar.getInstance();
            todayCalendar.set(Calendar.HOUR_OF_DAY, 0);
            todayCalendar.set(Calendar.MINUTE, 0);
            todayCalendar.set(Calendar.SECOND, 0);
            todayCalendar.set(Calendar.MILLISECOND, 0);
            Date todayStart = todayCalendar.getTime();

            // 查询排班数据
            RdStationSchedule querySchedule = new RdStationSchedule();
            querySchedule.setStationId(stationIdLong);
            List<RdStationSchedule> scheduleList = stationScheduleService.selectRdStationScheduleList(querySchedule);

            logger.info("联络站ID: {}, 查询到排班数据总数: {}", stationIdLong, scheduleList.size());
            logger.info("日期范围: {} 到 {}", queryStartDate, queryEndDate);

            // 过滤出指定日期范围内的数据
            final Date finalQueryStartDate = queryStartDate;
            final Date finalQueryEndDate = queryEndDate;
            List<RdStationSchedule> filteredSchedules = scheduleList.stream()
                    .filter(schedule -> {
                        Date scheduleDate = schedule.getScheduleDate();
                        boolean inRange = scheduleDate != null &&
                               !scheduleDate.before(finalQueryStartDate) &&
                               !scheduleDate.after(finalQueryEndDate);
                        if (scheduleDate != null) {
                            logger.debug("排班日期: {}, 是否在范围内: {}", scheduleDate, inRange);
                        }
                        return inRange;
                    })
                    .collect(Collectors.toList());

            logger.info("过滤后的排班数据数量: {}", filteredSchedules.size());

            // 构造返回数据
            List<Map<String, Object>> weeklyScheduleData = new ArrayList<>();
            SimpleDateFormat dateFormat = new SimpleDateFormat("dd");
            SimpleDateFormat monthFormat = new SimpleDateFormat("MM月");
            String[] weekdays = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};

            // 计算日期范围的天数
            long daysDiff = (queryEndDate.getTime() - queryStartDate.getTime()) / (24 * 60 * 60 * 1000) + 1;

            Calendar cal = Calendar.getInstance();
            for (int i = 0; i < daysDiff; i++) {
                cal.setTime(queryStartDate);
                cal.add(Calendar.DAY_OF_MONTH, i);
                Date currentDate = cal.getTime();

                // 查找当天的排班记录
                RdStationSchedule currentSchedule = filteredSchedules.stream()
                        .filter(schedule -> isSameDay(schedule.getScheduleDate(), currentDate))
                        .findFirst()
                        .orElse(null);

                Map<String, Object> dayData = new HashMap<>();
                dayData.put("date", dateFormat.format(currentDate));
                dayData.put("month", monthFormat.format(currentDate));
                dayData.put("weekday", weekdays[cal.get(Calendar.DAY_OF_WEEK) - 1]);
                dayData.put("isToday", isSameDay(currentDate, todayStart));

                if (currentSchedule != null) {
                    dayData.put("representative", currentSchedule.getDeputyName());
                    dayData.put("representativeId", currentSchedule.getDeputyId());
                    dayData.put("staff", currentSchedule.getStaffName());
                    dayData.put("staffId", currentSchedule.getStaffId());

                    // 获取代表详细信息
                    if (currentSchedule.getDeputyId() != null) {
                        RdDeputy deputy = deputyService.selectRdDeputyById(currentSchedule.getDeputyId());
                        if (deputy != null) {
                            dayData.put("repUnit", deputy.getCompany());
                        }
                    }

                    // 获取工作人员详细信息
                    if (currentSchedule.getStaffId() != null) {
                        RdStationStaff staff = stationStaffService.selectRdStationStaffById(currentSchedule.getStaffId());
                        if (staff != null) {
                            dayData.put("staffUnit", staff.getUnit());
                        }
                    }

                    // 根据日期计算状态
                    if (isSameDay(currentDate, todayStart)) {
                        dayData.put("status", "进行中");
                        dayData.put("statusClass", "active");
                    } else if (currentDate.before(todayStart)) {
                        dayData.put("status", "已完成");
                        dayData.put("statusClass", "completed");
                    } else {
                        dayData.put("status", "待值班");
                        dayData.put("statusClass", "pending");
                    }
                } else {
                    dayData.put("representative", "暂无安排");
                    dayData.put("representativeId", null);
                    dayData.put("repUnit", "");
                    dayData.put("staff", "暂无安排");
                    dayData.put("staffId", null);
                    dayData.put("staffUnit", "");
                    if (currentDate.before(todayStart)) {
                        dayData.put("status", "已完成");
                        dayData.put("statusClass", "completed");
                    } else {
                        dayData.put("status", "待安排");
                        dayData.put("statusClass", "pending");
                    }
                }

                weeklyScheduleData.add(dayData);
            }

            // 获取今日值班信息
            Map<String, Object> todayInfo = new HashMap<>();
            RdStationSchedule todaySchedule = filteredSchedules.stream()
                    .filter(schedule -> isSameDay(schedule.getScheduleDate(), todayStart))
                    .findFirst()
                    .orElse(null);

            if (todaySchedule != null) {
                // 今日代表信息
                if (todaySchedule.getDeputyId() != null) {
                    RdDeputy todayDeputy = deputyService.selectRdDeputyById(todaySchedule.getDeputyId());
                    if (todayDeputy != null) {
                        Map<String, Object> deputyInfo = new HashMap<>();
                        deputyInfo.put("id", todayDeputy.getId());
                        deputyInfo.put("name", todayDeputy.getName());
                        deputyInfo.put("unit", todayDeputy.getCompany());
                        deputyInfo.put("position", todayDeputy.getDuty());
                        deputyInfo.put("phone", todayDeputy.getPhone());
                        deputyInfo.put("avatar", todayDeputy.getAvatar());
                        deputyInfo.put("qrcodeUrl", todayDeputy.getQrcodeUrl());
                        todayInfo.put("todayRepresentative", deputyInfo);
                    }
                }

                // 今日工作人员信息
                if (todaySchedule.getStaffId() != null) {
                    RdStationStaff todayStaff = stationStaffService.selectRdStationStaffById(todaySchedule.getStaffId());
                    if (todayStaff != null) {
                        Map<String, Object> staffInfo = new HashMap<>();
                        staffInfo.put("id", todayStaff.getId());
                        staffInfo.put("name", todayStaff.getName());
                        staffInfo.put("unit", todayStaff.getUnit());
                        staffInfo.put("position", todayStaff.getPosition());
                        staffInfo.put("phone", todayStaff.getPhone());
                        staffInfo.put("avatar", todayStaff.getAvatar());
                        todayInfo.put("todayStaff", staffInfo);
                    }
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("weeklySchedule", weeklyScheduleData);
            result.put("todayInfo", todayInfo);

            return success(result);
        }
        catch (NumberFormatException e)
        {
            logger.error("联络站ID格式错误", e);
            return error("联络站ID格式错误");
        }
        catch (Exception e)
        {
            logger.error("获取排班数据失败", e);
            return error("获取排班数据失败：" + e.getMessage());
        }
    }

    /**
     * 判断两个日期是否为同一天
     */
    private boolean isSameDay(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return false;
        }
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();
        cal1.setTime(date1);
        cal2.setTime(date2);
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
               cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH) &&
               cal1.get(Calendar.DAY_OF_MONTH) == cal2.get(Calendar.DAY_OF_MONTH);
    }

        /**
     * 获取意见分类数据
     */
    @Anonymous
    @GetMapping("/getAdviceCategories")
    public AjaxResult getAdviceCategories(@RequestParam(required = false) String stationId)
    {
        try
        {
            Long stationIdLong = null;
            if (StringUtils.isNotEmpty(stationId) && !"0".equals(stationId)) {
                stationIdLong = Long.parseLong(stationId);
            }

            // 查询意见分类字典数据
            SysDictData queryData = new SysDictData();
            queryData.setDictType("rd_advice_category");
            queryData.setStatus("0"); // 只查询正常状态的数据
            List<SysDictData> categoryList = dictDataService.selectDictDataList(queryData);

            // 构造分类数据，包含统计数量
            List<Map<String, Object>> categories = new ArrayList<>();

            // 添加全部分类
            Map<String, Object> allCategory = new HashMap<>();
            allCategory.put("id", 0);
            allCategory.put("name", "全部");
            allCategory.put("icon", "el-icon-s-grid");
            allCategory.put("count", getAdviceCountByStationAndCategory(stationIdLong, null));
            categories.add(allCategory);

            // 添加具体分类
            for (SysDictData dictData : categoryList) {
                Map<String, Object> category = new HashMap<>();
                category.put("id", Long.parseLong(dictData.getDictValue()));
                category.put("name", dictData.getDictLabel());
                category.put("icon", getIconByCategory(dictData.getDictLabel()));
                category.put("count", getAdviceCountByStationAndCategory(stationIdLong, dictData.getDictValue()));
                categories.add(category);
            }

            return success(categories);
        }
        catch (Exception e)
        {
            logger.error("获取意见分类失败", e);
            return error("获取意见分类失败：" + e.getMessage());
        }
    }

    /**
     * 获取意见建议列表
     */
    @Anonymous
    @GetMapping("/getAdviceList")
    public AjaxResult getAdviceList(
            @RequestParam(required = false) String isStationDeputy,
            @RequestParam(required = false) String categoryId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String sortOrder,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "15") Integer pageSize)
    {
        try
        {
                        // 构造查询条件
            RdAdvice queryAdvice = new RdAdvice();

            // 如果指定了是否进站代表，则只查询进站代表的数据
            if (StringUtils.isNotEmpty(isStationDeputy)) {
                queryAdvice.setIsStationDeputy(isStationDeputy);
            }

            // 分类筛选
            if (StringUtils.isNotEmpty(categoryId) && !"0".equals(categoryId)) {
                queryAdvice.setCategory(categoryId);
            }

            // 状态筛选
            if (StringUtils.isNotEmpty(status)) {
                if ("待处理".equals(status)) {
                    queryAdvice.setStatus("0");
                } else if ("处理中".equals(status)) {
                    queryAdvice.setStatus("1");
                } else if ("已处理".equals(status)) {
                    queryAdvice.setStatus("2");
                } else {
                    queryAdvice.setStatus("");
                }
            }

            // 查询意见建议列表
            List<AdviceExtVO> adviceList = adviceService.selectAdviceExtList(queryAdvice);

            // 排序
            if ("asc".equals(sortOrder)) {
                adviceList.sort((a, b) -> a.getCreateTime().compareTo(b.getCreateTime()));
            } else {
                adviceList.sort((a, b) -> b.getCreateTime().compareTo(a.getCreateTime()));
            }

            // 手动分页
            int total = adviceList.size();
            int startIndex = (pageNum - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);

            List<AdviceExtVO> pagedList = new ArrayList<>();
            if (startIndex < total) {
                pagedList = adviceList.subList(startIndex, endIndex);
            }

            // 为每个意见建议获取反馈信息
            List<Map<String, Object>> resultList = new ArrayList<>();
            for (AdviceExtVO advice : pagedList) {
                Map<String, Object> adviceData = convertAdviceToMap(advice);

                // 获取反馈信息
                List<FeedbackInfoVO> feedbackList = feedbackService.selectFeedbackListWithAttachs(advice.getAdviceId());
                if (!feedbackList.isEmpty()) {
                    // 获取第一个反馈作为主要反馈
                    FeedbackInfoVO mainFeedback = feedbackList.get(0);
                    Map<String, Object> feedbackData = new HashMap<>();
                    feedbackData.put("representative", mainFeedback.getName());
                    feedbackData.put("date", mainFeedback.getCreateTime());
                    feedbackData.put("content", mainFeedback.getContent());
                    adviceData.put("feedback", feedbackData);

                    // 如果有多个反馈，作为对话回复处理
                    if (feedbackList.size() > 1) {
                        List<Map<String, Object>> replies = new ArrayList<>();
                        for (int i = 1; i < feedbackList.size(); i++) {
                            FeedbackInfoVO feedback = feedbackList.get(i);
                            Map<String, Object> reply = new HashMap<>();
                            reply.put("id", i); // 使用索引作为ID
                            if (feedback.getUserType() == 1) {
                                reply.put("type", "citizen");
                            } else {
                                reply.put("type", "representative");
                            }
                            reply.put("author", feedback.getName());
                            reply.put("date", feedback.getCreateTime());
                            reply.put("content", feedback.getContent());
                            replies.add(reply);
                        }
                        adviceData.put("replies", replies);
                    }
                }

                resultList.add(adviceData);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("list", resultList);
            result.put("total", total);
            result.put("pageNum", pageNum);
            result.put("pageSize", pageSize);
            result.put("totalPages", (int) Math.ceil((double) total / pageSize));

            return success(result);
        }
        catch (Exception e)
        {
            logger.error("获取意见建议列表失败", e);
            return error("获取意见建议列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取意见建议详情
     */
    @Anonymous
    @GetMapping("/getAdviceDetail/{adviceId}")
    public AjaxResult getAdviceDetail(@PathVariable Long adviceId)
    {
        try
        {
            // 查询意见建议详情
            RdAdvice advice = adviceService.selectRdAdviceById(adviceId);
            if (advice == null) {
                return error("意见建议不存在");
            }

            Map<String, Object> adviceData = new HashMap<>();
            adviceData.put("id", advice.getId());
            adviceData.put("title", advice.getTitle());
            adviceData.put("content", advice.getContent());
            adviceData.put("category", advice.getCategory());
            adviceData.put("submitter", advice.getName());
            adviceData.put("contact", advice.getPhone());
            adviceData.put("submitDate", advice.getCreateTime());
            adviceData.put("status", advice.getStatus());
            adviceData.put("rating", advice.getServiceRating()); // 群众服务评分

            // 获取反馈信息
            List<FeedbackInfoVO> feedbackList = feedbackService.selectFeedbackListWithAttachs(adviceId);

            List<Map<String, Object>> feedbacks = new ArrayList<>();
            for (int i = 0; i < feedbackList.size(); i++) {
                FeedbackInfoVO feedback = feedbackList.get(i);
                Map<String, Object> feedbackData = new HashMap<>();
                feedbackData.put("id", i + 1); // 使用索引作为ID
                feedbackData.put("representative", feedback.getName());
                feedbackData.put("date", feedback.getCreateTime());
                feedbackData.put("content", feedback.getContent());
                feedbackData.put("userType", feedback.getUserType()); // 添加用户类型：0-代表；1-群众
                feedbacks.add(feedbackData);
            }

            adviceData.put("feedbacks", feedbacks);

            return success(adviceData);
        }
        catch (Exception e)
        {
            logger.error("获取意见建议详情失败", e);
            return error("获取意见建议详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取意见反馈列表
     */
    @Anonymous
    @GetMapping("/getFeedbackList/{adviceId}")
    public AjaxResult getFeedbackList(@PathVariable Long adviceId)
    {
        try
        {
            List<FeedbackInfoVO> feedbackList = feedbackService.selectFeedbackListWithAttachs(adviceId);
            return success(feedbackList);
        }
        catch (Exception e)
        {
            logger.error("获取意见反馈列表失败", e);
            return error("获取意见反馈列表失败：" + e.getMessage());
        }
    }

    /**
     * 将AdviceExtVO转换为Map
     */
    private Map<String, Object> convertAdviceToMap(AdviceExtVO advice) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", advice.getAdviceId());
        map.put("title", advice.getTitle());
        map.put("content", advice.getContent());
        map.put("category", advice.getCategory());
        map.put("submitter", advice.getName());
        map.put("contact", advice.getPhone());
        map.put("submitDate", advice.getCreateTime());
        map.put("status", advice.getStatus());
        map.put("rating", advice.getServiceRating()); // 群众服务评分
        return map;
    }

    /**
     * 根据分类名称获取图标
     */
    private String getIconByCategory(String categoryName) {
        Map<String, String> iconMap = new HashMap<>();
        iconMap.put("经济发展", "el-icon-money");
        iconMap.put("民生保障", "el-icon-user-solid");
        iconMap.put("城市建设", "el-icon-office-building");
        iconMap.put("环境保护", "el-icon-sunny");
        iconMap.put("教育文化", "el-icon-reading");
        iconMap.put("法治建设", "el-icon-collection");
        iconMap.put("矛盾化解", "el-icon-refresh");
        iconMap.put("窗口服务", "el-icon-service");
        return iconMap.getOrDefault(categoryName, "el-icon-more");
    }

    /**
     * 获取总的意见建议数量
     */
    private int getTotalAdviceCount() {
        try {
            RdAdvice queryAdvice = new RdAdvice();
            queryAdvice.setIsStationDeputy("1");
            List<AdviceExtVO> adviceList = adviceService.selectAdviceExtList(queryAdvice);
            return adviceList.size();
        } catch (Exception e) {
            logger.error("获取总意见建议数量失败", e);
            return 0;
        }
    }

    /**
     * 根据分类获取意见建议数量
     */
    private int getAdviceCountByCategory(String categoryName) {
        try {
            RdAdvice queryAdvice = new RdAdvice();
            queryAdvice.setCategory(categoryName);
            queryAdvice.setIsStationDeputy("1");
            List<AdviceExtVO> adviceList = adviceService.selectAdviceExtList(queryAdvice);
            return adviceList.size();
        } catch (Exception e) {
            logger.error("获取分类意见建议数量失败", e);
            return 0;
        }
    }

    /**
     * 根据联络站ID和分类获取意见建议数量
     */
    private int getAdviceCountByStationAndCategory(Long stationId, String category) {
        try {
            RdAdvice queryAdvice = new RdAdvice();
            if (StringUtils.isNotEmpty(category)) {
                queryAdvice.setCategory(category);
            }
            queryAdvice.setIsStationDeputy("1");
            List<AdviceExtVO> adviceList = adviceService.selectAdviceExtList(queryAdvice);
            return adviceList.size();
        } catch (Exception e) {
            logger.error("获取联络站分类意见建议数量失败", e);
            return 0;
        }
    }

    /**
     * 根据代表ID获取代表详情及履职档案
     */
    @Anonymous
    @GetMapping("/getRepresentativeDetail/{deputyId}")
    public AjaxResult getRepresentativeDetail(@PathVariable Long deputyId)
    {
        if (deputyId == null)
        {
            return error("代表ID不能为空");
        }

        try
        {
            // 查询代表基本信息
            RdDeputy deputy = deputyService.selectRdDeputyById(deputyId);
            if (deputy == null) {
                return error("代表信息不存在");
            }

            // 构造代表基本信息
            Map<String, Object> representativeInfo = new HashMap<>();
            representativeInfo.put("id", deputy.getId());
            representativeInfo.put("name", deputy.getName());
            representativeInfo.put("unit", deputy.getCompany());
            representativeInfo.put("position", deputy.getDuty());
            representativeInfo.put("phone", deputy.getPhone());
            representativeInfo.put("station", deputy.getStationName());
            representativeInfo.put("group", deputy.getGroupName());
            representativeInfo.put("avatar", deputy.getAvatar());
            representativeInfo.put("qrcodeUrl", deputy.getQrcodeUrl());
            representativeInfo.put("resume", deputy.getResume());

            // 查询该代表相关的意见建议（只查询该代表所在联络站的意见建议）
            RdAdvice queryAdvice = new RdAdvice();
            queryAdvice.setDeputyId(deputyId);
            List<AdviceExtVO> adviceList = adviceService.selectAdviceExtList(queryAdvice);

            // 构造履职档案数据
            List<Map<String, Object>> opinions = new ArrayList<>();
            for (AdviceExtVO advice : adviceList) {
                Map<String, Object> opinion = new HashMap<>();
                opinion.put("id", advice.getAdviceId());
                opinion.put("category", advice.getCategory());
                opinion.put("title", advice.getTitle());
                opinion.put("content", advice.getContent());
                opinion.put("submitter", advice.getName());
                opinion.put("contact", advice.getPhone());
                opinion.put("submitDate", advice.getCreateTime());
                opinion.put("status", advice.getStatus()); // 保持原始状态值
                opinion.put("rating", advice.getServiceRating()); // 添加群众服务评分

                // 获取反馈信息
                List<FeedbackInfoVO> feedbackList = feedbackService.selectFeedbackListWithAttachs(advice.getAdviceId());
                if (!feedbackList.isEmpty()) {
                    // 获取代表的反馈作为主要反馈
                    FeedbackInfoVO representativeFeedback = feedbackList.stream()
                            .filter(f -> f.getUserType() == 0) // 0表示代表
                            .findFirst()
                            .orElse(feedbackList.get(0));

                    Map<String, Object> feedback = new HashMap<>();
                    feedback.put("representative", representativeFeedback.getName());
                    feedback.put("date", representativeFeedback.getCreateTime());
                    feedback.put("content", representativeFeedback.getContent());
                    opinion.put("feedback", feedback);

                    // 获取对话回复（群众和代表的后续交流）
                    if (feedbackList.size() > 1) {
                        List<Map<String, Object>> replies = new ArrayList<>();
                        int replyId = 1;
                        for (FeedbackInfoVO feedbackInfo : feedbackList) {
                            // 跳过第一个反馈（已作为主要反馈）
                            if (feedbackInfo.equals(representativeFeedback)) {
                                continue;
                            }

                            Map<String, Object> reply = new HashMap<>();
                            reply.put("id", replyId++);
                            reply.put("type", feedbackInfo.getUserType() == 1 ? "citizen" : "representative");
                            reply.put("author", feedbackInfo.getName());
                            reply.put("date", feedbackInfo.getCreateTime());
                            reply.put("content", feedbackInfo.getContent());
                            replies.add(reply);
                        }
                        opinion.put("replies", replies);
                    }
                }

                opinions.add(opinion);
            }

            // 构造返回数据
            Map<String, Object> result = new HashMap<>();
            result.put("representative", representativeInfo);
            result.put("opinions", opinions);

            return success(result);
        }
        catch (Exception e)
        {
            logger.error("获取代表详情失败", e);
            return error("获取代表详情失败：" + e.getMessage());
        }
    }

    /**
     * 根据分类ID获取分类名称
     */
    private String getCategoryNameById(String categoryId) {
        try {
            SysDictData queryData = new SysDictData();
            queryData.setDictType("rd_advice_category");
            queryData.setDictValue(categoryId);
            queryData.setStatus("0");
            List<SysDictData> list = dictDataService.selectDictDataList(queryData);
            if (!list.isEmpty()) {
                return list.get(0).getDictLabel();
            }
        } catch (Exception e) {
            logger.error("根据分类ID获取分类名称失败", e);
        }
        return null;
    }

    /**
     * 获取热门政策列表
     */
    @Anonymous
    @GetMapping("/getHotPolicies/{stationId}")
    public AjaxResult getHotPolicies(@PathVariable String stationId)
    {
        if (StringUtils.isEmpty(stationId))
        {
            return error("联络站ID不能为空");
        }

        try
        {
            Long stationIdLong = null;
            if (!"0".equals(stationId)) {
                stationIdLong = Long.parseLong(stationId);
            }

            // 构造查询条件
            RdStationPolicy queryPolicy = new RdStationPolicy();
            if (stationIdLong != null) {
                queryPolicy.setStationId(stationIdLong);
            }
            queryPolicy.setIsTop("1"); // 只查询置顶的政策
            queryPolicy.setStatus(1); // 只查询上线的政策

            // 查询政策列表
            List<RdStationPolicy> policyList = stationPolicyService.selectRdStationPolicyList(queryPolicy);

            // 按发布时间倒序排序，取前6条
            policyList.sort((a, b) -> {
                if (a.getPublishTime() == null) return 1;
                if (b.getPublishTime() == null) return -1;
                return b.getPublishTime().compareTo(a.getPublishTime());
            });

            List<RdStationPolicy> hotPolicies = policyList.stream()
                    .limit(6)
                    .collect(Collectors.toList());

            // 构造返回数据
            List<Map<String, Object>> result = new ArrayList<>();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

            for (RdStationPolicy policy : hotPolicies) {
                Map<String, Object> policyData = new HashMap<>();
                policyData.put("id", policy.getId());
                policyData.put("title", policy.getTitle());
                policyData.put("summary", policy.getSummary());
                policyData.put("publishTime", policy.getPublishTime() != null ? dateFormat.format(policy.getPublishTime()) : "");
                policyData.put("viewCount", policy.getViewCount() != null ? policy.getViewCount() : 0);
                policyData.put("type", policy.getType());
                policyData.put("status", policy.getStatus());
                policyData.put("department", policy.getDepartment());
                result.add(policyData);
            }

            return success(result);
        }
        catch (NumberFormatException e)
        {
            logger.error("联络站ID格式错误", e);
            return error("联络站ID格式错误");
        }
        catch (Exception e)
        {
            logger.error("获取热门政策失败", e);
            return error("获取热门政策失败：" + e.getMessage());
        }
    }

    /**
     * 获取最新动态列表
     */
    @Anonymous
    @GetMapping("/getLatestNews/{stationId}")
    public AjaxResult getLatestNews(@PathVariable String stationId)
    {
        if (StringUtils.isEmpty(stationId))
        {
            return error("联络站ID不能为空");
        }

        try
        {
            Long stationIdLong = null;
            if (!"0".equals(stationId)) {
                stationIdLong = Long.parseLong(stationId);
            }

            // 构造查询条件
            RdStationNews queryNews = new RdStationNews();
            if (stationIdLong != null) {
                queryNews.setStationId(stationIdLong);
            }
            queryNews.setStatus(1); // 只查询上线的动态
            queryNews.setIsTop("1"); // 只查询置顶的动态

            // 查询动态列表
            List<RdStationNews> newsList = stationNewsService.selectRdStationNewsList(queryNews);

            // 按发布时间倒序排序，取前6条
            newsList.sort((a, b) -> {
                if (a.getPublishTime() == null) return 1;
                if (b.getPublishTime() == null) return -1;
                return b.getPublishTime().compareTo(a.getPublishTime());
            });

            List<RdStationNews> latestNews = newsList.stream()
                    .limit(6)
                    .collect(Collectors.toList());

            // 构造返回数据
            List<Map<String, Object>> result = new ArrayList<>();
            SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
            SimpleDateFormat dayFormat = new SimpleDateFormat("dd");
            SimpleDateFormat monthFormat = new SimpleDateFormat("MM月");

            for (RdStationNews news : latestNews) {
                Map<String, Object> newsData = new HashMap<>();
                newsData.put("id", news.getId());
                newsData.put("title", news.getTitle());
                newsData.put("summary", news.getSummary());
                newsData.put("content", news.getContent());
                newsData.put("type", news.getType());
                newsData.put("publisher", news.getPublisher());
                newsData.put("publishTime", news.getPublishTime() != null ? timeFormat.format(news.getPublishTime()) : "");
                newsData.put("day", news.getPublishTime() != null ? dayFormat.format(news.getPublishTime()) : "");
                newsData.put("month", news.getPublishTime() != null ? monthFormat.format(news.getPublishTime()) : "");
                newsData.put("image", StringUtils.isNotEmpty(news.getCoverUrl()) ? news.getCoverUrl() : getDefaultNewsImage());
                newsData.put("viewCount", news.getViewCount() != null ? news.getViewCount() : 0);
                result.add(newsData);
            }

            return success(result);
        }
        catch (NumberFormatException e)
        {
            logger.error("联络站ID格式错误", e);
            return error("联络站ID格式错误");
        }
        catch (Exception e)
        {
            logger.error("获取最新动态失败", e);
            return error("获取最新动态失败：" + e.getMessage());
        }
    }

    /**
     * 获取政策列表（分页）
     */
    @Anonymous
    @GetMapping("/getPolicyList")
    public AjaxResult getPolicyList(
            @RequestParam(required = false) String stationId,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String timeFilter,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize)
    {
        try
        {
            Long stationIdLong = null;
            if (StringUtils.isNotEmpty(stationId) && !"0".equals(stationId)) {
                stationIdLong = Long.parseLong(stationId);
            }

            // 构造查询条件
            RdStationPolicy queryPolicy = new RdStationPolicy();
            if (stationIdLong != null) {
                queryPolicy.setStationId(stationIdLong);
            }

            // 类型筛选
            if (StringUtils.isNotEmpty(type) && !"all".equals(type)) {
                try {
                    queryPolicy.setType(Integer.parseInt(type));
                } catch (NumberFormatException e) {
                    logger.warn("政策类型格式错误: {}", type);
                }
            }

            // 关键词搜索
            if (StringUtils.isNotEmpty(keyword)) {
                queryPolicy.setTitle(keyword);
            }

            queryPolicy.setStatus(1); // 只查询上线的政策

            // 查询政策列表
            List<RdStationPolicy> allPolicies = stationPolicyService.selectRdStationPolicyList(queryPolicy);

            // 时间筛选
            if (StringUtils.isNotEmpty(timeFilter) && !"all".equals(timeFilter)) {
                allPolicies = filterPoliciesByTime(allPolicies, timeFilter);
            }

            // 按发布时间倒序排序
            allPolicies.sort((a, b) -> {
                if (a.getPublishTime() == null) return 1;
                if (b.getPublishTime() == null) return -1;
                return b.getPublishTime().compareTo(a.getPublishTime());
            });

            // 手动分页
            int total = allPolicies.size();
            int startIndex = (pageNum - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);

            List<RdStationPolicy> pagedPolicies = new ArrayList<>();
            if (startIndex < total) {
                pagedPolicies = allPolicies.subList(startIndex, endIndex);
            }

            // 构造返回数据
            List<Map<String, Object>> result = new ArrayList<>();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

            for (RdStationPolicy policy : pagedPolicies) {
                Map<String, Object> policyData = new HashMap<>();
                policyData.put("id", policy.getId());
                policyData.put("title", policy.getTitle());
                policyData.put("summary", policy.getSummary());
                policyData.put("publishTime", policy.getPublishTime() != null ? dateFormat.format(policy.getPublishTime()) : "");
                policyData.put("viewCount", policy.getViewCount() != null ? policy.getViewCount() : 0);
                policyData.put("type", policy.getType());
                policyData.put("department", policy.getDepartment());
                policyData.put("status", policy.getStatus());
                result.add(policyData);
            }

            Map<String, Object> response = new HashMap<>();
            response.put("list", result);
            response.put("total", total);
            response.put("pageNum", pageNum);
            response.put("pageSize", pageSize);
            response.put("totalPages", (int) Math.ceil((double) total / pageSize));

            return success(response);
        }
        catch (NumberFormatException e)
        {
            logger.error("联络站ID格式错误", e);
            return error("联络站ID格式错误");
        }
        catch (Exception e)
        {
            logger.error("获取政策列表失败", e);
            return error("获取政策列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据时间筛选政策列表
     */
    private List<RdStationPolicy> filterPoliciesByTime(List<RdStationPolicy> policies, String timeFilter) {
        Calendar calendar = Calendar.getInstance();
        Date currentDate = calendar.getTime();
        Date filterStartDate;

        switch (timeFilter) {
            case "week":
                calendar.add(Calendar.DAY_OF_YEAR, -7);
                filterStartDate = calendar.getTime();
                break;
            case "month":
                calendar.add(Calendar.MONTH, -1);
                filterStartDate = calendar.getTime();
                break;
            case "quarter":
                calendar.add(Calendar.MONTH, -3);
                filterStartDate = calendar.getTime();
                break;
            case "year":
                calendar.add(Calendar.YEAR, -1);
                filterStartDate = calendar.getTime();
                break;
            default:
                return policies; // 如果时间筛选条件不匹配，返回原列表
        }

        final Date startDate = filterStartDate;
        return policies.stream()
                .filter(policy -> {
                    Date publishTime = policy.getPublishTime();
                    return publishTime != null &&
                           !publishTime.before(startDate) &&
                           !publishTime.after(currentDate);
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取政策详情
     */
    @Anonymous
    @GetMapping("/getPolicyDetail/{policyId}")
    public AjaxResult getPolicyDetail(@PathVariable Long policyId)
    {
        if (policyId == null)
        {
            return error("政策ID不能为空");
        }

        try
        {
            // 查询政策详情
            RdStationPolicy policy = stationPolicyService.selectRdStationPolicyById(policyId);
            if (policy == null) {
                return error("政策信息不存在");
            }

            // 构造政策信息
            Map<String, Object> policyInfo = new HashMap<>();
            policyInfo.put("id", policy.getId());
            policyInfo.put("title", policy.getTitle());
            policyInfo.put("department", policy.getDepartment());

            // 格式化日期为字符串便于前端处理
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            policyInfo.put("publishTime", policy.getPublishTime() != null ? dateFormat.format(policy.getPublishTime()) : null);
            policyInfo.put("implementTime", policy.getImplementTime() != null ? dateFormat.format(policy.getImplementTime()) : null);
            policyInfo.put("validPeriod", policy.getValidPeriod());
            policyInfo.put("viewCount", policy.getViewCount() != null ? policy.getViewCount() : 0);
            policyInfo.put("type", policy.getType());

            // 处理政策内容
            List<Map<String, Object>> policyContent = new ArrayList<>();
            if (StringUtils.isNotEmpty(policy.getContent())) {
                // 简单按段落分割内容，实际可以根据具体格式处理
                String[] sections = policy.getContent().split("\n\n");
                for (int i = 0; i < sections.length; i++) {
                    String section = sections[i].trim();
                    if (StringUtils.isNotEmpty(section)) {
                        Map<String, Object> sectionData = new HashMap<>();
                        sectionData.put("id", i + 1);
                        // 如果段落以数字或中文序号开头，提取作为标题
                        if (section.matches("^[一二三四五六七八九十]+[、.].*") ||
                            section.matches("^\\d+[、.].*") ||
                            section.matches("^\\([一二三四五六七八九十]+\\).*") ||
                            section.matches("^\\(\\d+\\).*")) {
                            int titleEnd = section.indexOf('\n');
                            if (titleEnd > 0) {
                                sectionData.put("title", section.substring(0, titleEnd));
                                sectionData.put("content", "<p>" + section.substring(titleEnd + 1).replace("\n", "</p><p>") + "</p>");
                            } else {
                                sectionData.put("title", "第" + (i + 1) + "条");
                                sectionData.put("content", "<p>" + section.replace("\n", "</p><p>") + "</p>");
                            }
                        } else {
                            sectionData.put("title", "第" + (i + 1) + "条");
                            sectionData.put("content", "<p>" + section.replace("\n", "</p><p>") + "</p>");
                        }
                        policyContent.add(sectionData);
                    }
                }
            }

            // 如果没有内容分段，创建一个默认段落
            if (policyContent.isEmpty() && StringUtils.isNotEmpty(policy.getContent())) {
                Map<String, Object> defaultSection = new HashMap<>();
                defaultSection.put("id", 1);
                defaultSection.put("title", "政策内容");
                defaultSection.put("content", "<p>" + policy.getContent().replace("\n", "</p><p>") + "</p>");
                policyContent.add(defaultSection);
            }

            // 更新浏览量
            policy.setViewCount((policy.getViewCount() != null ? policy.getViewCount() : 0) + 1);
            stationPolicyService.updateRdStationPolicy(policy);

            Map<String, Object> result = new HashMap<>();
            result.put("policyInfo", policyInfo);
            result.put("policyContent", policyContent);

            return success(result);
        }
        catch (Exception e)
        {
            logger.error("获取政策详情失败", e);
            return error("获取政策详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取新闻动态列表（分页）
     */
    @Anonymous
    @GetMapping("/getNewsList")
    public AjaxResult getNewsList(
            @RequestParam(required = false) String stationId,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String timeFilter,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "12") Integer pageSize)
    {
        try
        {
            Long stationIdLong = null;
            if (StringUtils.isNotEmpty(stationId) && !"0".equals(stationId)) {
                stationIdLong = Long.parseLong(stationId);
            }

            // 构造查询条件
            RdStationNews queryNews = new RdStationNews();
            if (stationIdLong != null) {
                queryNews.setStationId(stationIdLong);
            }

            // 类型筛选
            if (StringUtils.isNotEmpty(type) && !"all".equals(type)) {
                try {
                    queryNews.setType(Integer.parseInt(type));
                } catch (NumberFormatException e) {
                    logger.warn("新闻类型格式错误: {}", type);
                }
            }

            // 关键词搜索
            if (StringUtils.isNotEmpty(keyword)) {
                queryNews.setTitle(keyword);
            }

            queryNews.setStatus(1); // 只查询上线的动态

            // 查询新闻列表
            List<RdStationNews> allNews = stationNewsService.selectRdStationNewsList(queryNews);

            // 时间筛选
            if (StringUtils.isNotEmpty(timeFilter) && !"all".equals(timeFilter)) {
                allNews = filterNewsByTime(allNews, timeFilter);
            }

            // 按发布时间倒序排序
            allNews.sort((a, b) -> {
                if (a.getPublishTime() == null) return 1;
                if (b.getPublishTime() == null) return -1;
                return b.getPublishTime().compareTo(a.getPublishTime());
            });

            // 手动分页
            int total = allNews.size();
            int startIndex = (pageNum - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);

            List<RdStationNews> pagedNews = new ArrayList<>();
            if (startIndex < total) {
                pagedNews = allNews.subList(startIndex, endIndex);
            }

            // 构造返回数据
            List<Map<String, Object>> result = new ArrayList<>();
            SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
            SimpleDateFormat dayFormat = new SimpleDateFormat("dd");
            SimpleDateFormat monthFormat = new SimpleDateFormat("MM月");

            for (RdStationNews news : pagedNews) {
                Map<String, Object> newsData = new HashMap<>();
                newsData.put("id", news.getId());
                newsData.put("title", news.getTitle());
                newsData.put("content", StringUtils.isNotEmpty(news.getSummary()) ? news.getSummary() :
                    (StringUtils.isNotEmpty(news.getContent()) ?
                        (news.getContent().length() > 100 ? news.getContent().substring(0, 100) + "..." : news.getContent())
                        : ""));
                newsData.put("source", StringUtils.isNotEmpty(news.getPublisher()) ? news.getPublisher() : "系统发布");
                newsData.put("type", news.getType() != null ? news.getType().toString() : "1");
                newsData.put("time", news.getPublishTime() != null ? timeFormat.format(news.getPublishTime()) : "");
                newsData.put("day", news.getPublishTime() != null ? dayFormat.format(news.getPublishTime()) : "");
                newsData.put("month", news.getPublishTime() != null ? monthFormat.format(news.getPublishTime()) : "");
                newsData.put("readCount", news.getViewCount() != null ? news.getViewCount() : 0);
                newsData.put("likeCount", (int)(Math.random() * 100) + 10); // 模拟点赞数
                newsData.put("image", StringUtils.isNotEmpty(news.getCoverUrl()) ? news.getCoverUrl() : getDefaultNewsImage());
                result.add(newsData);
            }

            Map<String, Object> response = new HashMap<>();
            response.put("list", result);
            response.put("total", total);
            response.put("pageNum", pageNum);
            response.put("pageSize", pageSize);
            response.put("totalPages", (int) Math.ceil((double) total / pageSize));

            return success(response);
        }
        catch (NumberFormatException e)
        {
            logger.error("联络站ID格式错误", e);
            return error("联络站ID格式错误");
        }
        catch (Exception e)
        {
            logger.error("获取新闻动态列表失败", e);
            return error("获取新闻动态列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据时间筛选新闻列表
     */
    private List<RdStationNews> filterNewsByTime(List<RdStationNews> newsList, String timeFilter) {
        Calendar calendar = Calendar.getInstance();
        Date currentDate = calendar.getTime();
        Date filterStartDate;

        switch (timeFilter) {
            case "today":
                // 今天0点
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                filterStartDate = calendar.getTime();
                break;
            case "week":
                calendar.add(Calendar.DAY_OF_YEAR, -7);
                filterStartDate = calendar.getTime();
                break;
            case "month":
                calendar.add(Calendar.MONTH, -1);
                filterStartDate = calendar.getTime();
                break;
            case "quarter":
                calendar.add(Calendar.MONTH, -3);
                filterStartDate = calendar.getTime();
                break;
            default:
                return newsList; // 如果时间筛选条件不匹配，返回原列表
        }

        final Date startDate = filterStartDate;
        return newsList.stream()
                .filter(news -> {
                    Date publishTime = news.getPublishTime();
                    return publishTime != null &&
                           !publishTime.before(startDate) &&
                           !publishTime.after(currentDate);
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取新闻动态详情
     */
    @Anonymous
    @GetMapping("/getNewsDetail/{newsId}")
    public AjaxResult getNewsDetail(@PathVariable Long newsId)
    {
        if (newsId == null)
        {
            return error("新闻ID不能为空");
        }

        try
        {
            // 查询新闻详情
            RdStationNews news = stationNewsService.selectRdStationNewsById(newsId);
            if (news == null) {
                return error("新闻信息不存在");
            }

            // 构造新闻信息
            Map<String, Object> newsInfo = new HashMap<>();
            newsInfo.put("id", news.getId());
            newsInfo.put("title", news.getTitle());
            newsInfo.put("content", news.getContent());
            newsInfo.put("summary", news.getSummary());
            newsInfo.put("publisher", news.getPublisher());
            newsInfo.put("type", news.getType());
            newsInfo.put("coverUrl", news.getCoverUrl());
            newsInfo.put("viewCount", news.getViewCount() != null ? news.getViewCount() : 0);

            // 格式化日期
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            newsInfo.put("publishTime", news.getPublishTime() != null ? dateFormat.format(news.getPublishTime()) : null);

            // 更新浏览量
            news.setViewCount((news.getViewCount() != null ? news.getViewCount() : 0) + 1);
            stationNewsService.updateRdStationNews(news);

            return success(newsInfo);
        }
        catch (Exception e)
        {
            logger.error("获取新闻详情失败", e);
            return error("获取新闻详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取默认新闻图片
     */
    private String getDefaultNewsImage() {
        String[] defaultImages = {
            "https://images.unsplash.com/photo-1749802449762-5e428ccf9a45?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHx0b3BpYy1mZWVkfDJ8NnNNVmpUTFNrZVF8fGVufDB8fHx8fA%3D%3D",
            "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=250&fit=crop",
            "https://plus.unsplash.com/premium_photo-1720971628524-f4d6e44c96a4?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHx0b3BpYy1mZWVkfDl8NnNNVmpUTFNrZVF8fGVufDB8fHx8fA%3D%3D",
            "https://images.unsplash.com/photo-1531297484001-80022131f5a1?w=400&h=250&fit=crop",
            "https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=400&h=250&fit=crop",
            "https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=400&h=250&fit=crop"
        };
        return defaultImages[(int)(Math.random() * defaultImages.length)];
    }
}
