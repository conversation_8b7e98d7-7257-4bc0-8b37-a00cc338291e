<template>
  <div class="representative-detail">
    <app-header :showBackBtn="true" />

    <main class="content" v-if="!loading">
      <div class="detail-container" v-if="representative">
        <div class="left-panel">
          <div class="photo-section">
            <div class="photo-frame">
              <img :src="getPhotoUrl()" alt="代表照片" class="representative-photo" @error="handleImageError" />
            </div>
          </div>
          <div class="qr-section">
            <div class="qr-frame">
              <img :src="getQrUrl()" alt="二维码" class="qr-code" @error="handleQRCodeError" />
            </div>
            <p class="qr-text">扫码提建议</p>
          </div>
        </div>

        <div class="right-panel">
          <!-- 代表信息栏 -->
          <div class="info-section">
            <div class="info-header">
              <h2><i class="el-icon-user-solid"></i>代表信息</h2>
            </div>

            <div class="representative-card">
              <!-- 顶部名片区域 -->
              <div class="profile-header">
                <div class="name-area">
                  <h3 class="rep-name">{{ representative.name }}</h3>
                  <div class="rep-badges">
                    <span class="unit-badge">{{ representative.unit }}</span>
                    <span class="position-badge">{{ representative.position }}</span>
                  </div>
                </div>
                <div class="status-area">
                  <div class="status-badge">
                    <span class="status-icon">✓</span>
                    <span class="status-text">在职履职</span>
                  </div>
                </div>
              </div>

              <!-- 详细信息网格 -->
              <div class="info-grid">
                <div class="info-item">
                  <div class="info-icon-wrapper">
                    <span class="info-icon">📞</span>
                  </div>
                  <div class="info-details">
                    <span class="info-label">联系电话</span>
                    <span class="info-value">{{ representative.phone }}</span>
                  </div>
                </div>

                <div class="info-item">
                  <div class="info-icon-wrapper">
                    <span class="info-icon">👥</span>
                  </div>
                  <div class="info-details">
                    <span class="info-label">民族</span>
                    <span class="info-value">汉族</span>
                  </div>
                </div>

                <div class="info-item">
                  <div class="info-icon-wrapper">
                    <span class="info-icon">🏢</span>
                  </div>
                  <div class="info-details">
                    <span class="info-label">所属站点</span>
                    <span class="info-value">{{ representative.station }}</span>
                  </div>
                </div>

                <div class="info-item">
                  <div class="info-icon-wrapper">
                    <span class="info-icon">📍</span>
                  </div>
                  <div class="info-details">
                    <span class="info-label">所属代表团</span>
                    <span class="info-value">{{ representative.group }}</span>
                  </div>
                </div>
              </div>

              <!-- 简介区域 -->
              <div class="biography-section">
                <div class="bio-title">
                  <span class="bio-icon">📝</span>
                  <span>代表简介</span>
                </div>
                <div class="bio-content">
                  <p class="bio-text">
                    {{ representative.resume || `${representative.name}同志，现任${representative.unit}${representative.position}，长期致力于基层工作，深入了解民情民意。在担任人大代表期间，积极履职尽责，关注民生问题，多次提出切实可行的建议和提案。特别在城市建设、环境保护、教育发展等领域有着丰富的经验和独到的见解，深受群众信赖和好评。` }}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- 处理意见 -->
          <div class="opinions-section">
            <div class="info-header">
              <h2><i class="el-icon-chat-line-square"></i>履职档案</h2>
            </div>

                        <div class="opinions-content">
              <!-- 加载状态 -->
              <div v-if="loading" class="loading-opinions">
                <div class="loading-text">正在加载履职档案...</div>
              </div>

              <!-- 有履职档案数据时显示 -->
              <div v-else-if="representativeOpinions.length > 0">
                <div class="opinion-card" v-for="opinion in representativeOpinions" :key="opinion.id">
                  <div class="opinion-header">
                    <div class="opinion-meta">
                      <span class="opinion-id">#{{ opinion.id }}</span>
                      <span class="opinion-category">{{ getCategoryName(opinion.category) }}</span>
                      <span class="opinion-date">{{ formatDate(opinion.submitDate) }}</span>
                      <span class="opinion-status" :class="getStatusName(opinion.status)">{{ getStatusName(opinion.status) }}</span>
                    </div>
                    <!-- 已处理意见显示群众服务评分 -->
                    <div class="service-rating" v-if="opinion.status === '2' && opinion.rating">
                      <div class="rating-stars">
                        <span v-for="n in 5" :key="n" class="star" :class="{ filled: n <= (opinion.rating || 0) }">★</span>
                      </div>
                      <span class="rating-text">{{ opinion.rating }}分</span>
                    </div>
                  </div>

                  <div class="opinion-content">
                    <h4>{{ opinion.title }}</h4>
                    <p class="opinion-text">{{ opinion.content }}</p>
                    <div class="opinion-submitter">
                      <span>提交人：{{ opinion.submitter }}</span>
                      <span>联系方式：{{ opinion.contact }}</span>
                    </div>
                  </div>

                  <!-- 代表反馈 -->
                  <div class="feedback-section" v-if="opinion.feedback">
                    <div class="feedback-header">
                      <h5>代表反馈</h5>
                      <div class="feedback-meta">
                        <span>{{ opinion.feedback.representative }}</span>
                        <span>{{ formatDate(opinion.feedback.date) }}</span>
                      </div>
                    </div>
                    <div class="feedback-content">
                      <p>{{ opinion.feedback.content }}</p>
                    </div>
                  </div>

                  <!-- 对话回复 -->
                  <div class="replies-section" v-if="opinion.replies && opinion.replies.length > 0">
                    <div class="replies-header">
                      <h6>对话交流</h6>
                    </div>
                    <div class="replies-list">
                      <div
                        class="reply-item"
                        v-for="reply in opinion.replies"
                        :key="reply.id"
                        :class="reply.type"
                      >
                        <div class="reply-header">
                          <span class="reply-author">{{ reply.author }}</span>
                          <span class="reply-date">{{ formatDate(reply.date) }}</span>
                        </div>
                        <div class="reply-content">{{ reply.content }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 无履职档案数据时显示空状态 -->
              <div v-else class="no-opinions-container">
                <div class="no-opinions-content">
                  <i class="el-icon-document"></i>
                  <p>该代表暂无履职档案</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 无数据状态 -->
      <div v-else class="no-data-container">
        <div class="no-data-content">
          <i class="el-icon-warning"></i>
          <p>未找到代表信息</p>
        </div>
      </div>
    </main>

    <!-- 加载状态 -->
    <main class="content loading-content" v-else>
      <div class="loading-container">
        <div class="loading-spinner"></div>
        <p>正在加载代表信息...</p>
      </div>
    </main>

    <!-- 右下角返回按钮 -->
    <div class="back-button" @click="goBack">
      <span>返回</span>
    </div>
  </div>
</template>

<script>
import { getRepresentativeDetail } from '@/api/renda/stationscreen'
import AppHeader from '@/components/StationScreen/AppHeader.vue'

export default {
  name: 'RepresentativeDetail',
  components: {
    AppHeader
  },
  dicts: ['rd_advice_category', 'rd_advice_status'],
  data() {
    return {
      representative: null,
      representativeOpinions: [],
      deputyId: null,
      loading: false
    }
  },
  methods: {
    // 获取代表详情数据
    async fetchRepresentativeDetail() {

      // 清空之前的数据
      this.representative = null
      this.representativeOpinions = []

      try {
        this.loading = true

        const response = await getRepresentativeDetail(this.deputyId)

        if (response.code === 200) {
          this.representative = response.data.representative
          this.representativeOpinions = response.data.opinions || []
        } else {
          console.error('fetchRepresentativeDetail: API返回错误', response.msg)
          this.$message.error(response.msg || '获取代表详情失败')
        }
      } catch (error) {
        console.error('fetchRepresentativeDetail: 请求失败', error)
        this.$message.error('获取代表详情失败')
      } finally {
        this.loading = false
      }
    },

    goBack() {
      this.$router.go(-1);
    },

    // 获取图片URL
    getPhotoUrl() {
      // 如果有头像路径，返回完整URL
      if (this.representative && this.representative.avatar) {
        // 如果是完整URL，直接返回
        if (this.representative.avatar.startsWith('http://') || this.representative.avatar.startsWith('https://')) {
          return this.representative.avatar
        }
        // 如果是相对路径，拼接基础URL
        return process.env.VUE_APP_BASE_API + this.representative.avatar
      }
      // 没有头像时使用默认图片
      return require('@/assets/deputy/someone.jpg')
    },

    // 获取二维码URL
    getQrUrl() {
      // 如果有二维码路径，返回完整URL
      if (this.representative && this.representative.qrcodeUrl) {
        // 如果是完整URL，直接返回
        if (this.representative.qrcodeUrl.startsWith('http://') || this.representative.qrcodeUrl.startsWith('https://')) {
          return this.representative.qrcodeUrl
        }
        // 如果是相对路径，拼接基础URL
        return process.env.VUE_APP_BASE_API + this.representative.qrcodeUrl
      }
      // 没有二维码时使用默认图片
      return require('@/assets/deputy/qrcode.png')
    },

    // 处理图片加载错误
    handleImageError(event) {
      console.warn('图片加载失败，使用默认图片')
      event.target.src = require('@/assets/deputy/someone.jpg')
    },

    // 处理二维码加载错误
    handleQRCodeError(event) {
      console.warn('二维码加载失败，使用默认二维码')
      event.target.src = require('@/assets/deputy/qrcode.png')
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return ''

      try {
        const date = new Date(dateString)

        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          return dateString
        }

        // 格式化为 YYYY-MM-DD HH:mm 格式
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')

        return `${year}-${month}-${day} ${hours}:${minutes}`
      } catch (error) {
        console.error('日期格式化失败:', error)
        return dateString
      }
    },

    // 获取建议类别名称
    getCategoryName(categoryValue) {
      if (!categoryValue || !this.dict.type.rd_advice_category) return categoryValue
      const category = this.dict.type.rd_advice_category.find(item => item.value === categoryValue)
      return category ? category.label : categoryValue
    },

    // 获取建议状态名称
    getStatusName(statusValue) {
      if (!statusValue || !this.dict.type.rd_advice_status) return statusValue
      const status = this.dict.type.rd_advice_status.find(item => item.value === statusValue)
      return status ? status.label : statusValue
    }
  },
  async mounted() {

    this.$nextTick(() => {
      // 禁用右键菜单
      document.oncontextmenu = function() {
        return false;
      };
    });

    // 获取代表Id
    this.deputyId = this.$route.query.deputyId || this.$route.params.deputyId || '0'

    // 获取代表详情数据
    if (this.deputyId && this.deputyId !== '0') {
      await this.fetchRepresentativeDetail()
    } else {
      console.error('mounted: deputyId无效', this.deputyId)
      this.$message.error('未找到代表信息')
    }
  },

  // keep-alive组件激活时
  activated() {
    // 重新获取deputyId，防止路由参数变化未更新
    const newDeputyId = this.$route.query.deputyId || this.$route.params.deputyId || '1'
    if (newDeputyId !== this.deputyId) {
      this.deputyId = newDeputyId
    }

    if (this.deputyId && this.deputyId !== '0') {
      this.fetchRepresentativeDetail()
    } else {
      console.warn('activated: deputyId无效', this.deputyId)
    }
  },

  // 路由进入前
  beforeRouteEnter(to, from, next) {
    next(vm => {
      const deputyId = to.query.deputyId || to.params.deputyId || '0'

      vm.deputyId = deputyId
      if (deputyId && deputyId !== '0') {
        vm.fetchRepresentativeDetail()
      } else {
        vm.$message.error('未找到代表信息')
      }
    })
  },

  // 路由更新时
  beforeRouteUpdate(to, from, next) {
    const oldDeputyId = this.deputyId
    const newDeputyId = to.query.deputyId || to.params.deputyId || '0'

    this.deputyId = newDeputyId
    if (this.deputyId && this.deputyId !== '0') {
      this.fetchRepresentativeDetail()
    } else {
      this.$message.error('未找到代表信息')
    }
    next()
  },
}
</script>

<style lang="scss" scoped>
.representative-detail {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-size: 18px;
  overflow: hidden;
  font-family: 'AL-R' !important;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  margin: 0;
  padding: 0;
}

.content {
  flex: 1;
  padding: 40px;
  display: flex;
  min-height: 0;

  .detail-container {
    flex: 1;
    display: flex;
    background: rgba(255, 255, 255, 0.98);
    border-radius: 24px;
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.12);
    overflow: hidden;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-height: 0;
  }

  .left-panel {
    width: 450px;
    background: linear-gradient(145deg, #d71718, #a01012);
    padding: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    overflow-y: auto;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(145deg, rgba(215, 23, 24, 0.9), rgba(160, 16, 18, 0.9));
      backdrop-filter: blur(5px);
    }

    > * {
      position: relative;
      z-index: 1;
    }

    .photo-section {
      margin-top: 20px;
      margin-bottom: 40px;

      .photo-frame {
        padding: 10px;
        background: linear-gradient(145deg, #ffffff, #f0f0f0);
        border-radius: 24px;
        box-shadow: 0 20px 50px rgba(0, 0, 0, 0.25);
        transition: transform 0.3s ease;

        &:hover {
          transform: translateY(-8px) scale(1.02);
        }

        .representative-photo {
          width: 280px;
          height: 373px;
          object-fit: cover;
          border-radius: 18px;
          display: block;
        }
      }
    }

    .qr-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 30px;

      .qr-frame {
        padding: 12px !important;
        background: linear-gradient(145deg, #ffffff, #f0f0f0);
        border-radius: 24px;
        box-shadow: 0 20px 50px rgba(0, 0, 0, 0.25);
        transition: transform 0.3s ease;

        &:hover {
          transform: translateY(-5px) scale(1.05);
        }

        .qr-code {
          width: 180px;
          height: 180px;
          object-fit: cover;
          border-radius: 12px;
          display: block;
        }
      }

      .qr-text {
        margin-top: 20px;
        color: #fff;
        font-size: 20px;
        font-weight: 400;
        text-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
      }
    }
  }

  .right-panel {
    flex: 1;
    padding: 40px;
    display: flex;
    flex-direction: column;
    gap: 40px;
    overflow: hidden;
    min-height: 0;

    .info-section {
      flex-shrink: 0;

      .info-header {
        border-bottom: 4px solid #d71718;
        margin-bottom: 30px;
        padding-bottom: 15px;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: -4px;
          left: 0;
          width: 80px;
          height: 4px;
          background: linear-gradient(90deg, #d71718, #ff4444);
        }

        h2 {
          color: #d71718;
          font-size: 36px;
          margin: 0;
          font-weight: 600;
          display: flex;
          align-items: center;
          gap: 15px;

          i {
            font-size: 32px;
          }
        }
      }

      .representative-card {
        background: linear-gradient(145deg, #ffffff, #f8f9fa);
        padding: 20px;
        border-radius: 24px;
        box-shadow: 0 15px 45px rgba(0, 0, 0, 0.12);
        border-left: 8px solid #d71718;
        display: flex;
        flex-direction: column;
        gap: 15px;

        .profile-header {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          gap: 25px;

          .name-area {
            flex: 1;

            .rep-name {
              font-size: 64px;
              font-weight: 700;
              color: #d71718;
              margin: 12px 0;
              text-shadow: 0 2px 4px rgba(215, 23, 24, 0.1);
              text-align: left;
            }

            .rep-badges {
              display: flex;
              gap: 12px;
              flex-wrap: wrap;

              .position-badge,
              .unit-badge {
                padding: 12px 20px;
                border-radius: 20px;
                background: linear-gradient(145deg, #d71718, #a01012);
                color: #fff;
                font-size: 18px;
                font-weight: 500;
                box-shadow: 0 4px 15px rgba(215, 23, 24, 0.3);
                transition: transform 0.2s ease;

                &:hover {
                  transform: translateY(-2px);
                }
              }
            }
          }

          .status-area {
            .status-badge {
              display: flex;
              align-items: center;
              gap: 8px;
              background: linear-gradient(145deg, #28a745, #20c997);
              color: #fff;
              padding: 12px 18px;
              border-radius: 20px;
              font-size: 16px;
              font-weight: 500;
              box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
              white-space: nowrap;

              .status-icon {
                font-size: 18px;
                font-weight: bold;
              }
            }
          }
        }

        .divider {
          height: 2px;
          background: linear-gradient(90deg, rgba(215, 23, 24, 0.2), rgba(215, 23, 24, 0.05), rgba(215, 23, 24, 0.2));
          border-radius: 1px;
          margin: 8px 0;
        }

        .info-grid {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 20px;

          .info-item {
            display: flex;
            align-items: center;
            padding: 18px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 14px;
            border: 1px solid rgba(215, 23, 24, 0.1);
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
              background: rgba(255, 255, 255, 0.9);
            }

            .info-icon-wrapper {
              width: 45px;
              height: 45px;
              background: linear-gradient(145deg, #f8f9fa, #e9ecef);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 16px;
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

              .info-icon {
                font-size: 40px;
              }
            }

            .info-details {
              flex: 1;
              display: flex;
              flex-direction: column;
              gap: 4px;

              .info-label {
                font-size: 30px !important;
                font-weight: 500;
                color: #666;
                margin-bottom: 2px;
              }

              .info-value {
                font-size: 36px !important;
                font-weight: 600;
                color: #333;
                line-height: 1.2;
              }
            }
          }
        }

        .biography-section {
          background: rgba(215, 23, 24, 0.02);
          padding: 25px;
          border-radius: 16px;
          border: 1px solid rgba(215, 23, 24, 0.1);

          .bio-title {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-size: 22px;
            font-weight: 600;
            color: #d71718;

            .bio-icon {
              font-size: 40px;
              margin-right: 10px;
            }
          }

          .bio-content {
            .bio-text {
              font-size: 24px;
              color: #444;
              line-height: 1.6;
              text-indent: 2em;
              margin: 0;
              text-align: left;
            }
          }
        }
      }
    }

    .opinions-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;
      max-height: calc(100vh - 100px);

      .info-header {
        border-bottom: 4px solid #d71718;
        margin-bottom: 25px;
        padding-bottom: 15px;
        position: relative;
        flex-shrink: 0;

        &::after {
          content: '';
          position: absolute;
          bottom: -4px;
          left: 0;
          width: 80px;
          height: 4px;
          background: linear-gradient(90deg, #d71718, #ff4444);
        }

        h2 {
          color: #d71718;
          font-size: 36px;
          margin: 0;
          font-weight: 600;
          display: flex;
          align-items: center;
          gap: 15px;

          i {
            font-size: 32px;
          }
        }
      }

      .opinions-content {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        padding-right: 10px;
        max-height: calc(100vh - 250px);

        /* 显示滚动条 */
        scrollbar-width: thin;
        scrollbar-color: rgba(215, 23, 24, 0.3) transparent;

        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        &::-webkit-scrollbar-thumb {
          background: rgba(215, 23, 24, 0.3);
          border-radius: 3px;

          &:hover {
            background: rgba(215, 23, 24, 0.5);
          }
        }

        .opinion-card {
          background: rgba(255, 255, 255, 0.95);
          border-radius: 12px;
          padding: 18px;
          margin-bottom: 20px;
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          transition: all 0.3s;
          border-left: 4px solid #d71718;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
          }

          .opinion-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 15px;

            .opinion-meta {
              display: flex;
              gap: 15px;
              align-items: center;
              flex-wrap: wrap;

              .opinion-id {
                color: #d71718;
                font-weight: 600;
                font-size: 16px;
              }

              .opinion-category {
                padding: 6px 12px;
                background: rgba(215, 23, 24, 0.1);
                color: #d71718;
                border-radius: 12px;
                font-size: 14px;
                font-weight: 500;
              }

              .opinion-date {
                color: #666;
                font-size: 14px;
              }

              .opinion-status {
                padding: 6px 12px;
                border-radius: 12px;
                font-size: 14px;
                font-weight: 500;

                &.待处理 {
                  background: #fff3cd;
                  color: #856404;
                }

                &.处理中 {
                  background: #cce5ff;
                  color: #004085;
                }

                &.已处理 {
                  background: #d4edda;
                  color: #155724;
                }
              }
            }

            .service-rating {
              display: flex;
              flex-direction: column;
              align-items: center;
              gap: 5px;

              .rating-stars {
                display: flex;
                gap: 2px;

                .star {
                  font-size: 18px;
                  color: #ddd;

                  &.filled {
                    color: #ffd700;
                  }
                }
              }

              .rating-text {
                font-size: 14px;
                color: #666;
                font-weight: 500;
              }
            }
          }

          .opinion-content {
            margin-bottom: 15px;

            h4 {
              color: #d71718;
              font-size: 18px;
              font-weight: 600;
              margin: 0 0 10px;
              line-height: 1.4;
            }

            .opinion-text {
              color: #555;
              font-size: 16px;
              line-height: 1.6;
              margin: 0 0 10px;
            }

            .opinion-submitter {
              display: flex;
              gap: 20px;
              color: #666;
              font-size: 14px;
              flex-wrap: wrap;
            }
          }

          .feedback-section {
            background: rgba(215, 23, 24, 0.05);
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #d71718;

            .feedback-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 10px;
              flex-wrap: wrap;
              gap: 10px;

              h5 {
                color: #d71718;
                font-size: 16px;
                font-weight: 600;
                margin: 0;
              }

              .feedback-meta {
                display: flex;
                gap: 15px;
                color: #666;
                font-size: 14px;
              }
            }

            .feedback-content {
              p {
                color: #666;
                font-size: 16px;
                line-height: 1.6;
                margin: 0 0 10px;
              }


            }
          }

          .replies-section {
            margin-bottom: 15px;

            .replies-header {
              margin-bottom: 10px;

              h6 {
                color: #d71718;
                font-size: 16px;
                font-weight: 600;
                margin: 0;
              }
            }

            .replies-list {
              .reply-item {
                padding: 12px 15px;
                margin-bottom: 10px;
                border-radius: 8px;
                position: relative;

                &.citizen {
                  background: rgba(108, 117, 125, 0.1);
                  margin-left: 0;
                  margin-right: 30px;
                }

                &.representative {
                  background: rgba(215, 23, 24, 0.05);
                  margin-left: 30px;
                  margin-right: 0;
                }

                .reply-header {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  margin-bottom: 8px;

                  .reply-author {
                    font-weight: 600;
                    font-size: 14px;
                    color: #333;
                  }

                  .reply-date {
                    font-size: 12px;
                    color: #999;
                  }
                }

                .reply-content {
                  font-size: 14px;
                  line-height: 1.6;
                  color: #555;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 4K屏幕适配
@media (min-width: 2560px) {
  .representative-detail {
    font-size: 24px;
  }

  .content {
    padding: 60px;
    min-height: 0;

    .detail-container {
      min-height: 0;
    }

    .left-panel {
      width: 550px;
      padding: 60px;

      .photo-section {
        margin-bottom: 60px;

        .photo-frame {
          padding: 15px;
          border-radius: 30px;

          .representative-photo {
            width: 350px;
            height: 467px;
            border-radius: 25px;
          }
        }
      }

      .qr-section {
        margin-top: 40px;

        .qr-frame {
          padding: 25px;
          border-radius: 30px;

          .qr-code {
            width: 220px;
            height: 220px;
            border-radius: 18px;
          }
        }

        .qr-text {
          font-size: 36px;
          margin-top: 30px;
        }
      }
    }

          .right-panel {
        padding: 60px;
        gap: 50px;
        min-height: 0;

      .info-section {
        .info-header h2 {
          font-size: 50px;

          i {
            font-size: 44px;
          }
        }

        .representative-card {
          padding: 35px;
          gap: 25px;
          border-left-width: 10px;

          .profile-header {
            gap: 35px;

            .name-area {
              .rep-name {
                font-size: 64px;
              }

              .rep-badges {
                gap: 16px;

                .position-badge,
                .unit-badge {
                  padding: 16px 30px;
                  font-size: 24px;
                  border-radius: 25px;
                }
              }
            }

            .status-area {
              .status-badge {
                padding: 16px 26px;
                font-size: 22px;

                .status-icon {
                  font-size: 24px;
                }
              }
            }
          }

          .info-grid {
            gap: 30px;

            .info-item {
              padding: 26px;

              .info-icon-wrapper {
                width: 60px;
                height: 60px;
                margin-right: 24px;

                .info-icon {
                  font-size: 48px;
                }
              }

              .info-details {
                gap: 6px;

                .info-label {
                  font-size: 22px;
                }

                .info-value {
                  font-size: 28px;
                }
              }
            }
          }

          .biography-section {
            padding: 20px;
            font-family: 'AL-L';

            .bio-title {
              font-size: 32px;
              margin-bottom: 20px;

              .bio-icon {
                font-size: 40;
                margin-right: 15px;
              }
            }

            .bio-content {
              .bio-text {
                font-size: 32px;
                line-height: 1.5;
              }
            }
          }
        }
      }

      .opinions-section {
        max-height: calc(100vh - 200px);
        font-family: 'AL-L';

        .info-header h2 {
          font-size: 50px;

          i {
            font-size: 44px;
          }
        }

        .opinions-content {
          max-height: calc(100vh - 350px);
          .opinion-card {
            padding: 25px;
            margin-bottom: 30px;
            border-left-width: 6px;

            .opinion-header {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 20px;
              flex-wrap: wrap;
              gap: 20px;

              .opinion-meta {
                gap: 20px;

                .opinion-id {
                  font-size: 28px;
                }

                .opinion-category {
                  padding: 8px 16px;
                  font-size: 24px;
                  border-radius: 16px;
                }

                .opinion-date {
                  font-size: 24px;
                }

                .opinion-status {
                  padding: 8px 16px;
                  font-size: 24px;
                  border-radius: 16px;
                }
              }

              .service-rating {
                gap: 8px;

                .rating-stars {
                  gap: 4px;

                  .star {
                    font-size: 32px;
                  }
                }

                .rating-text {
                  font-size: 24px;
                }
              }
            }

            .opinion-content {
              margin-bottom: 20px;

              h4 {
                font-size: 36px;
                margin: 0 0 15px;
                text-align: center;
              }

              .opinion-text {
                font-size: 30px;
                margin: 0 0 15px;
                text-align: left;
                text-indent: 2em;
              }

              .opinion-submitter {
                gap: 25px;
                font-size: 24px;
              }
            }

            .feedback-section {
              padding: 20px;
              margin-bottom: 20px;
              border-left-width: 6px;

              .feedback-header {
                margin-bottom: 15px;
                gap: 15px;

                h5 {
                  font-size: 30px;
                }

                .feedback-meta {
                  gap: 20px;
                  font-size: 24px;
                }
              }

              .feedback-content {
                p {
                  font-size: 30px;
                  margin: 0 0 15px;
                  text-align: left;
                  text-indent: 2em;
                }


              }
            }

            .replies-section {
              margin-bottom: 20px;

              .replies-header {
                margin-bottom: 15px;

                h6 {
                  font-size: 30px;
                }
              }

              .replies-list {
                .reply-item {
                  padding: 18px 20px;
                  margin-bottom: 15px;
                  border-radius: 12px;

                  &.citizen {
                    margin-right: 50px;
                  }

                  &.representative {
                    margin-left: 50px;
                  }

                  .reply-header {
                    margin-bottom: 12px;

                    .reply-author {
                      font-size: 24px;
                    }

                    .reply-date {
                      font-size: 24px;
                    }
                  }

                  .reply-content {
                    font-size: 30px;
                  }
                }
              }
            }
          }
        }
      }
    }

    // 无履职档案状态样式 - 4K
    .no-opinions-container {
      padding: 120px 0;

      .no-opinions-content {
        i {
          font-size: 120px;
          margin-bottom: 30px;
        }

        p {
          font-size: 32px;
        }
      }
    }

    // 履职档案加载状态样式 - 4K
    .loading-opinions {
      padding: 120px 0;

      .loading-text {
        font-size: 28px;
      }
    }
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

// 加载状态样式
.loading-content {
  display: flex;
  align-items: center;
  justify-content: center;

  .loading-container {
    text-align: center;
    color: #666;

    .loading-spinner {
      width: 60px;
      height: 60px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #d71718;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px;
    }

    p {
      font-size: 18px;
      margin: 0;
    }
  }
}

// 无数据状态样式
.no-data-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;

  .no-data-content {
    text-align: center;
    color: #999;

    i {
      font-size: 60px;
      color: #d71718;
      margin-bottom: 20px;
      display: block;
    }

    p {
      font-size: 18px;
      margin: 0;
    }
  }
}

// 无履职档案状态样式
.no-opinions-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px 0;

  .no-opinions-content {
    text-align: center;
    color: #999;

    i {
      font-size: 80px;
      color: #d71718;
      margin-bottom: 20px;
      display: block;
    }

    p {
      font-size: 20px;
      margin: 0;
    }
  }
}

// 履职档案加载状态样式
.loading-opinions {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px 0;

  .loading-text {
    font-size: 18px;
    color: #666;
    text-align: center;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 右下角返回按钮
.back-button {
  position: fixed;
  bottom: 40px;
  right: 40px;
  width: 80px;
  height: 80px;
  background: rgba(215, 23, 24, 0.9);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(215, 23, 24, 0.3);
  backdrop-filter: blur(10px);
  z-index: 10000;

  &:hover {
    background: rgba(215, 23, 24, 1);
    transform: scale(1.1);
    box-shadow: 0 8px 30px rgba(215, 23, 24, 0.5);
  }

  .back-icon {
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 8px 12px 8px 0;
    border-color: transparent #fff transparent transparent;
    margin-bottom: 4px;
  }

  span {
    color: #fff;
    font-size: 12px;
    font-weight: 500;
    font-family: 'AL-R';
  }
}

// 4K分辨率适配 - 返回按钮
@media (min-width: 2560px) {
  .back-button {
    width: 120px;
    height: 120px;
    bottom: 60px;
    right: 60px;

    .back-icon {
      border-width: 12px 18px 12px 0;
      margin-bottom: 6px;
    }

    span {
      font-size: 24px;
    }
  }
}

// 图标字体
.icon-user::before { content: '👤'; }
.icon-chart::before { content: '📊'; }
</style>
