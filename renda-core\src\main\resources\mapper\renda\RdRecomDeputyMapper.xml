<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renda.core.mapper.RdRecomDeputyMapper">

    <resultMap type="RdRecomDeputy" id="RdRecomDeputyResult">
        <result property="id"    column="id"    />
        <result property="recomId"    column="recom_id"    />
        <result property="deputyId"    column="deputy_id"    />
        <result property="hasReply"    column="has_reply"    />
        <result property="replyRate"    column="reply_rate"    />
    </resultMap>

    <sql id="selectRdRecomDeputyVo">
        select id, recom_id, deputy_id, has_reply, reply_rate from rd_recom_deputy
    </sql>

    <select id="selectRdRecomDeputyList" parameterType="RdRecomDeputy" resultMap="RdRecomDeputyResult">
        <include refid="selectRdRecomDeputyVo"/>
        <where>
            <if test="recomId != null "> and recom_id = #{recomId}</if>
            <if test="deputyId != null "> and deputy_id = #{deputyId}</if>
        </where>
    </select>

    <select id="selectRdRecomDeputyById" parameterType="Long" resultMap="RdRecomDeputyResult">
        <include refid="selectRdRecomDeputyVo"/>
        where id = #{id}
    </select>

    <insert id="insertRdRecomDeputy" parameterType="RdRecomDeputy">
        insert into rd_recom_deputy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="recomId != null">recom_id,</if>
            <if test="deputyId != null">deputy_id,</if>
            <if test="hasReply != null">has_reply,</if>
            <if test="replyRate != null">reply_rate,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="recomId != null">#{recomId},</if>
            <if test="deputyId != null">#{deputyId},</if>
            <if test="hasReply != null">#{hasReply},</if>
            <if test="replyRate != null">#{replyRate},</if>
         </trim>
    </insert>

    <update id="updateRdRecomDeputy" parameterType="RdRecomDeputy">
        update rd_recom_deputy
        <trim prefix="SET" suffixOverrides=",">
            <if test="recomId != null">recom_id = #{recomId},</if>
            <if test="deputyId != null">deputy_id = #{deputyId},</if>
            <if test="hasReply != null">has_reply = #{hasReply},</if>
            <if test="replyRate != null">reply_rate = #{replyRate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRdRecomDeputyById" parameterType="Long">
        delete from rd_recom_deputy where id = #{id}
    </delete>

    <delete id="deleteRdRecomDeputyByIds" parameterType="String">
        delete from rd_recom_deputy where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteRdRecomDeputyByRecomId" parameterType="Long">
        delete from rd_recom_deputy where recom_id = #{recomId}
    </delete>
    <delete id="deleteRdRecomDeputyByRecomIds">
        delete from rd_recom_deputy where recom_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
