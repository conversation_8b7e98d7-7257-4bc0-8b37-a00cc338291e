<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renda.system.mapper.SysNoticeMapper">

    <resultMap type="SysNotice" id="SysNoticeResult">
        <result property="noticeId"        column="notice_id"        />
        <result property="noticeTitle"     column="notice_title"     />
        <result property="noticePublisher" column="notice_publisher" />
        <result property="noticeType"      column="notice_type"      />
        <result property="noticeCategory"  column="notice_category"  />
        <result property="noticeContent"   column="notice_content"   />
        <result property="coverUrl"        column="cover_url"        />
        <result property="isTop"           column="is_top"           />
        <result property="status"          column="status"           />
        <result property="viewCount"       column="view_count"       />
        <result property="allowFeedback"   column="allow_feedback"   />
        <result property="feedbackCount"   column="feedbackCount"   />
        <result property="createBy"        column="create_by"        />
        <result property="createTime"      column="create_time"      />
        <result property="updateBy"        column="update_by"        />
        <result property="updateTime"      column="update_time"      />
        <result property="remark"          column="remark"           />
    </resultMap>

    <sql id="selectNoticeVo">
        select notice_id, notice_title, notice_publisher, notice_type, notice_category,
               cast(notice_content as char) as notice_content, cover_url,
               status, is_top, view_count, allow_feedback, create_by, create_time, update_by, update_time, remark,
                (select count(1) from rd_notice_feedback where notice_id = n.notice_id) as feedbackCount
		from sys_notice n
    </sql>

    <select id="selectNoticeById" parameterType="Long" resultMap="SysNoticeResult">
        <include refid="selectNoticeVo"/>
        where notice_id = #{noticeId}
    </select>

    <select id="selectNoticeList" parameterType="SysNotice" resultMap="SysNoticeResult">
        <include refid="selectNoticeVo"/>
        <where>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
			<if test="noticeTitle != null and noticeTitle != ''">
				AND notice_title like concat('%', #{noticeTitle}, '%')
			</if>
            <if test="noticePublisher != null and noticePublisher != ''">
                AND notice_publisher like concat('%', #{noticePublisher}, '%')
            </if>
			<if test="noticeType != null and noticeType != ''">
				AND notice_type = #{noticeType}
			</if>
			<if test="createBy != null and createBy != ''">
				AND create_by like concat('%', #{createBy}, '%')
			</if>
		</where>
        order by is_top desc, notice_id desc
    </select>

    <select id="getNews" parameterType="SysNotice" resultMap="SysNoticeResult">
        <include refid="selectNoticeVo"/>
        <where>
            status = 1
            <if test="noticeTitle != null and noticeTitle != ''">
                AND ( notice_title like concat('%', #{noticeTitle}, '%')
                or notice_publisher like concat('%', #{noticePublisher}, '%')
                or notice_content like concat('%', #{noticeContent}, '%') )
            </if>
        </where>
        order by is_top desc, notice_id desc
        limit 0, 5
    </select>

    <select id="getAllNews" parameterType="SysNotice" resultMap="SysNoticeResult">
        <include refid="selectNoticeVo"/>
        <where> status = 1
            # 不含通知公告类型
            and notice_type != '5'
            <if test="noticeTitle != null and noticeTitle != ''">
                and ( notice_title like concat('%', #{noticeTitle}, '%')
                or notice_publisher like concat('%', #{noticePublisher}, '%')
                or notice_content like concat('%', #{noticeContent}, '%') )
            </if>
        </where>
        order by is_top desc, notice_id desc
    </select>

    <select id="getAllWorkNotice" parameterType="SysNotice" resultMap="SysNoticeResult">
        <include refid="selectNoticeVo"/>
        <where> status = 1
            # 不含通知公告类型
            and notice_type = '5'
            <if test="noticeTitle != null and noticeTitle != ''">
                and ( notice_title like concat('%', #{noticeTitle}, '%')
                or notice_publisher like concat('%', #{noticePublisher}, '%')
                or notice_content like concat('%', #{noticeContent}, '%') )
            </if>
        </where>
        order by is_top desc, notice_id desc
    </select>

    <insert id="insertNotice" parameterType="SysNotice">
        insert into sys_notice (
			<if test="noticeTitle != null and noticeTitle != ''">notice_title, </if>
            <if test="noticePublisher != null and noticePublisher != ''">notice_publisher, </if>
			<if test="noticeType != null and noticeType != ''">notice_type, </if>
            <if test="noticeCategory != null and noticeCategory != ''">notice_category, </if>
			<if test="noticeContent != null and noticeContent != ''">notice_content, </if>
            <if test="coverUrl != null and coverUrl != ''">cover_url, </if>
            <if test="isTop != null and isTop != ''">is_top, </if>
            <if test="allowFeedback != null">allow_feedback, </if>
			<if test="status != null and status != ''">status, </if>
			<if test="remark != null and remark != ''">remark,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
            view_count,
 			create_time
 		)values(
			<if test="noticeTitle != null and noticeTitle != ''">#{noticeTitle}, </if>
            <if test="noticePublisher != null and noticePublisher != ''">#{noticePublisher}, </if>
			<if test="noticeType != null and noticeType != ''">#{noticeType}, </if>
            <if test="noticeCategory != null and noticeCategory != ''">#{noticeCategory}, </if>
			<if test="noticeContent != null and noticeContent != ''">#{noticeContent}, </if>
            <if test="coverUrl != null and coverUrl != ''">#{coverUrl}, </if>
            <if test="isTop != null and isTop != ''">#{isTop}, </if>
            <if test="allowFeedback != null">#{allowFeedback}, </if>
			<if test="status != null and status != ''">#{status}, </if>
			<if test="remark != null and remark != ''">#{remark},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 		    0,
 			sysdate()
		)
    </insert>

    <update id="addNoticeViewCount" parameterType="Long">
        update sys_notice
        set view_count = view_count + 1
        where notice_id = #{noticeId}
    </update>

    <update id="updateNotice" parameterType="SysNotice">
        update sys_notice
        <set>
            <if test="noticeTitle != null and noticeTitle != ''">notice_title = #{noticeTitle}, </if>
            <if test="noticePublisher != null and noticePublisher != ''">notice_publisher = #{noticePublisher}, </if>
            <if test="noticeType != null and noticeType != ''">notice_type = #{noticeType}, </if>
            <if test="noticeCategory != null and noticeCategory != ''">notice_category = #{noticeCategory}, </if>
            <if test="noticeContent != null">notice_content = #{noticeContent}, </if>
            <if test="coverUrl != null and coverUrl != ''">cover_url = #{coverUrl}, </if>
            <if test="isTop != null and isTop != ''">is_top = #{isTop}, </if>
            <if test="allowFeedback != null">allow_feedback = #{allowFeedback}, </if>
            <if test="status != null and status != ''">status = #{status}, </if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			update_time = sysdate()
        </set>
        where notice_id = #{noticeId}
    </update>

    <delete id="deleteNoticeById" parameterType="Long">
        delete from sys_notice where notice_id = #{noticeId}
    </delete>

    <delete id="deleteNoticeByIds" parameterType="Long">
        delete from sys_notice where notice_id in
        <foreach item="noticeId" collection="array" open="(" separator="," close=")">
            #{noticeId}
        </foreach>
    </delete>

</mapper>
