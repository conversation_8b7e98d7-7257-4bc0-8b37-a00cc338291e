<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renda.core.mapper.RdFeedbackMapper">

    <resultMap type="RdFeedback" id="RdFeedbackResult">
        <result property="id"    column="id"    />
        <result property="adviceId"    column="advice_id"    />
        <result property="userType"    column="user_type"    />
        <result property="userId"    column="user_id"    />
        <result property="name"    column="name"    />
        <result property="avatar"    column="avatar"    />
        <result property="content"    column="content"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <resultMap id="FeedbackWithAttachsResult" type="com.renda.core.domain.vo.FeedbackInfoVO">
        <result property="feedbackType"    column="feedback_type"    />
        <result property="userType"    column="user_type"    />
        <result property="userId"    column="user_id"    />
        <result property="name"    column="name"    />
        <result property="avatar"    column="avatar"    />
        <result property="content"    column="content"    />
        <result property="createTime"    column="create_time"    />
        <collection property="attachs" ofType="com.renda.core.domain.vo.FeedbackAttachmentVO">
            <result property="fileType"    column="file_type"    />
            <result property="fileName"    column="file_name"    />
            <result property="fileUrl"    column="file_url"    />
        </collection>
    </resultMap>

    <sql id="selectRdFeedbackVo">
        select id, advice_id, user_type, user_id, name, avatar, content, create_time from rd_feedback
    </sql>

    <select id="selectRdFeedbackList" parameterType="RdFeedback" resultMap="RdFeedbackResult">
        <include refid="selectRdFeedbackVo"/>
        <where>
            <if test="adviceId != null "> and advice_id = #{adviceId}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="avatar != null  and avatar != ''"> and avatar = #{avatar}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
        </where>
    </select>

    <select id="selectRdFeedbackById" parameterType="Long" resultMap="RdFeedbackResult">
        <include refid="selectRdFeedbackVo"/>
        where id = #{id}
    </select>

    <select id="selectFeedbackListWithAttachs" parameterType="Long" resultMap="FeedbackWithAttachsResult">
        select f.feedback_type, f.user_type, f.user_id, f.name, f.avatar, f.content, f.create_time,
               a.file_type, a.file_name, a.file_url
        from rd_feedback f
        left join rd_feedback_attachment a on f.id = a.feedback_id
        where f.advice_id = #{adviceId}
        order by f.create_time, a.file_type
    </select>

    <insert id="insertRdFeedback" parameterType="RdFeedback" useGeneratedKeys="true" keyProperty="id">
        insert into rd_feedback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="adviceId != null">advice_id,</if>
            <if test="feedbackType != null">feedback_type,</if>
            <if test="userType != null">user_type,</if>
            <if test="userId != null">user_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="avatar != null">avatar,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="adviceId != null">#{adviceId},</if>
            <if test="feedbackType != null">#{feedbackType},</if>
            <if test="userType != null">#{userType},</if>
            <if test="userId != null">#{userId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateRdFeedback" parameterType="RdFeedback">
        update rd_feedback
        <trim prefix="SET" suffixOverrides=",">
            <if test="adviceId != null">advice_id = #{adviceId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRdFeedbackById" parameterType="Long">
        delete from rd_feedback where id = #{id}
    </delete>

    <delete id="deleteRdFeedbackByIds" parameterType="String">
        delete from rd_feedback where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
