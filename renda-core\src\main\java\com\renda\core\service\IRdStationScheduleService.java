package com.renda.core.service;

import java.util.List;
import java.util.Date;
import com.renda.core.domain.RdStationSchedule;

/**
 * 排班表管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface IRdStationScheduleService 
{
    /**
     * 查询排班表管理
     * 
     * @param id 排班表管理主键
     * @return 排班表管理
     */
    public RdStationSchedule selectRdStationScheduleById(Long id);

    /**
     * 查询排班表管理列表
     * 
     * @param rdStationSchedule 排班表管理
     * @return 排班表管理集合
     */
    public List<RdStationSchedule> selectRdStationScheduleList(RdStationSchedule rdStationSchedule);

    /**
     * 检查指定联络站和日期是否已存在排班记录
     * 
     * @param stationId 联络站ID
     * @param scheduleDate 排班日期
     * @param excludeId 排除的记录ID（修改时使用）
     * @return 是否存在重复记录
     */
    public boolean checkStationScheduleExists(Long stationId, Date scheduleDate, Long excludeId);

    /**
     * 新增排班表管理
     * 
     * @param rdStationSchedule 排班表管理
     * @return 结果
     */
    public int insertRdStationSchedule(RdStationSchedule rdStationSchedule);

    /**
     * 修改排班表管理
     * 
     * @param rdStationSchedule 排班表管理
     * @return 结果
     */
    public int updateRdStationSchedule(RdStationSchedule rdStationSchedule);

    /**
     * 批量删除排班表管理
     * 
     * @param ids 需要删除的排班表管理主键集合
     * @return 结果
     */
    public int deleteRdStationScheduleByIds(Long[] ids);

    /**
     * 删除排班表管理信息
     * 
     * @param id 排班表管理主键
     * @return 结果
     */
    public int deleteRdStationScheduleById(Long id);
}
