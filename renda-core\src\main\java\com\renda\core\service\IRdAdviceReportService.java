package com.renda.core.service;

import java.util.List;
import com.renda.core.domain.RdAdviceReport;
import com.renda.core.domain.vo.AdviceReportVO;

/**
 * 建议上报Service接口
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
public interface IRdAdviceReportService
{
    /**
     * 查询建议上报
     *
     * @param id 建议上报主键
     * @return 建议上报
     */
    public RdAdviceReport selectRdAdviceReportById(Long id);

    /**
     * 查询建议上报列表
     *
     * @param rdAdviceReport 建议上报
     * @return 建议上报集合
     */
    public List<RdAdviceReport> selectRdAdviceReportList(RdAdviceReport rdAdviceReport);

    /**
     * 新增建议上报
     *
     * @param rdAdviceReport 建议上报
     * @return 结果
     */
    public int insertRdAdviceReport(RdAdviceReport rdAdviceReport);

    /**
     * 修改建议上报
     *
     * @param rdAdviceReport 建议上报
     * @return 结果
     */
    public int updateRdAdviceReport(RdAdviceReport rdAdviceReport);

    /**
     * 批量删除建议上报
     *
     * @param ids 需要删除的建议上报主键集合
     * @return 结果
     */
    public int deleteRdAdviceReportByIds(Long[] ids);

    /**
     * 删除建议上报信息
     *
     * @param id 建议上报主键
     * @return 结果
     */
    public int deleteRdAdviceReportById(Long id);

    /***
     *
     * @param adviceReportVO 建议上报vo
     * @return
     */
    public int reportAdvice(AdviceReportVO adviceReportVO);

}
