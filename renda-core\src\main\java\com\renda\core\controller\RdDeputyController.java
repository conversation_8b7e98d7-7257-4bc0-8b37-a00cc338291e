package com.renda.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.renda.core.domain.vo.DeputyStatVO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.renda.common.annotation.Log;
import com.renda.common.core.controller.BaseController;
import com.renda.common.core.domain.AjaxResult;
import com.renda.common.enums.BusinessType;
import com.renda.core.domain.RdDeputy;
import com.renda.core.service.IRdDeputyService;
import com.renda.common.utils.poi.ExcelUtil;
import com.renda.common.core.page.TableDataInfo;

/**
 * 人大代表管理Controller
 *
 * <AUTHOR>
 * @date 2023-10-23
 */
@RestController
@RequestMapping("/renda/deputy")
public class RdDeputyController extends BaseController
{
    @Autowired
    private IRdDeputyService rdDeputyService;

    /**
     * 查询人大代表管理列表
     */
    @PreAuthorize("@ss.hasPermi('renda:deputy:list')")
    @GetMapping("/list")
    public TableDataInfo list(RdDeputy rdDeputy)
    {
        startPage();
        List<RdDeputy> list = rdDeputyService.selectRdDeputyList(rdDeputy);
        return getDataTable(list);
    }

    /**
     * 查询人大代表管理列表
     */
    @PreAuthorize("@ss.hasPermi('renda:deputy:list')")
    @GetMapping("/list2")
    public TableDataInfo list2(RdDeputy rdDeputy)
    {
        startPage();
        List<RdDeputy> list = rdDeputyService.selectRdDeputyList2(rdDeputy);
        return getDataTable(list);
    }

    /**
     * 导出人大代表管理列表
     */
    @PreAuthorize("@ss.hasPermi('renda:deputy:export')")
    @Log(title = "人大代表管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RdDeputy rdDeputy)
    {
        List<RdDeputy> list = rdDeputyService.selectRdDeputyList(rdDeputy);
        ExcelUtil<RdDeputy> util = new ExcelUtil<RdDeputy>(RdDeputy.class);
        util.exportExcel(response, list, "人大代表管理数据");
    }

    /**
     * 获取人大代表管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('renda:deputy:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rdDeputyService.selectRdDeputyById(id));
    }

    /**
     * 新增人大代表管理
     */
    @PreAuthorize("@ss.hasPermi('renda:deputy:add')")
    @Log(title = "人大代表管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RdDeputy rdDeputy)
    {
        return toAjax(rdDeputyService.insertRdDeputy(rdDeputy));
    }

    /**
     * 修改人大代表管理
     */
    @PreAuthorize("@ss.hasPermi('renda:deputy:edit')")
    @Log(title = "人大代表管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RdDeputy rdDeputy)
    {
        return toAjax(rdDeputyService.updateRdDeputy(rdDeputy));
    }

    /**
     * 删除人大代表管理
     */
    @PreAuthorize("@ss.hasPermi('renda:deputy:remove')")
    @Log(title = "人大代表管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rdDeputyService.deleteRdDeputyByIds(ids));
    }

    /**
     * 获取人大代表履职统计信息
     */
    @PreAuthorize("@ss.hasPermi('renda:deputy:list')")
    @GetMapping(value = "/deputyStat/{id}")
    public AjaxResult getDeputyStat(@PathVariable("id") Long id)
    {
        return success(rdDeputyService.getDeputyStat(id));
    }

    /**
     * 批量生成代表二维码
     */
    @PreAuthorize("@ss.hasPermi('renda:deputy:qrcode')")
    @Log(title = "人大代表二维码", businessType = BusinessType.INSERT)
    @PostMapping("/genDeputyQrcode/{ids}")
    public AjaxResult genDeputyQrcode(@PathVariable Long[] ids)
    {
        return toAjax(rdDeputyService.genDeputyQrcode(ids));
    }

}
