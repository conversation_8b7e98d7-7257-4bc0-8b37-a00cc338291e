<template>
  <div class="opinion-collection">
    <app-header :showBackBtn="true" />

    <main class="content">
      <div class="background-elements">
        <div class="circle circle-1"></div>
        <div class="circle circle-2"></div>
        <div class="circle circle-3"></div>
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
      </div>

      <div class="page-title">
        <h1>民意征集站</h1>
        <div class="title-decoration"></div>
        <p>征集群众建议 · 了解民情民声</p>
        <!-- 提交意见按钮 -->
        <div class="submit-opinion-btn" @click="submitOpinion">
          <i class="el-icon-edit"></i>
          <span>提交意见建议</span>
        </div>
      </div>

      <!-- 意见分类区域 -->
      <div class="section categories-section">
        <!-- <div class="section-title">
          <h2>意见分类</h2>
          <div class="title-line"></div>
        </div> -->
        <div class="categories-container">
          <div
            class="category-card"
            v-for="category in categories"
            :key="category.id"
            :class="{ active: selectedCategory === category.id }"
            @click="selectCategory(category.id)"
          >
            <div class="category-icon">
              <i :class="category.icon"></i>
            </div>
            <div class="category-info">
              <h3>{{ category.name }}</h3>
              <div class="opinion-count">{{ category.count }} 条</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 意见列表区域 -->
      <div class="section opinions-section">
        <div class="section-title">
          <!-- <h2>全部意见</h2>
          <div class="title-line"></div> -->
          <div class="filter-bar">
            <div class="filter-group">
              <label>状态筛选：</label>
              <select v-model="selectedStatus" @change="filterOpinions">
                <option value="">全部</option>
                <option value="待处理">待处理</option>
                <option value="处理中">处理中</option>
                <option value="已处理">已处理</option>
              </select>
            </div>
            <div class="filter-group">
              <label>时间排序：</label>
              <select v-model="sortOrder" @change="sortOpinions">
                <option value="desc">最新优先</option>
                <option value="asc">最早优先</option>
              </select>
            </div>
          </div>
        </div>

        <div class="opinions-container">
          <!-- 加载状态 -->
          <div v-if="loading" class="loading-container">
            <div class="loading-text">正在加载意见建议...</div>
          </div>

          <!-- 意见建议列表 -->
          <div v-else-if="filteredOpinions.length > 0">
            <div class="opinion-card" v-for="opinion in filteredOpinions" :key="opinion.id" @click="viewOpinionDetail(opinion.id)">
            <div class="opinion-header">
              <div class="opinion-meta">
                <span class="opinion-id">#{{ opinion.id }}</span>
                <span class="opinion-category">{{ dict.type.rd_advice_category[opinion.category-1].label }}</span>
                <span class="opinion-date">{{ formatDate(opinion.submitDate) }}</span>
                <span class="opinion-status" :class="dict.type.rd_advice_status[opinion.status].label">{{ dict.type.rd_advice_status[opinion.status].label }}</span>
              </div>
              <!-- 已处理意见显示群众服务评分 -->
              <div class="service-rating" v-if="opinion.status === '2' && opinion.rating">
                <div class="rating-stars">
                  <span v-for="n in 5" :key="n" class="star" :class="{ filled: n <= (opinion.rating || 0) }">★</span>
                </div>
                <span class="rating-text">{{ opinion.rating }}分</span>
              </div>
            </div>

            <div class="opinion-content">
              <h4>{{ opinion.title }}</h4>
              <p class="opinion-text">{{ opinion.content }}</p>
              <div class="opinion-submitter">
                <span>提交人：{{ opinion.submitter ? opinion.submitter : '匿名' }}</span>
                <span>联系方式：{{ opinion.contact ? opinion.contact : '未填写' }}</span>
              </div>
            </div>

            <!-- 代表反馈 -->
            <div class="feedback-section" v-if="opinion.feedback">
              <div class="feedback-header">
                <h5>代表反馈</h5>
                <div class="feedback-meta">
                  <span>{{ opinion.feedback.representative }}</span>
                  <span>{{ formatDate(opinion.feedback.date) }}</span>
                </div>
              </div>
              <div class="feedback-content">
                <p>{{ opinion.feedback.content }}</p>
              </div>
            </div>

            <!-- 对话回复 -->
            <div class="replies-section" v-if="opinion.replies && opinion.replies.length > 0">
              <div class="replies-header">
                <h6>对话交流</h6>
              </div>
              <div class="replies-list">
                <div
                  class="reply-item"
                  v-for="reply in opinion.replies"
                  :key="reply.id"
                  :class="reply.type"
                >
                  <div class="reply-header">
                    <span class="reply-author">{{ reply.author }}</span>
                    <span class="reply-date">{{ formatDate(reply.date) }}</span>
                  </div>
                  <div class="reply-content">{{ reply.content }}</div>
                </div>
              </div>
            </div>

            </div>
          </div>

          <!-- 无数据状态 -->
          <div v-else class="no-data-container">
            <div class="no-data-text">暂无意见建议</div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-container" v-if="totalPages > 1">
          <button
            class="pagination-btn prev-btn"
            :disabled="currentPage === 1"
            @click="changePage(currentPage - 1)"
          >
            <i class="arrow-left"></i>
            上一页
          </button>

          <div class="page-numbers">
            <button
              class="page-number"
              v-for="page in visiblePages"
              :key="page"
              :class="{ active: page === currentPage }"
              @click="changePage(page)"
              :disabled="page === '...'"
            >
              {{ page }}
            </button>
          </div>

          <button
            class="pagination-btn next-btn"
            :disabled="currentPage === totalPages"
            @click="changePage(currentPage + 1)"
          >
            下一页
            <i class="arrow-right"></i>
          </button>
        </div>
      </div>

      <!-- 返回顶部按钮 -->
      <div class="back-to-top" v-show="showBackToTop" @click="scrollToTop">
        <i class="el-icon-top"></i>
        <span>返回顶部</span>
      </div>

      <!-- 右下角返回按钮 -->
      <div class="back-button" @click="goBack">
        <span>返回</span>
      </div>

    </main>
  </div>
</template>

<script>
import AppHeader from '@/components/StationScreen/AppHeader.vue'
import { getAdviceCategories, getAdviceList } from '@/api/renda/stationscreen'

export default {
  name: 'OpinionCollection',
  components: {
    AppHeader
  },
  data() {
    return {
      selectedCategory: 0, // 0 表示全部
      selectedStatus: '',
      sortOrder: 'desc',
      currentPage: 1,
      pageSize: 15,
      totalPages: 0,
      stationId: '0',
      loading: false,
      showBackToTop: false,

      categories: [],
      opinions: []
    }
  },

  dicts: ['rd_advice_category', 'rd_advice_status'],

  computed: {
    selectedCategoryName() {
      const category = this.categories.find(c => c.id === this.selectedCategory)
      return category ? category.name : '全部'
    },

    filteredOpinions() {
      // API返回的数据已经是过滤和分页后的数据
      return this.opinions
    },

    // 显示的页码
    visiblePages() {
      const pages = []
      const totalPages = this.totalPages
      const currentPage = this.currentPage

      if (totalPages <= 7) {
        // 总页数少于7页，显示所有页码
        for (let i = 1; i <= totalPages; i++) {
          pages.push(i)
        }
      } else {
        // 总页数大于7页，智能显示页码
        if (currentPage <= 4) {
          // 当前页在前4页
          for (let i = 1; i <= 5; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(totalPages)
        } else if (currentPage >= totalPages - 3) {
          // 当前页在后4页
          pages.push(1)
          pages.push('...')
          for (let i = totalPages - 4; i <= totalPages; i++) {
            pages.push(i)
          }
        } else {
          // 当前页在中间
          pages.push(1)
          pages.push('...')
          for (let i = currentPage - 1; i <= currentPage + 1; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(totalPages)
        }
      }

      return pages
    }
  },

  methods: {
    // 处理键盘事件
    handleKeydown(event) {
      // ESC键返回首页
      if (event.key === 'Escape') {
        this.goToHome()
      }
    },

    goToHome() {
      this.$router.push({ path: "StationScreenHome"});
    },

    selectCategory(categoryId) {
      this.selectedCategory = categoryId
      this.currentPage = 1
      this.loadOpinions()
    },

    filterOpinions() {
      this.currentPage = 1
      this.loadOpinions()
    },

    sortOpinions() {
      this.currentPage = 1
      this.loadOpinions()
    },

    changePage(page) {
      if (page >= 1 && page <= this.totalPages && page !== '...') {
        this.currentPage = page
        this.loadOpinions()
        // 切换页面后回到顶部
        this.scrollToTop()
      }
    },

    // 返回顶部
    scrollToTop() {
      const content = document.querySelector('.content')
      if (content) {
        content.scrollTo({
          top: 0,
          behavior: 'smooth'
        })
      }
    },

    // 监听滚动事件
    handleScroll() {
      const content = document.querySelector('.content')
      if (content) {
        this.showBackToTop = content.scrollTop > 500
      }
    },

    submitOpinion() {
      this.$router.push({
        name: 'PhotoWall',
        query: {
          stationId: this.stationId
        }
      })
    },

    viewOpinionDetail(opinionId) {
      this.$router.push({
        name: 'OpinionDetail',
        query: {
          opinionId: opinionId
        }
      })
    },

    // 手动刷新数据
    refreshData() {
      this.loadCategories()
      this.loadOpinions()
    },

    // 加载意见分类数据
    async loadCategories() {
      try {
        const response = await getAdviceCategories(this.stationId)
        if (response.code === 200) {
          this.categories = response.data
        } else {
          console.error('获取意见分类失败:', response.msg)
        }
      } catch (error) {
        console.error('加载意见分类失败:', error)
      }
    },

    // 加载意见建议列表
    async loadOpinions() {
      try {
        this.loading = true
        const params = {
          isStationDeputy: 1,
          categoryId: this.selectedCategory,
          status: this.selectedStatus,
          sortOrder: this.sortOrder,
          pageNum: this.currentPage,
          pageSize: this.pageSize
        }

        const response = await getAdviceList(params)
        if (response.code === 200) {
          this.opinions = response.data.list
          this.totalPages = response.data.totalPages
        } else {
          console.error('获取意见建议列表失败:', response.msg)
        }
      } catch (error) {
        console.error('加载意见建议列表失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return ''

      try {
        const date = new Date(dateString)

        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          return dateString
        }

        // 格式化为 YYYY-MM-DD HH:mm 格式
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')

        return `${year}-${month}-${day} ${hours}:${minutes}`
      } catch (error) {
        console.error('日期格式化失败:', error)
        return dateString
      }
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    }
  },
  mounted() {
    this.$nextTick(() => {
      // 禁用右键菜单
      document.oncontextmenu = function() {
        return false;
      };
    });

    // 获取联络站Id
    this.stationId = this.$route.query.stationId || '0'
    console.log('联络站ID:', this.stationId)

    // 加载数据
    this.loadCategories()
    this.loadOpinions()

    document.addEventListener('keydown', this.handleKeydown)

    // 添加滚动监听
    const content = document.querySelector('.content')
    if (content) {
      content.addEventListener('scroll', this.handleScroll)
    }
  },
  activated() {
    // 每次激活组件时都刷新数据
    this.loadCategories()
    this.loadOpinions()
  },
  beforeRouteUpdate(to, from, next) {
    // 路由参数变化时刷新数据
    this.stationId = to.query.stationId || '0'
    this.currentPage = 1  // 重置到第一页
    this.selectedCategory = 0  // 重置分类筛选
    this.selectedStatus = ''  // 重置状态筛选
    this.loadCategories()
    this.loadOpinions()
    next()
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.handleKeydown)

    // 移除滚动监听
    const content = document.querySelector('.content')
    if (content) {
      content.removeEventListener('scroll', this.handleScroll)
    }
  }
}
</script>

<style lang="scss" scoped>
.opinion-collection {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f8f8f8, #efefef);
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  margin: 0;
  padding: 0;

  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.content {
  flex: 1;
  padding: 80px 800px 40px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;

  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }

  .background-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 0;
    pointer-events: none;

    .circle {
      position: absolute;
      border-radius: 50%;
      opacity: 0.06;

      &.circle-1 {
        width: 600px;
        height: 600px;
        background: #d71718;
        top: -200px;
        left: -200px;
      }

      &.circle-2 {
        width: 500px;
        height: 500px;
        background: #d71718;
        bottom: 20%;
        right: 15%;
      }

      &.circle-3 {
        width: 300px;
        height: 300px;
        background: #d71718;
        bottom: 35%;
        left: 5%;
      }
    }

    .shape {
      position: absolute;
      opacity: 0.04;
      background: #d71718;

      &.shape-1 {
        width: 500px;
        height: 500px;
        transform: rotate(45deg);
        top: 15%;
        right: -300px;
      }

      &.shape-2 {
        width: 400px;
        height: 400px;
        transform: rotate(30deg);
        bottom: -50px;
        left: 30%;
      }
    }
  }

  .page-title {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
    z-index: 1;

    h1 {
      color: #d71718;
      font-size: 48px;
      font-weight: 500;
      margin: 0 0 20px;
      letter-spacing: 2px;
      font-family: 'AL-BL' !important;
    }

    .title-decoration {
      width: 120px;
      height: 4px;
      background: linear-gradient(135deg, #d71718, #b41616);
      margin: 0 auto 20px;
      border-radius: 2px;
    }

    p {
      color: #666;
      font-size: 18px;
      margin: 0 0 30px;
      font-family: 'AL-L' !important;
    }

    .submit-opinion-btn {
      display: inline-flex;
      align-items: center;
      gap: 10px;
      background: linear-gradient(135deg, #d71718, #b41616);
      color: #fff;
      padding: 15px 30px;
      border-radius: 25px;
      cursor: pointer;
      box-shadow: 0 8px 25px rgba(215, 23, 24, 0.3);
      transition: all 0.3s;
      font-size: 32px;
      font-weight: 300;
      font-family: 'AL-L';
      margin-top: 50px;

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(215, 23, 24, 0.4);
      }

      i {
        font-size: 32px;
      }
    }
  }

  .section {
    margin-bottom: 50px;
    position: relative;
    z-index: 1;

    .section-title {
      margin-bottom: 30px;

      h2 {
        color: #d71718;
        font-size: 28px;
        font-weight: 600;
        margin: 0 0 15px;
        font-family: 'AL-B' !important;
      }

      .title-line {
        width: 80px;
        height: 4px;
        background: linear-gradient(90deg, #d71718, #b41616);
        border-radius: 2px;
        margin-bottom: 20px;
      }

      .filter-bar {
        display: flex;
        gap: 30px;
        align-items: center;
        padding-left: 100px;

                  .filter-group {
            display: flex;
            align-items: center;
            gap: 15px;

            label {
              color: #666;
              font-size: 20px;
              font-weight: 500;
              font-family: 'AL-L';
            }

            select {
              padding: 12px 18px;
              border: 2px solid #e0e0e0;
              border-radius: 12px;
              background: #fff;
              color: #333;
              font-size: 18px;
              font-family: 'AL-L';
              outline: none;
              transition: all 0.3s;

              &:focus {
                border-color: #d71718;
              }
            }
          }
      }
    }
  }

  .categories-container {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;

    .category-card {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 12px;
      padding: 15px 20px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      display: flex;
      align-items: center;
      position: relative;
      border: 2px solid transparent;
      min-width: 140px;
      flex-direction: column;
      text-align: center;

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
      }

      &.active {
        border-color: #d71718;
        background: linear-gradient(135deg, rgba(215, 23, 24, 0.95), rgba(180, 22, 22, 0.95));
        color: #fff;

        .category-info h3,
        .opinion-count {
          color: #fff !important;
        }
      }

      .category-icon {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        background: rgba(215, 23, 24, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 12px;

        i {
          font-size: 24px;
          color: #d71718;
        }
      }

      &.active .category-icon {
        background: rgba(255, 255, 255, 0.2);

        i {
          color: #fff;
        }
      }

      .category-info {
        flex: 1;

        h3 {
          color: #d71718;
          font-size: 16px;
          font-weight: 600;
          margin: 0 0 8px;
          font-family: 'AL-B' !important;
        }

        .opinion-count {
          color: #666;
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
  }

  .opinions-container {
    max-width: calc(100% - 200px);
    margin: 0 auto;
    font-family: 'AL-L';

    .loading-container {
      text-align: center;
      padding: 60px 0;

      .loading-text {
        font-size: 24px;
        color: #666;
        font-family: 'AL-L';
      }
    }

    .no-data-container {
      text-align: center;
      padding: 60px 0;

      .no-data-text {
        font-size: 24px;
        color: #999;
        font-family: 'AL-L';
      }
    }

          .opinion-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 12px;
        padding: 18px;
        margin-bottom: 60px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
        border-left: 4px solid #d71718;
        cursor: pointer;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
        }

      .opinion-header {
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;

        .opinion-meta {
          display: flex;
          gap: 20px;
          align-items: center;
          flex-wrap: wrap;

          .opinion-id {
            color: #d71718;
            font-weight: 600;
            font-size: 20px;
          }

          .opinion-category {
            padding: 8px 16px;
            background: rgba(215, 23, 24, 0.1);
            color: #d71718;
            border-radius: 16px;
            font-size: 16px;
            font-weight: 500;
          }

          .opinion-date {
            color: #666;
            font-size: 18px;
          }

          .opinion-status {
            padding: 8px 18px;
            border-radius: 16px;
            font-size: 16px;
            font-weight: 500;

            &.待处理 {
              background: #fff3cd;
              color: #856404;
            }

            &.处理中 {
              background: #cce5ff;
              color: #004085;
            }

            &.已处理 {
              background: #d4edda;
              color: #155724;
            }
          }
        }

        .service-rating {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 5px;

          .rating-stars {
            display: flex;
            gap: 2px;

            .star {
              font-size: 18px;
              color: #ddd;

              &.filled {
                color: #ffd700;
              }
            }
          }

          .rating-text {
            font-size: 14px;
            color: #666;
            font-weight: 500;
          }
        }
      }

      .opinion-content {
        margin-bottom: 25px;

        h4 {
          color: #d71718;
          font-size: 32px;
          font-weight: 600;
          margin: 0 0 15px;
          font-family: 'AL-B' !important;
          line-height: 1.4;
        }

        .opinion-text {
          color: #555;
          font-size: 24px;
          line-height: 1.6;
          margin: 0 0 20px;
          text-align: left;
          text-indent: 2em;
        }

        .opinion-submitter {
          display: flex;
          gap: 25px;
          color: #666;
          font-size: 18px;
          flex-wrap: wrap;
        }
      }

      .feedback-section {
        background: rgba(215, 23, 24, 0.05);
        border-radius: 16px;
        padding: 30px;
        margin-bottom: 25px;
        border-left: 4px solid #d71718;

        .feedback-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          flex-wrap: wrap;
          gap: 15px;

          h5 {
            color: #d71718;
            font-size: 24px;
            font-weight: 600;
            margin: 0;
            font-family: 'AL-B' !important;
          }

          .feedback-meta {
            display: flex;
            gap: 20px;
            color: #666;
            font-size: 16px;
          }
        }

        .feedback-content {
          p {
            color: #666;
            font-size: 20px;
            line-height: 1.6;
            margin: 0;
            text-align: left;
            text-indent: 2em;
          }
        }
      }

      .replies-section {
        margin-bottom: 25px;

        .replies-header {
          margin-bottom: 20px;

          h6 {
            color: #d71718;
            font-size: 28px;
            font-weight: 600;
            margin: 0;
            font-family: 'AL-B' !important;
          }
        }

        .replies-list {
          .reply-item {
            padding: 18px 20px;
            margin-bottom: 15px;
            border-radius: 12px;
            position: relative;

            &.citizen {
              background: rgba(108, 117, 125, 0.1);
              margin-left: 0;
              margin-right: 50px;
            }

            &.representative {
              background: rgba(215, 23, 24, 0.05);
              margin-left: 50px;
              margin-right: 0;
            }

            .reply-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 12px;

              .reply-author {
                font-size: 28px !important;
                font-weight: 600;
                font-size: 18px;
                color: #333;
              }

              .reply-date {
                font-size: 28px;
                color: #999;
              }
            }

            .reply-content {
              font-size: 28px;
              line-height: 1.6;
              color: #555;
              text-align: left;
              text-indent: 2em;
            }
          }
        }
      }


    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 60px;

    .pagination-btn {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 20px;
      background: rgba(255, 255, 255, 0.9);
      border: 2px solid rgba(215, 23, 24, 0.2);
      border-radius: 25px;
      color: #666;
      font-size: 16px;
      font-weight: 500;
      font-family: 'AL-R';
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover:not(:disabled) {
        background: rgba(215, 23, 24, 0.1);
        border-color: rgba(215, 23, 24, 0.4);
        color: #d71718;
        transform: translateY(-2px);
      }

      &:disabled {
        opacity: 0.4;
        cursor: not-allowed;
      }

      .arrow-left, .arrow-right {
        width: 0;
        height: 0;
        border-style: solid;
      }

      .arrow-left {
        border-width: 6px 8px 6px 0;
        border-color: transparent currentColor transparent transparent;
      }

      .arrow-right {
        border-width: 6px 0 6px 8px;
        border-color: transparent transparent transparent currentColor;
      }
    }

    .page-numbers {
      display: flex;
      gap: 8px;

      .page-number {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(215, 23, 24, 0.2);
        border-radius: 50%;
        color: #666;
        font-size: 16px;
        font-weight: 500;
        font-family: 'AL-R';
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover:not(.active):not(:disabled) {
          background: rgba(215, 23, 24, 0.1);
          border-color: rgba(215, 23, 24, 0.4);
          color: #d71718;
          transform: scale(1.1);
        }

        &.active {
          background: #d71718;
          border-color: #d71718;
          color: #fff;
          box-shadow: 0 4px 15px rgba(215, 23, 24, 0.3);
        }

        &:disabled {
          cursor: default;
          opacity: 0.6;
        }
      }
    }
  }

  .back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 160px;
    height: 60px;
    border-radius: 30px;
    background: linear-gradient(135deg, #d71718, #b41616);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    box-shadow: 0 8px 25px rgba(215, 23, 24, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1000;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 12px 35px rgba(215, 23, 24, 0.4);
    }

    i {
      font-size: 24px;
    }

    span {
      font-size: 16px;
      font-weight: 500;
      font-family: 'AL-L';
    }
  }

    .back-button {
    position: fixed;
    bottom: 40px;
    right: 40px;
    width: 80px;
    height: 80px;
    background: rgba(215, 23, 24, 0.9);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(215, 23, 24, 0.3);
    backdrop-filter: blur(10px);
    z-index: 1000;

    &:hover {
      background: rgba(215, 23, 24, 1);
      transform: scale(1.1);
      box-shadow: 0 8px 30px rgba(215, 23, 24, 0.5);
    }

    .back-icon {
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 8px 12px 8px 0;
      border-color: transparent #fff transparent transparent;
      margin-bottom: 4px;
    }

    span {
      color: #fff;
      font-size: 12px;
      font-weight: 500;
      font-family: 'AL-R';
    }
  }
}

/* 4K分辨率适配 */
@media (min-width: 3000px) {
  .content {
    padding: 80px 800px 80px;

    .page-title {
      margin-bottom: 80px;

      h1 {
        font-size: 96px;
        margin: 0 0 30px;
      }

      .title-decoration {
        width: 160px;
        height: 6px;
        margin: 0 auto 30px;
      }

      p {
        font-size: 42px;
      }
    }

    .section {
      margin-bottom: 70px;

      .section-title {
        margin-bottom: 50px;

        h2 {
          font-size: 36px;
          margin: 0 0 20px;
        }

        .title-line {
          width: 80px;
          height: 4px;
          margin-bottom: 30px;
        }

        .filter-bar {
          gap: 50px;

          .filter-group {
            gap: 15px;

            label {
              font-size: 28px;
            }

            select {
              padding: 12px 18px;
              font-size: 28px;
              border-radius: 12px;
            }
          }
        }
      }
    }

    .categories-container {
      gap: 25px;

      .category-card {
        padding: 25px 30px;
        border-radius: 16px;
        min-width: 120px;
        font-family: 'AL-L';

        .category-icon {
          width: 100px;
          height: 100px;
          border-radius: 12px;
          margin-bottom: 15px;

          i {
            font-size: 64px;
          }
        }

        .category-info {
          h3 {
            font-size: 28px;
            margin: 0 0 10px;
          }

          .opinion-count {
            font-size: 28px;
          }
        }
      }
    }

    .opinions-container {
      .loading-container {
        padding: 80px 0;

        .loading-text {
          font-size: 32px;
        }
      }

      .no-data-container {
        padding: 80px 0;

        .no-data-text {
          font-size: 32px;
        }
      }

      .opinion-card {
        padding: 40px;
        border-radius: 24px;
        margin-bottom: 60px;
        border-left-width: 6px;

        .opinion-header {
          margin-bottom: 25px;

          .opinion-meta {
            gap: 25px;

            .opinion-id {
              font-size: 32px;
            }

            .opinion-category {
              padding: 10px 25px;
              font-size: 24px;
              border-radius: 20px;
            }

            .opinion-date {
              font-size: 24px;
            }

            .opinion-status {
              padding: 10px 25px;
              font-size: 24px;
              border-radius: 20px;
            }
          }

          .service-rating {
            gap: 8px;

            .rating-stars {
              gap: 4px;

              .star {
                font-size: 32px;
              }
            }

            .rating-text {
              font-size: 24px;
            }
          }
        }

        .opinion-content {
          margin-bottom: 30px;

          h4 {
            font-size: 48px;
            margin: 0 0 20px;
            text-align: center;
          }

          .opinion-text {
            font-size: 32px;
            margin: 0 0 25px;
          }

          .opinion-submitter {
            gap: 30px;
            font-size: 28px;
          }
        }

        .feedback-section {
          padding: 30px;
          border-radius: 16px;
          margin-bottom: 30px;
          border-left-width: 4px;

          .feedback-header {
            margin-bottom: 20px;

            h5 {
              font-size: 36px;
            }

            .feedback-meta {
              gap: 20px;
              font-size: 28px;
            }
          }

          .feedback-content {
            p {
              font-size: 30px;
              margin: 0;
            }
          }
        }


      }
    }

    .pagination-container {
      margin-top: 90px;
      gap: 30px;

      .pagination-btn {
        font-size: 24px;
        padding: 18px 30px;
        border-radius: 35px;

        .arrow-left {
          border-width: 9px 12px 9px 0;
        }

        .arrow-right {
          border-width: 9px 0 9px 12px;
        }
      }

      .page-numbers {
        gap: 12px;

        .page-number {
          width: 60px;
          height: 60px;
          font-size: 24px;
        }
      }
    }

    .replies-section {
      .replies-header {
        h6 {
          font-size: 32px;
        }
      }

      .replies-list {
        .reply-item {
          padding: 25px 30px;
          margin-bottom: 20px;
          border-radius: 16px;

          &.citizen {
            margin-right: 80px;
          }

          &.representative {
            margin-left: 80px;
          }

          .reply-header {
            margin-bottom: 15px;

            .reply-author {
              font-size: 28px;
            }

            .reply-date {
              font-size: 20px;
            }
          }

          .reply-content {
            font-size: 26px;
          }
        }
      }
    }

    .back-to-top {
      bottom: 60px;
      right: 650px;
      width: 220px;
      height: 80px;
      border-radius: 40px;
      gap: 15px;

      i {
        font-size: 32px;
      }

      span {
        font-size: 24px;
      }
    }

    .back-button {
      width: 120px;
      height: 120px;
      bottom: 60px;
      right: 60px;

      .back-icon {
        border-width: 12px 18px 12px 0;
        margin-bottom: 6px;
      }

      span {
        font-size: 24px;
      }
    }
  }
}

</style>
