<template>
  <div class="app-container bg">
    <ScreenHeader ref="headerComp" :lastPageNum="lastPageNum" />
    <div class="my-screen">
      <div class="deputy-left">
        <div class="deputy-info">
          <ScreenTitle caption="代表信息" more="" />
          <div class="deputy-info-content">
            <div class="top-info">
              <el-image class="img" :src="baseURL + deputy.avatar" fit="cover"></el-image>
              <div class="right-info">
                <div class="base-info">
                  <div class="name">{{deputy.name}}</div>
                  <div class="info">「单位」{{deputy.company}}</div>
                  <div class="info">「职务」{{deputy.duty}}</div>
                  <div class="info">
                    <div class="info-1">「民族」<dict-tag :options="dict.type.yw_mz" :value="deputy.nation"/></div>
                    <div class="info-2">「出生日期」{{ formatYear<PERSON>onth(deputy.birthday) }}</div>
                  </div>
                  <div class="info">
                    <div class="info-1" v-if="deputy.tel">「电话」{{deputy.tel}}</div>
                    <div class="info-2">「所属站点」{{deputy.stationName}}</div>
                  </div>
                </div>
                <el-image class="code" :src="baseURL + deputy.qrcodeUrl" fit="contain"></el-image>
              </div>
            </div>
            <div class="job-stat">
              <div class="title">履职档案</div>
              <div class="job-stat-content">
                <div class="item" v-for="(item, index) in deputyStat">
                  <div class="title">{{item.title}}</div>
                  <div class="count">{{item.count}}</div>
                </div>
              </div>
            </div>
            <div class="deputy-recom">
              <div class="title">代表建议</div>
              <div class="deputy-recom-content">
                <el-table
                  :data="deputyRecom"
                  stripe
                  v-el-table-infinite-scroll="loadDeputyRecom"
                  infinite-scroll-distance="5"
                  height="492px;"
                  :header-cell-style="headerCellStyle"
                  :cell-style="cellStyle"
                  @row-click="onPreview"
                >
                  <el-table-column prop="recomType" label="建议类型" align="center" width="170">
                    <template slot-scope="scope">
                      <dict-tag :options="dict.type.rd_recom_type" :value="scope.row.recomType"/>
                    </template>
                  </el-table-column>
                  <el-table-column prop="session" label="届次" align="center" width="200">
                    <template slot-scope="scope">
                      <div style="display: flex; flex-direction: row; justify-content: center;">
                        <dict-tag :options="dict.type.rd_session" :value="scope.row.session"/>
                        <dict-tag :options="dict.type.rd_times" :value="scope.row.times"/>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="title" label="建议标题" />
                </el-table>
                <el-alert v-if="isLoadingDeputyRecom" title="加载中..." type="success" center :closable="false" show-icon></el-alert>
                <el-alert v-if="noMoreDeputyRecom" title="没有更多了" type="warning" center show-icon></el-alert>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="deputy-right">
        <div class="deputy-job">
          <ScreenTitle caption="履职信息" more="" />
          <div class="deputy-job-content">
            <el-table
              :data="deputyJob"
              v-el-table-infinite-scroll="loadDeputyJob"
              infinite-scroll-distance="5"
              :show-header="false"
              height="492px;"
              @row-click="onDeputyJob"
            >
              <el-table-column prop="title" label="标题" align="center">
                <template slot-scope="scope">
                  <div class="job-item">
                    <el-image class="img" :src="selectImage(scope.row.attachments, defaultDblz)"></el-image>
                    <div class="right">
                      <div class="top">
                        <div class="title">{{scope.row.title}}</div>
                        <div class="content">{{scope.row.content}}</div>
                      </div>
                      <div class="footer">
                        <div class="time">{{formatReadableDate(scope.row.beginDate)}}</div>
                        <dict-tag :options="dict.type.job_type" :value="scope.row.jobType"/>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <el-alert v-if="isLoadingDeputyJob" title="加载中..." type="success" center :closable="false" show-icon></el-alert>
            <el-alert v-if="noMoreDeputyJob" title="没有更多了" type="warning" center show-icon></el-alert>
          </div>
        </div>
        <div class="mass-advice">
          <ScreenTitle caption="群众建议" more="" />
          <div class="mass-advice-content">
            <el-table
              :data="adviceList"
              v-el-table-infinite-scroll="loadAdvice"
              infinite-scroll-distance="5"
              :show-header="false"
              height="492px;"
              @row-click="onAdvice"
            >
              <el-table-column prop="title" label="标题" align="center">
                <template slot-scope="scope">
                  <div class="advice-item">
                    <div class="advice-header">
                      <div class="left">
                        <el-image class="img" :src="scope.row.massAvatar ? baseURL + scope.row.massAvatar : '@/assets/images/avatar.png'"></el-image>
                        <div class="name">{{scope.row.name ? scope.row.name : '匿名'}}</div>
                      </div>
                      <div class="advice-date">{{formatReadableDate(scope.row.createTime)}}</div>
                    </div>
                    <div class="advice-content"><B>建议：</B>{{scope.row.content}}</div>
                    <div v-if="scope.row.feedbackList && scope.row.feedbackList.length > 0">
                      <div class="seperator-dashed"></div>
                      <div class="advice-feedback"><B>答复：</B>{{scope.row.feedbackList[0].content}}</div>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <el-alert v-if="isLoadingAdvice" title="加载中..." type="success" center :closable="false" show-icon></el-alert>
            <el-alert v-if="noMoreAdvice" title="没有更多了" type="warning" center show-icon></el-alert>
          </div>
        </div>
      </div>
    </div>
    <el-image class="return-btn" :src="require('@/assets/images/screen/return.png')" @click="onBack"></el-image>

    <!-- 预览代表建议对话框 -->
    <el-dialog :visible.sync="openPreview" modal width="1240px" height="1640px" top="300px!important">
      <div slot="title" style="font-family: AL-R; font-size: 34px; text-align: center;">{{recomTitle}}</div>
      <iframe
        v-if="previewUrl"
        :src="previewUrl"
        width="1200px"
        height="1500px"
        frameborder="0"
        scrolling="yes" />
    </el-dialog>

  </div>
</template>

<script>

import ScreenHeader from './components/screenHeader';
import ScreenTitle from './components/screenTitle';
import { formatYearMonth, formatReadableDate } from '@/api/renda/utils';
import ElTableInfiniteScroll from "el-table-infinite-scroll";
import { getDeputy, getDeputyStat, listRecom, listJob, listAdviceWithFeedback } from '@/api/renda/screen';
import { Base64 } from 'js-base64'

export default {
  name: "DeputyDetail",
  components: { ScreenHeader, ScreenTitle },
  dicts: ['yw_mz', 'rd_recom_type', 'rd_session', 'rd_times', 'job_type'],
  directives: {
    "el-table-infinite-scroll": ElTableInfiniteScroll,
  },
  data() {
    return {
      defaultAvatar: require("@/assets/images/avatar.png"), // 默认头像
      defaultDblz: require('@/assets/images/screen/lzgz.png'), // 默认代表履职图片
      baseURL: process.env.VUE_APP_BASE_API, // 图片上传地址头
      deputyId: 0, // 人大代表id
      lastPageNum: 0, // 保存上一页的页码
      deputy: {}, // 人大代表信息
      deputyStat: [], // 人大代表统计信息
      pagesize: 10, // 每页显示条数

      deputyRecom: [], // 人大代表建议
      recomPage: 0, // 人大代表建议页码
      lastTimeLoadDeputyRecom: new Date().getTime(), // 上次加载时间
      isLoadingDeputyRecom: false,
      noMoreDeputyRecom: false,
      openPreview: false, // 打开预览对话框
      recomTitle: '', // 预览文件标题
      previewUrl: '', // 预览文件地址

      deputyJob: [], // 人大代表建议
      jobPage: 0, // 人大代表建议页码
      lastTimeLoadDeputyJob: new Date().getTime(), // 上次加载时间
      isLoadingDeputyJob: false,
      noMoreDeputyJob: false,

      adviceList: [], // 群众建议
      advicePage: 0, // 群众建议页码
      lastTimeLoadAdvice: new Date().getTime(), // 上次加载时间
      isLoadingAdvice: false,
      noMoreAdvice: false,

    };
  },
  activated() {
    this.deputyId = this.$route.query.deputyId;
    this.lastPageNum = Number(this.$route.query.lastPageNum);
    if (!this.deputyId) {
      this.$router.go(-1)
    }
    this.loadData();
  },
  mounted() {
    this.$nextTick(() => {
      document.oncontextmenu = function() {
        return false;
      };
    });
  },
  methods: {
    loadData() {
      getDeputy(this.deputyId).then(res => {
        this.deputy = res.data;
      });
      getDeputyStat(this.deputyId).then(res => {
        this.deputyStat = res.data;
      });
      let lastTime = new Date().getTime() - 2000;
      this.lastTimeLoadDeputyRecom = lastTime;
      this.recomPage = 0;
      this.deputyRecom = [];
      this.loadDeputyRecom();
      this.lastTimeLoadDeputyJob = lastTime;
      this.jobPage = 0;
      this.deputyJob = [];
      this.loadDeputyJob();
      this.lastTimeLoadAdvice = lastTime;
      this.advicePage = 0;
      this.adviceList = [];
      this.loadAdvice();
    },
    onBack() {
      this.$refs.headerComp.onClick();
    },
    formatYearMonth(date) {
      return formatYearMonth(date)
    },
    loadDeputyRecom() {
      let nowTime = new Date().getTime()
      let diffTime = ( nowTime - this.lastTimeLoadDeputyRecom ) / 1000
      if (diffTime > 1) {
        this.isLoadingDeputyRecom = true;
        this.recomPage++;
        listRecom({
          deputyId: this.deputyId,
          pageNum: this.recomPage,
          pageSize: this.pagesize,
        }).then(res => {
          this.isLoadingDeputyRecom = false;
          if (this.recomPage > Math.floor(res.total / this.pagesize) + 1 ) {
            this.recomPage = Math.floor(res.total / this.pagesize) + 1;
            this.noMoreDeputyRecom = true;
            setTimeout(()=>{
              this.noMoreDeputyRecom = false
            },1000)
          } else {
            this.deputyRecom = this.deputyRecom.concat(res.rows);
          }
        });
      }
      this.lastTimeLoadDeputyRecom = nowTime
    },
    onPreview(row) {
      let fileUrl = row.recomFileUrl;
      if (fileUrl) {
        let file = 'https://rd.juruifeng.cn:9000/prod-api' + fileUrl; //要预览文件的访问地址
        let url = 'https://rd.juruifeng.cn:8888/onlinePreview?url='+encodeURIComponent(Base64.encode(file));
        this.previewUrl = url;
        this.recomTitle = row.title;
        this.openPreview = true;
      }
    },
    headerCellStyle() {
      return 'padding: 10px 20px; font-family: AL-R; font-size: 26px; font-weight: 600; line-height: 50px !important; color: #5a5e66; background-color: rgba(240, 64, 64, 0.2);';
    },
    cellStyle() {
      return 'padding: 40px 20px; font-family: AL-R; font-size: 26px; font-weight: 400; line-height: 50px !important; color: #5a5e66; background-color: #fff;';
    },
    formatReadableDate(date) {
      return formatReadableDate(date);
    },
    loadDeputyJob() {
      let nowTime = new Date().getTime()
      let diffTime = ( nowTime - this.lastTimeLoadDeputyJob ) / 1000
      if (diffTime > 1) {
        this.isLoadingDeputyJob = true;
        this.jobPage++;
        listJob({
          deputyId: this.deputyId,
          pageNum: this.jobPage,
          pageSize: this.pagesize,
        }).then(res => {
          this.isLoadingDeputyJob = false;
          if (this.jobPage > Math.floor(res.total / this.pagesize) + 1 ) {
            this.jobPage = Math.floor(res.total / this.pagesize) + 1;
            this.noMoreDeputyJob = true;
            setTimeout(()=>{
              this.noMoreDeputyJob = false
            },1000)
          } else {
            this.deputyJob = this.deputyJob.concat(res.rows);
          }
        });
      }
      this.lastTimeLoadDeputyJob = nowTime
    },
    onDeputyJob(row) {
      this.$router.push({ path: "JobDetail", query: { jobId: row.id, deputyId: row.deputyId }});
    },
    loadAdvice() {
      let nowTime = new Date().getTime()
      let diffTime = ( nowTime - this.lastTimeLoadAdvice ) / 1000
      if (diffTime > 1) {
        this.isLoadingAdvice = true;
        this.advicePage++;
        listAdviceWithFeedback({
          deputyId: this.deputyId,
          pageNum: this.advicePage,
          pageSize: this.pagesize,
        }).then(res => {
          this.isLoadingAdvice = false;
          if (this.advicePage > Math.floor(res.total / this.pagesize) + 1 ) {
            this.advicePage = Math.floor(res.total / this.pagesize) + 1;
            this.noMoreAdvice = true;
            setTimeout(()=>{
              this.noMoreAdvice = false
            },1000)
          } else {
            this.adviceList = this.adviceList.concat(res.rows);
          }
        });
      }
      this.lastTimeLoadAdvice = nowTime
    },
    onAdvice(row) {
      this.$router.push({ path: "AdviceDetail", query: { adviceId: row.id }});
    },
    selectImage(attachmentList, defaultImage) {
      if (!attachmentList) {
        return defaultImage
      }
      let image = attachmentList.find(item => item.fileType === 1);
      if (image) {
        return this.baseURL + image.fileUrl
      } else {
        return defaultImage
      }
    },
  }
};
</script>

<style lang="scss" scoped>

@import '../../../common/font/font.css';

.my-screen {
  margin: 250px 540px 100px 540px;
  padding: 50px 50px;
  height: 1826px;
  display: flex;
  flex-direction: row;
  //background-color: #fff;

  .deputy-left {
    width: 1500px;
    padding: 0 50px;
    display: flex;
    flex-direction: column;
    .deputy-info {
      display: flex;
      flex-direction: column;
      width: 100%;
      flex-shrink: 0;
      .deputy-info-content {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 1616px;
        background: #FFF6EF;
        margin-top: 20px;
        padding: 30px;
        overflow: hidden;
        .top-info {
          display: flex;
          flex-direction: row;
          margin: 32px;
          .img {
            width: 225px;
            height: 300px;
            border: #ffffff solid 10px;
            box-shadow: 0px 0px 12px 0px rgba(0,0,0,0.1);
            flex-shrink: 0;
          }
          .right-info {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            width: 100%;
            padding: 10px 30px;
            .base-info {
              display: flex;
              flex-direction: column;
              width: 100%;
              text-align: left;
              .name {
                font-size: 48px;
                font-family: AL-B;
                font-weight: bold;
                color: #000000;
              }
              .duty {
                margin: 10px 0 30px 0;
                font-size: 26px;
                font-family: AL-R;
                font-weight: 300;
                color: #504B4A;
              }
              .info {
                margin: 10px 0;
                display: flex;
                flex-direction: row;
                font-size: 26px;
                font-family: AL-R;
                font-weight: 400;
                color: #504B4A;
                .info-1 {
                  width: 400px;
                  display: flex;
                }
                .info-2 {
                  display: flex;
                }
              }
            }
            .code {
              width: 200px;
              height: 200px;
            }
          }
        }
        .job-stat {
          margin: 32px;
          display: flex;
          flex-direction: column;
          //background-color: #1ab394;
          .title {
            font-size: 32px;
            font-family: AL-B;
            color: #5a5e66;
          }
          .job-stat-content {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: space-between;
            margin: 20px 0;
            .item {
              width: 600px;
              height: 70px;
              margin: 4px 0;
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: space-between;
              background-color: #ffffff;
              box-shadow: 0px 0px 2px 0px rgba(0,0,0,0.3);
              .title {
                padding: 10px 20px;
                align-items: center;
                font-size: 26px;
                font-family: AL-R;
              }
              .count {
                width: 70px;
                height: 70px;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 36px;
                font-family: AL-R;
                background-color: rgba(240, 64, 64, 0.2);
              }
            }
          }
        }
        .deputy-recom {
          margin: 32px;
          display: flex;
          flex-direction: column;
          //background-color: #1ab394;
          .title {
            font-size: 32px;
            font-family: AL-B;
            color: #5a5e66;
          }
          .deputy-recom-content {
            display: flex;
            flex-direction: column;
            margin: 20px 0;
            height: 492px;
          }
        }
      }
    }
  }
  .deputy-right {
    width: 1120px;
    padding: 0 50px;
    display: flex;
    flex-direction: column;
    .deputy-job {
      display: flex;
      flex-direction: column;
      width: 100%;
      flex-shrink: 0;
      .deputy-job-content {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 916px;
        background: #FFF6EF;
        margin-top: 20px;
        padding: 30px;
        overflow: hidden;
        .job-item {
          width: 100%;
          height: 240px;
          margin: 12px 0;
          padding: 0 12px;
          display: flex;
          flex-direction: row;
          //justify-content: space-between;
          .img {
            display: flex;
            width: 360px;
            height: 240px;
            flex-shrink: 0;
          }
          .right {
            padding: 10px 30px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .top {
              display: flex;
              flex-direction: column;
              text-align: left;
              .title {
                font-family: AL-B;
                font-size: 28px;
                line-height: 34px;
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 2;
                display: -webkit-box;
                -webkit-box-orient: vertical;
              }
              .content {
                margin-top: 15px;
                font-family: AL-R;
                font-size: 22px;
                line-height: 30px;
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 3;
                display: -webkit-box;
                -webkit-box-orient: vertical;
              }
            }
            .footer {
              display: flex;
              flex-direction: row;
              justify-content: space-between;
              .time {
                font-family: AL-L;
                font-size: 22px;
              }
              .type {
                font-family: AL-R;
                font-size: 20px;
              }
            }
          }
        }
      }
    }
    .mass-advice {
      width: 100%;
      margin-top: 50px;
      display: flex;
      flex-direction: column;
      flex-shrink: 0;
      .mass-advice-content {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 540px;
        background: #FFF6EF;
        margin-top: 20px;
        padding: 30px;
        overflow: hidden;
        .advice-item {
          margin: 12px 0;
          padding: 0 12px;
          display: flex;
          flex-direction: column;
          .advice-header {
            width: 100%;
            height: 64px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            .left {
              width: 100%;
              height: 64px;
              display: flex;
              flex-direction: row;
              align-items: center;
              .img {
                width: 64px;
                height: 64px;
                border-radius: 32px;
                border: #fff solid 2px;
                box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.5);
                display: flex;
                flex-shrink: 0;
              }
              .name {
                margin-left: 20px;
                font-size: 30px;
                font-family: AL-B;
                font-weight: 300;
                color: #2F2F2F;
              }
            }
            .advice-date {
              display: flex;
              width: 120px;
              height: 64px;
              text-align: right;
              align-items: center;
              font-size: 24px;
              font-family: AL-R;
              font-weight: 300;
              color: #999694;
            }
          }
          .advice-content {
            margin: 12px 0;
            font-family: AL-B;
            font-size: 24px;
            line-height: 30px;
            text-align: left;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 2;
            display: -webkit-box;
            -webkit-box-orient: vertical;
          }
          .advice-feedback {
            margin: 12px 0;
            font-family: AL-R;
            font-size: 24px;
            line-height: 30px;
            text-align: left;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 3;
            display: -webkit-box;
            -webkit-box-orient: vertical;
          }
        }
      }
    }
  }
}

</style>
