import request from '@/utils/request'

// 根据设备ID获取联络站ID
export function getStationByDevice(deviceId) {
  return request({
    url: '/renda/stationScreen/getStationByDevice',
    method: 'post',
    data: { deviceId }
  })
}

// 根据联络站ID获取统计数据
export function getStationStatistics(stationId) {
  return request({
    url: `/renda/stationScreen/getStationStatistics/${stationId}`,
    method: 'get'
  })
}

// 根据联络站ID获取组织机构信息
export function getOrganizationData(stationId) {
  return request({
    url: `/renda/stationScreen/getOrganizationData/${stationId}`,
    method: 'get'
  })
}

// 根据联络站ID获取排班数据（支持日期范围）
export function getScheduleData(stationId, startDate, endDate) {
  const params = {}
  if (startDate) {
    params.startDate = startDate
  }
  if (endDate) {
    params.endDate = endDate
  }
  
  return request({
    url: `/renda/stationScreen/getScheduleData/${stationId}`,
    method: 'get',
    params
  })
}

// 获取意见分类数据
export function getAdviceCategories(stationId) {
  return request({
    url: '/renda/stationScreen/getAdviceCategories',
    method: 'get',
    params: { stationId }
  })
}

// 获取意见建议列表
export function getAdviceList(params) {
  return request({
    url: '/renda/stationScreen/getAdviceList',
    method: 'get',
    params: params
  })
}

// 获取意见建议详情（包含反馈信息）
export function getAdviceDetail(adviceId) {
  return request({
    url: `/renda/stationScreen/getAdviceDetail/${adviceId}`,
    method: 'get'
  })
}

// 获取意见反馈列表
export function getFeedbackList(adviceId) {
  return request({
    url: `/renda/stationScreen/getFeedbackList/${adviceId}`,
    method: 'get'
  })
}

// 根据代表ID获取代表详情及履职档案
export function getRepresentativeDetail(deputyId) {
  return request({
    url: `/renda/stationScreen/getRepresentativeDetail/${deputyId}`,
    method: 'get'
  })
}

// 获取热门政策列表
export function getHotPolicies(stationId) {
  return request({
    url: `/renda/stationScreen/getHotPolicies/${stationId}`,
    method: 'get'
  })
}

// 获取最新动态列表
export function getLatestNews(stationId) {
  return request({
    url: `/renda/stationScreen/getLatestNews/${stationId}`,
    method: 'get'
  })
}

// 获取政策列表
export function getPolicyList(params) {
  return request({
    url: '/renda/stationScreen/getPolicyList',
    method: 'get',
    params: params
  })
}

// 获取政策详情
export function getPolicyDetail(policyId) {
  return request({
    url: `/renda/stationScreen/getPolicyDetail/${policyId}`,
    method: 'get'
  })
}

// 获取新闻动态列表
export function getNewsList(params) {
  return request({
    url: '/renda/stationScreen/getNewsList',
    method: 'get',
    params: params
  })
}

// 获取新闻动态详情
export function getNewsDetail(newsId) {
  return request({
    url: `/renda/stationScreen/getNewsDetail/${newsId}`,
    method: 'get'
  })
}