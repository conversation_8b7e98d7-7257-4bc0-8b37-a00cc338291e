package com.renda.core.controller;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import com.renda.common.config.RendaConfig;
import com.renda.common.config.WxMaConfiguration;
import com.renda.common.core.controller.BaseController;
import com.renda.common.core.domain.AjaxResult;
import com.renda.common.core.domain.entity.RdStation;
import com.renda.common.core.domain.entity.SysDictData;
import com.renda.common.core.domain.entity.SysUser;
import com.renda.common.core.domain.model.LoginUser;
import com.renda.common.core.page.TableDataInfo;
import com.renda.common.utils.SecurityUtils;
import com.renda.common.utils.bean.BeanUtils;
import com.renda.common.utils.file.FileUploadUtils;
import com.renda.core.domain.RdAdvice;
import com.renda.core.domain.RdDeputy;
import com.renda.core.domain.RdMass;
import com.renda.core.domain.vo.*;
import com.renda.core.mapper.RdMassMapper;
import com.renda.core.service.*;
import com.renda.framework.web.service.TokenService;
import com.renda.system.service.ISysConfigService;
import com.renda.system.service.ISysDictDataService;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * 小程序群众端Controller
 *
 * <AUTHOR>
 * @date 2023-10-30
 */
@RestController
@RequestMapping("/renda/api/mass")
public class RdApiMassController extends BaseController
{

    /**
     * 哈密市人大代表智慧管理平台小程序-群众端
     */
    private static final String appid = "wxc6f998e9a2de87fc";

    @Autowired
    private RdMassMapper massMapper;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private IRdDeputyService deputyService;

    @Autowired
    private IRdAdviceService adviceService;

    @Autowired
    private IRdMassService massService;

    @Autowired
    private IRdFeedbackService feedbackService;

    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private IRdStationService stationService;

    @Autowired
    private ISysConfigService configService;


    /***
     * 登录接口
     * @param loginInfo 登录信息
     * @return 登录结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginInfo loginInfo) throws WxErrorException {

        final WxMaService wxService = WxMaConfiguration.getMaService(appid);

        // 用phoneCode换取手机号
        WxMaPhoneNumberInfo phoneNoInfo = wxService.getUserService().getPhoneNoInfo(loginInfo.getPhoneCode());
        String phone = phoneNoInfo.getPhoneNumber();

        // 用loginCode换取openid
        WxMaJscode2SessionResult sessionInfo = wxService.getUserService().getSessionInfo(loginInfo.getLoginCode());
        String openid = sessionInfo.getOpenid();

        // 根据openid获取群众
        RdMass mass = massMapper.selectRdMassByOpenid(openid);
        if (ObjectUtils.isEmpty(mass)) {
            mass = new RdMass();
            mass.setName("微信用户");
            mass.setPhone(phone);
            mass.setOpenid(openid);
            Date now = new Date();
            mass.setCreateTime(now);
            mass.setUpdateTime(now);
            massMapper.insertRdMass(mass);
        } else {
            mass.setPhone(phone);
            mass.setUpdateTime(new Date());
            massMapper.updateRdMass(mass);
        }

        // 获取token
        SysUser user = new SysUser();
        user.setUserName("mass_" + phone);
        user.setPhonenumber(phone);
        user.setUserId(mass.getId());
        LoginUser loginUser = new LoginUser();
        loginUser.setUserId(mass.getId());
        loginUser.setUser(user);
        String token = tokenService.createTokenWx(loginUser);

        // 返回结果
        AjaxResult result = AjaxResult.success();
        result.put("userInfo", mass);
        result.put("token", token);

        return result;

    }

    /***
     * 获取人大代表信息接口
     * @param deputy 人大代表
     * @return 人大代表信息
     */
    @PostMapping("/getDeputyInfo")
    public AjaxResult getDeputyInfo(@RequestBody RdDeputy deputy) {
        DeputyInfoVO result = deputyService.selectRdDeputyInfoById(deputy.getId());
        result.setPhone("******");
        return AjaxResult.success(result);
    }

    /***
     * 提交意见建议接口
     * @param adviceInfoVO 意见建议
     * @return 提交结果
     */
    @PostMapping("/saveAdvice")
    public AjaxResult saveAdvice(@RequestBody AdviceInfoVO adviceInfoVO) {
        adviceService.saveAdvice(adviceInfoVO);
        return AjaxResult.success();
    }

    /***
     * 文件上传接口
     * @param file 文件
     * @return 文件名
     */
    @PostMapping(value = "/fileUpload")
    public AjaxResult uploadFile(@RequestParam("file") MultipartFile file) {
        try {
            // 保存文件
            String filename = FileUploadUtils.upload(RendaConfig.getUploadPath(), file);
            return AjaxResult.success(filename);
        } catch (IOException e) {
            e.printStackTrace();
            return AjaxResult.error(e.getMessage());
        }
    }

    /***
     * 获取用户统计信息接口
     * @return 用户统计信息
     */
    @PostMapping("/getMassStatistics")
    public AjaxResult getMassStatistics() {
        MassStatisticsInfoVO result = massService.getMassStatistics(SecurityUtils.getLoginUser().getUserId());
        return AjaxResult.success(result);
    }

    /***
     * 获取用户建议列表接口
     * @return 用户建议列表
     */
    @PostMapping("/getMassAdviceList")
    public AjaxResult getMassAdviceList() {
        List<MassAdviceInfoVO> result = massService.getMassAdviceList(SecurityUtils.getLoginUser().getUserId());
        return AjaxResult.success(result);
    }

    /***
     * 获取建议意见详情接口
     * @return 建议意见详情
     */
    @PostMapping("/getAdviceDetail")
    public AjaxResult getAdviceDetail(@RequestBody AdviceInfoVO adviceInfoVO) {

        // 获取建议详情
        AdviceInfoVO adviceInfo = adviceService.selectAdviceInfoById(adviceInfoVO.getAdviceId());
        if (ObjectUtils.isEmpty(adviceInfo)) {
            return AjaxResult.error("未找到该建议");
        }

        // 获取人大代表信息
        RdDeputy deputy = deputyService.selectRdDeputyById(adviceInfo.getDeputyId());
        if (ObjectUtils.isEmpty(deputy)) {
            return AjaxResult.error("未找到人大代表信息");
        }
        DeputyInfoVO deputyInfo = new DeputyInfoVO();
        BeanUtils.copyBeanProp(deputyInfo, deputy);

        // 获取建议回复列表
        List<FeedbackInfoVO> feedbackList = feedbackService.selectFeedbackListWithAttachs(adviceInfoVO.getAdviceId());

        AjaxResult result = AjaxResult.success();
        result.put("deputyInfo", deputyInfo);
        result.put("adviceInfo", adviceInfo);
        result.put("feedbackList", feedbackList);
        return result;

    }

    /***
     * 提交反馈接口
     * @param feedbackInfo2VO 意见建议
     * @return 提交结果
     */
    @PostMapping("/saveMassFeedback")
    public AjaxResult saveMassFeedback(@RequestBody FeedbackInfo2VO feedbackInfo2VO) {
        // 群众
        feedbackService.saveMassFeedback(feedbackInfo2VO, 1);
        return AjaxResult.success();
    }

    /***
     * 修改个人昵称头像接口
     * @param mass 群众
     * @return 提交结果
     */
    @PostMapping("/saveMassInfo")
    public AjaxResult saveMassInfo(@RequestBody RdMass mass) {
        // 群众
        mass.setId(SecurityUtils.getLoginUser().getUserId());
        massService.updateRdMass(mass);
        return AjaxResult.success(mass);
    }

    /***
     * 获取后台数据字典接口
     * @return 后台数据字典信息
     */
    @PostMapping("/loadDicts")
    public AjaxResult loadDicts(@RequestBody SysDictData sysDictData) {
        return AjaxResult.success(dictDataService.selectSmallDictDataList(sysDictData));
    }

    /***
     * 获取人大代表列表接口
     * @return 人大代表列表
     */
    @GetMapping("/getDeputyList")
    public TableDataInfo getDeputyList(RdDeputy rdDeputy) {
        startPage();
        List<RdDeputy> result = deputyService.selectRdDeputyList2(rdDeputy);
        result.forEach(deputy -> {
            deputy.setPhone("******");
        });
        return getDataTable(result);
    }

    /***
     * 获取站点列表接口
     * @return 站点列表
     */
    @PostMapping("/getStationList")
    public AjaxResult getStationList(@RequestBody RdStation rdStation) {
        List<RdStation> result = stationService.selectStationList(rdStation);
        return AjaxResult.success(result);
    }

    /***
     * 获取系统参数
     * @param configKey 参数key
     * @return 系统参数
     */
    @GetMapping("/getSysConfig")
    public AjaxResult getSysConfig(String configKey) {
        return AjaxResult.success("OK", configService.selectConfigByKey(configKey));
    }

    /***
     * 建议办结接口
     * @return 建议办结结果
     */
    @PostMapping("/completeAdvice")
    public AjaxResult completeAdvice(@RequestBody RdAdvice advice) {
        adviceService.completeAdvice( advice);
        return AjaxResult.success();
    }

}
