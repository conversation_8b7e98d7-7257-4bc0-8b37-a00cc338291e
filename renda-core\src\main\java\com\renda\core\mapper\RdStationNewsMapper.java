package com.renda.core.mapper;

import java.util.List;
import com.renda.core.domain.RdStationNews;

/**
 * 联络站动态Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface RdStationNewsMapper 
{
    /**
     * 查询联络站动态
     * 
     * @param id 联络站动态主键
     * @return 联络站动态
     */
    public RdStationNews selectRdStationNewsById(Long id);

    /**
     * 查询联络站动态列表
     * 
     * @param rdStationNews 联络站动态
     * @return 联络站动态集合
     */
    public List<RdStationNews> selectRdStationNewsList(RdStationNews rdStationNews);

    /**
     * 新增联络站动态
     * 
     * @param rdStationNews 联络站动态
     * @return 结果
     */
    public int insertRdStationNews(RdStationNews rdStationNews);

    /**
     * 修改联络站动态
     * 
     * @param rdStationNews 联络站动态
     * @return 结果
     */
    public int updateRdStationNews(RdStationNews rdStationNews);

    /**
     * 删除联络站动态
     * 
     * @param id 联络站动态主键
     * @return 结果
     */
    public int deleteRdStationNewsById(Long id);

    /**
     * 批量删除联络站动态
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRdStationNewsByIds(Long[] ids);
}
