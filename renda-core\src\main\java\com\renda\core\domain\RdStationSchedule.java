package com.renda.core.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.renda.common.annotation.Excel;
import com.renda.common.core.domain.BaseEntity;

/**
 * 排班表管理对象 rd_station_schedule
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
public class RdStationSchedule extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 联络站ID */
    @Excel(name = "联络站ID")
    private Long stationId;

    /** 联络站名称 */
    @Excel(name = "联络站名称")
    private String stationName;

    /** 排班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "排班日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date scheduleDate;

    /** 值班代表ID */
    @Excel(name = "值班代表ID")
    private Long deputyId;

    /** 值班代表姓名 */
    @Excel(name = "值班代表")
    private String deputyName;

    /** 值班工作人员ID */
    @Excel(name = "值班工作人员ID")
    private Long staffId;

    /** 值班工作人员姓名 */
    @Excel(name = "值班工作人员")
    private String staffName;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setStationId(Long stationId)
    {
        this.stationId = stationId;
    }

    public Long getStationId()
    {
        return stationId;
    }

    public void setStationName(String stationName)
    {
        this.stationName = stationName;
    }

    public String getStationName()
    {
        return stationName;
    }

    public void setScheduleDate(Date scheduleDate)
    {
        this.scheduleDate = scheduleDate;
    }

    public Date getScheduleDate()
    {
        return scheduleDate;
    }
    public void setDeputyId(Long deputyId)
    {
        this.deputyId = deputyId;
    }

    public Long getDeputyId()
    {
        return deputyId;
    }

    public void setDeputyName(String deputyName)
    {
        this.deputyName = deputyName;
    }

    public String getDeputyName()
    {
        return deputyName;
    }

    public void setStaffId(Long staffId)
    {
        this.staffId = staffId;
    }

    public Long getStaffId()
    {
        return staffId;
    }

    public void setStaffName(String staffName)
    {
        this.staffName = staffName;
    }

    public String getStaffName()
    {
        return staffName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("stationId", getStationId())
            .append("stationName", getStationName())
            .append("scheduleDate", getScheduleDate())
            .append("deputyId", getDeputyId())
            .append("deputyName", getDeputyName())
            .append("staffId", getStaffId())
            .append("staffName", getStaffName())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
