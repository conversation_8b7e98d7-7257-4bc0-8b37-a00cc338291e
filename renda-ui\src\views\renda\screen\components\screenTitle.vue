<template>
  <div class="screentitle" @click="onClickMore">
    <div class="flex row">
      <div class="caption">{{caption}}</div>
    </div>
    <div class="more">{{more}}</div>
  </div>
</template>

<script>

export default {
  name: "ScreenTitle",
  props: {
    imgUrl: {
      type: String,
      default: ''
    },
    caption: {
      type: String,
      default: ''
    },
    more: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
    };
  },
  methods: {
    handleSrc(url) {
      return require("@" + url);
    },
    onClickMore() {
      this.$emit("clickMore");
    },
  }
};
</script>

<style lang="scss" scoped>

@import '/src/common/font/font.css';

.screentitle {
  width: 100%;
  height: 90px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-top: 12px;
  padding-left: 10%;
  background: url("~@/assets/images/screen/title.png") no-repeat;
  background-size: 100% 100%;
  flex-shrink: 0;
  .img {
    width: 32px;
    height: 32px;
  }
  .caption {
    font-family: AL-B;
    font-size: 36px;
    color: #FFF;
  }
  .more {
    margin-right: 120px;
    font-family: AL-R;
    font-size: 30px;
    color: #F9B8B8;
  }
}

</style>
