package com.renda.core.service;

import java.util.List;
import com.renda.core.domain.RdFeedback;
import com.renda.core.domain.vo.FeedbackInfo2VO;
import com.renda.core.domain.vo.FeedbackInfoVO;

/**
 * 人大代反馈意见Service接口
 *
 * <AUTHOR>
 * @date 2023-11-04
 */
public interface IRdFeedbackService
{
    /**
     * 查询人大代反馈意见
     *
     * @param id 人大代反馈意见主键
     * @return 人大代反馈意见
     */
    public RdFeedback selectRdFeedbackById(Long id);

    /**
     * 查询人大代反馈意见列表
     *
     * @param rdFeedback 人大代反馈意见
     * @return 人大代反馈意见集合
     */
    public List<RdFeedback> selectRdFeedbackList(RdFeedback rdFeedback);

    /**
     * 新增人大代反馈意见
     *
     * @param rdFeedback 人大代反馈意见
     * @return 结果
     */
    public int insertRdFeedback(RdFeedback rdFeedback);

    /**
     * 修改人大代反馈意见
     *
     * @param rdFeedback 人大代反馈意见
     * @return 结果
     */
    public int updateRdFeedback(RdFeedback rdFeedback);

    /**
     * 批量删除人大代反馈意见
     *
     * @param ids 需要删除的人大代反馈意见主键集合
     * @return 结果
     */
    public int deleteRdFeedbackByIds(Long[] ids);

    /**
     * 删除人大代反馈意见信息
     *
     * @param id 人大代反馈意见主键
     * @return 结果
     */
    public int deleteRdFeedbackById(Long id);

    /***
     * 获取建议回复列表
     * @param adviceId
     * @return
     */
    List<FeedbackInfoVO> selectFeedbackListWithAttachs(Long adviceId);

    /***
     * 提交反馈接口
     * @param feedbackInfo2VO 意见建议
     * @return
     */
    void saveMassFeedback(FeedbackInfo2VO feedbackInfo2VO, Integer userType);
}
