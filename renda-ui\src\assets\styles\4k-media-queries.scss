/* 4K分辨率媒体查询样式 */

/* 定义在3840*2160分辨率下的元素缩放比例 */
$scale-4k: 1.5;

/* 全局4K屏幕媒体查询 */
@mixin for-4k-screens {
  @media (min-width: 3000px) {
    @content;
  }
}

/* 通用标题大小 */
@mixin heading-4k {
  @include for-4k-screens {
    h1 {
      font-size: 48px;
    }
    
    h2 {
      font-size: 40px;
    }
    
    h3 {
      font-size: 32px;
    }
    
    h4 {
      font-size: 28px;
    }
  }
}

/* 通用文本大小 */
@mixin text-4k {
  @include for-4k-screens {
    font-size: 24px;
    
    p {
      font-size: 24px;
      line-height: 1.6;
    }
    
    .small-text {
      font-size: 20px;
    }
  }
}

/* 通用按钮大小 */
@mixin button-4k {
  @include for-4k-screens {
    padding: 15px 30px;
    font-size: 24px;
    border-radius: 10px;
  }
}

/* 通用卡片样式 */
@mixin card-4k {
  @include for-4k-screens {
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.1);
  }
}

/* Element UI组件的4K适配 */
@mixin element-ui-4k {
  @include for-4k-screens {
    .el-button {
      font-size: 20px;
      padding: 12px 25px;
    }
    
    .el-input__inner {
      font-size: 20px;
      height: 50px;
      line-height: 50px;
    }
    
    .el-table {
      font-size: 20px;
      
      th, td {
        padding: 15px 20px;
      }
    }
    
    .el-pagination {
      font-size: 20px;
      
      .btn-prev, .btn-next {
        width: 45px;
        height: 45px;
      }
      
      .el-pager li {
        min-width: 45px;
        height: 45px;
        line-height: 45px;
        font-size: 20px;
      }
    }
    
    .el-dialog {
      width: 50%;
      
      .el-dialog__title {
        font-size: 28px;
      }
      
      .el-dialog__body {
        padding: 30px;
        font-size: 20px;
      }
    }
    
    .el-form {
      .el-form-item__label {
        font-size: 20px;
        line-height: 50px;
      }
      
      .el-form-item {
        margin-bottom: 25px;
      }
    }
  }
}

/* 通用间距调整 */
@mixin spacing-4k {
  @include for-4k-screens {
    .mt-1 { margin-top: 10px; }
    .mt-2 { margin-top: 20px; }
    .mt-3 { margin-top: 30px; }
    .mt-4 { margin-top: 40px; }
    .mt-5 { margin-top: 50px; }
    
    .mb-1 { margin-bottom: 10px; }
    .mb-2 { margin-bottom: 20px; }
    .mb-3 { margin-bottom: 30px; }
    .mb-4 { margin-bottom: 40px; }
    .mb-5 { margin-bottom: 50px; }
    
    .ml-1 { margin-left: 10px; }
    .ml-2 { margin-left: 20px; }
    .ml-3 { margin-left: 30px; }
    .ml-4 { margin-left: 40px; }
    .ml-5 { margin-left: 50px; }
    
    .mr-1 { margin-right: 10px; }
    .mr-2 { margin-right: 20px; }
    .mr-3 { margin-right: 30px; }
    .mr-4 { margin-right: 40px; }
    .mr-5 { margin-right: 50px; }
    
    .p-1 { padding: 10px; }
    .p-2 { padding: 20px; }
    .p-3 { padding: 30px; }
    .p-4 { padding: 40px; }
    .p-5 { padding: 50px; }
  }
}

/* 通用图标大小 */
@mixin icon-4k {
  @include for-4k-screens {
    i {
      font-size: 28px;
    }
    
    .el-icon-s-promotion, 
    .el-icon-reading, 
    .el-icon-pie-chart,
    .el-icon-s-check {
      font-size: 40px;
    }
  }
}

/* 应用所有4K样式 */
@mixin apply-all-4k-styles {
  @include heading-4k;
  @include text-4k;
  @include button-4k;
  @include card-4k;
  @include element-ui-4k;
  @include spacing-4k;
  @include icon-4k;
} 