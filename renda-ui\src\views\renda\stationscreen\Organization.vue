<template>
  <div class="organization">
    <app-header :showBackBtn="true" />

    <main class="content">
      <div class="background-elements">
        <div class="circle circle-1"></div>
        <div class="circle circle-2"></div>
        <div class="shape shape-1"></div>
      </div>

      <div class="page-title">
        <h1>组织机构</h1>
        <div class="title-decoration"></div>
        <p>{{ stationName ? stationName + '组织架构' : '联络站组织架构' }}</p>
      </div>

      <!-- 站长区域 -->
      <div class="section director-section" v-if="directors && directors.length > 0">
        <div class="section-title">
          <h2>站长</h2>
          <div class="title-line"></div>
        </div>
        <div class="director-container">
          <div class="person-card director-card" v-for="director in directors" :key="director.id" @click="goToDetail(director.id)">
            <div class="photo-container">
              <div class="photo-frame">
                <img :src="getImageUrl(director.avatar)" alt="站长照片" class="person-photo" />
                <div class="photo-overlay"></div>
              </div>
              <div class="qr-code">
                <img :src="getImageUrl(director.qrcodeUrl, require('@/assets/deputy/qrcode.png'))" alt="个人二维码" />
              </div>
            </div>
            <div class="person-info">
              <h3 class="person-name">{{ director.name }}</h3>
              <p class="person-unit">{{ director.company || director.deptName }}</p>
              <p class="person-position">{{ director.duty || '站长' }}</p>
            </div>
            <div class="card-decoration"></div>
          </div>
        </div>
      </div>

      <!-- 副站长区域 -->
      <div class="section deputy-section" v-if="deputies && deputies.length > 0">
        <div class="section-title">
          <h2>副站长</h2>
          <div class="title-line"></div>
        </div>
        <div class="deputy-container">
          <div class="person-card deputy-card" v-for="deputy in deputies" :key="deputy.id" @click="goToDetail(deputy.id)">
            <div class="photo-container">
              <div class="photo-frame">
                <img :src="getImageUrl(deputy.avatar)" alt="副站长照片" class="person-photo" />
                <div class="photo-overlay"></div>
              </div>
              <div class="qr-code">
                <img :src="getImageUrl(deputy.qrcodeUrl, require('@/assets/deputy/qrcode.png'))" alt="个人二维码" />
              </div>
            </div>
            <div class="person-info">
              <h3 class="person-name">{{ deputy.name }}</h3>
              <p class="person-unit">{{ deputy.company || deputy.deptName }}</p>
              <p class="person-position">{{ deputy.duty || '副站长' }}</p>
            </div>
            <div class="card-decoration"></div>
          </div>
        </div>
      </div>

      <!-- 联络员区域 -->
      <div class="section liaison-section" v-if="liaisons && liaisons.length > 0">
        <div class="section-title">
          <h2>联络员</h2>
          <div class="title-line"></div>
        </div>
        <div class="liaison-container">
          <div class="person-card liaison-card" v-for="liaison in liaisons" :key="liaison.id" @click="goToDetail(liaison.id)">
            <div class="photo-container">
              <div class="photo-frame">
                <img :src="getImageUrl(liaison.avatar)" alt="联络员照片" class="person-photo" />
                <div class="photo-overlay"></div>
              </div>
              <div class="qr-code">
                <img :src="getImageUrl(liaison.qrcodeUrl, require('@/assets/deputy/qrcode.png'))" alt="个人二维码" />
              </div>
            </div>
            <div class="person-info">
              <h3 class="person-name">{{ liaison.name }}</h3>
              <p class="person-unit">{{ liaison.company || liaison.deptName }}</p>
              <p class="person-position">{{ liaison.duty || '联络员' }}</p>
            </div>
            <div class="card-decoration"></div>
          </div>
        </div>
      </div>

      <!-- 进站代表区域 -->
      <div class="section representative-section" v-if="representatives && representatives.length > 0">
        <div class="section-title">
          <h2>进站代表</h2>
          <div class="title-line"></div>
        </div>

        <!-- 分类筛选器 -->
        <div class="filter-container">
          <div class="filter-buttons">
            <button
              class="filter-btn"
              :class="{ active: currentCategory === 'all' }"
              @click="filterByCategory('all')"
            >
              全部
            </button>
            <button
              class="filter-btn"
              v-for="category in availableCategories"
              :key="category.dictValue"
              :class="{ active: currentCategory === category.dictValue }"
              @click="filterByCategory(category.dictValue)"
            >
              {{ category.dictLabel }}
            </button>
          </div>
        </div>

        <!-- 代表展示网格 -->
        <div class="representative-container">
          <div class="person-card representative-card" v-for="representative in currentPageRepresentatives" :key="representative.id" @click="goToDetail(representative.id)">
            <div class="photo-container">
              <div class="photo-frame">
                <img :src="getImageUrl(representative.avatar)" alt="代表照片" class="person-photo" />
                <div class="photo-overlay"></div>
              </div>
              <div class="qr-code">
                <img :src="getImageUrl(representative.qrcodeUrl, require('@/assets/deputy/qrcode.png'))" alt="个人二维码" />
              </div>
            </div>
            <div class="person-info">
              <h3 class="person-name">{{ representative.name }}</h3>
              <p class="person-unit">{{ representative.company || representative.deptName }}</p>
              <p class="person-position">{{ representative.duty || getRepresentativeCategory(representative.tags) || '进站代表' }}</p>
            </div>
            <div class="card-decoration"></div>
            <div class="category-badge" v-if="getRepresentativeCategory(representative.tags)">
              {{ getRepresentativeCategory(representative.tags) }}
            </div>
          </div>
        </div>

        <!-- 分页控件 -->
        <div class="pagination-container">
          <button
            class="pagination-btn prev-btn"
            :disabled="currentPage === 1"
            @click="changePage(currentPage - 1)"
          >
            <i class="arrow-left"></i>
            上一页
          </button>

          <div class="page-numbers">
            <button
              class="page-number"
              v-for="page in visiblePages"
              :key="page"
              :class="{ active: page === currentPage }"
              @click="changePage(page)"
            >
              {{ page }}
            </button>
          </div>

          <button
            class="pagination-btn next-btn"
            :disabled="currentPage === totalPages"
            @click="changePage(currentPage + 1)"
          >
            下一页
            <i class="arrow-right"></i>
          </button>
        </div>
      </div>
    </main>

    <!-- 右下角返回按钮 -->
    <div class="back-button" @click="goBack">
      <span>返回</span>
    </div>
  </div>
</template>

<script>
import AppHeader from '@/components/StationScreen/AppHeader.vue'
import { getOrganizationData } from '@/api/renda/stationscreen'

export default {
  name: 'Organization',
  components: {
    AppHeader
  },
  data() {
    return {
      stationId: 0, // 联络站ID
      stationName: '', // 联络站名称
      directors: [],
      deputies: [],
      liaisons: [],
      representatives: [],
      currentCategory: 'all',
      currentPage: 1,
      pageSize: 12, // 每页12个（2行 * 6列）
      filteredRepresentatives: [],
      availableCategories: [],
      loading: false,
      stateKey: 'organization_page_state' // 状态保存的key
    }
  },
  computed: {
    // 根据当前分类过滤代表
    currentFilteredRepresentatives() {
      if (this.currentCategory === 'all') {
        return this.representatives
      } else {
        return this.representatives.filter(rep => {
          if (rep.tags && rep.tags.length > 0) {
            return rep.tags.includes(this.currentCategory)
          }
          return false
        })
      }
    },
    // 总页数
    totalPages() {
      return Math.ceil(this.currentFilteredRepresentatives.length / this.pageSize)
    },
    // 当前页显示的代表
    currentPageRepresentatives() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.currentFilteredRepresentatives.slice(start, end)
    },
    // 显示的页码
    visiblePages() {
      const pages = []
      const totalPages = this.totalPages
      const currentPage = this.currentPage

      if (totalPages <= 7) {
        // 总页数少于7页，显示所有页码
        for (let i = 1; i <= totalPages; i++) {
          pages.push(i)
        }
      } else {
        // 总页数大于7页，智能显示页码
        if (currentPage <= 4) {
          // 当前页在前4页
          for (let i = 1; i <= 5; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(totalPages)
        } else if (currentPage >= totalPages - 3) {
          // 当前页在后4页
          pages.push(1)
          pages.push('...')
          for (let i = totalPages - 4; i <= totalPages; i++) {
            pages.push(i)
          }
        } else {
          // 当前页在中间
          pages.push(1)
          pages.push('...')
          for (let i = currentPage - 1; i <= currentPage + 1; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(totalPages)
        }
      }

      return pages
    }
  },
  methods: {
    // 获取组织机构数据
    async getOrganizationDataFromApi() {
      if (!this.stationId) {
        console.warn('联络站ID为空，无法获取数据')
        return
      }

      this.loading = true
      try {
        const response = await getOrganizationData(this.stationId)
        if (response.code === 200) {
          const data = response.data
          this.directors = data.directors || []
          this.deputies = data.viceDirectors || []
          this.liaisons = data.liaisons || []
          this.representatives = data.representatives || []
          this.availableCategories = data.categories || []

          // 设置站点名称
          if (data.station && data.station.stationName) {
            this.stationName = data.station.stationName
          }

          // 处理代表标签数据
          this.representatives.forEach(rep => {
            if (rep.tags && typeof rep.tags === 'string') {
              rep.tags = rep.tags.split(',').map(tag => tag.trim()).filter(tag => tag !== '')
            } else if (!rep.tags) {
              rep.tags = []
            }
          })

        } else {
          this.$message.error(response.msg || '获取组织机构数据失败')
        }
      } catch (error) {
        console.error('获取组织机构数据失败:', error)
        this.$message.error('获取组织机构数据失败')
      } finally {
        this.loading = false
      }
    },

    // 处理键盘事件
    handleKeydown(event) {
      // ESC键返回首页
      if (event.key === 'Escape') {
        this.goToHome()
      }
    },

    goToHome() {
      this.$router.push({ path: "StationScreenHome"});
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 跳转到代表详情页面
    goToDetail(deputyId) {
      if (!deputyId) {
        console.warn('deputyId为空，无法跳转到详情页面')
        return
      }
      
      // 保存当前页面状态
      this.savePageState()
      
      this.$router.push({
        path: 'RepresentativeDetail',
        query: {
          deputyId: deputyId
        }
      })
    },

    // 保存页面状态
    savePageState() {
      const state = {
        currentCategory: this.currentCategory,
        currentPage: this.currentPage,
        scrollTop: this.getScrollTop(),
        timestamp: Date.now()
      }
      
      try {
        sessionStorage.setItem(this.stateKey, JSON.stringify(state))
        console.log('页面状态已保存:', state)
      } catch (error) {
        console.error('保存页面状态失败:', error)
      }
    },

    // 恢复页面状态
    restorePageState() {
      try {
        const savedState = sessionStorage.getItem(this.stateKey)
        if (savedState) {
          const state = JSON.parse(savedState)
          
          // 检查状态是否过期（30分钟）
          const now = Date.now()
          if (now - state.timestamp > 30 * 60 * 1000) {
            console.log('页面状态已过期，清除状态')
            sessionStorage.removeItem(this.stateKey)
            return
          }
          
          // 恢复状态
          this.currentCategory = state.currentCategory || 'all'
          this.currentPage = state.currentPage || 1
          
          // 恢复滚动位置需要在DOM更新后执行
          if (state.scrollTop !== undefined) {
            this.$nextTick(() => {
              setTimeout(() => {
                this.setScrollTop(state.scrollTop)
              }, 100)
            })
          }
          
          console.log('页面状态已恢复:', state)
          
          // 清除已使用的状态
          sessionStorage.removeItem(this.stateKey)
        }
      } catch (error) {
        console.error('恢复页面状态失败:', error)
      }
    },

    // 获取滚动位置
    getScrollTop() {
      const contentElement = document.querySelector('.organization .content')
      return contentElement ? contentElement.scrollTop : 0
    },

    // 设置滚动位置
    setScrollTop(scrollTop) {
      const contentElement = document.querySelector('.organization .content')
      if (contentElement) {
        contentElement.scrollTop = scrollTop
      }
    },

    // 按分类筛选
    filterByCategory(category) {
      this.currentCategory = category
      this.currentPage = 1 // 重置到第一页
    },
    // 切换页面
    changePage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page
      }
    },
    // 获取代表类别显示文本
    getRepresentativeCategory(tags) {
      if (!tags || tags.length === 0) {
        return null
      }

      // 取第一个标签对应的字典标签
      const firstTag = tags[0]
      const category = this.availableCategories.find(c => c.dictValue === firstTag)
      return category ? category.dictLabel : firstTag
    },

    // 获取代表分类的所有标签文本
    getRepresentativeCategoryLabels(tags) {
      if (!tags || tags.length === 0) {
        return []
      }

      return tags.map(tag => {
        const category = this.availableCategories.find(c => c.dictValue === tag)
        return category ? category.dictLabel : tag
      })
    },

    // 处理图片URL，添加环境前缀
    getImageUrl(url, defaultUrl = null) {
      // 如果URL为空或null，返回默认图片
      if (!url || url === '' || url === null) {
        return defaultUrl || require('@/assets/deputy/someone.jpg')
      }

      // 如果URL已经是完整的HTTP/HTTPS地址，直接返回
      if (url.startsWith('http://') || url.startsWith('https://')) {
        return url
      }

      // 如果URL已经包含环境前缀，直接返回
      if (url.startsWith('/dev-api') || url.startsWith('/prod-api')) {
        return url
      }

      // 添加环境前缀
      return process.env.VUE_APP_BASE_API + url
    }
  },
  async mounted() {
    this.$nextTick(() => {
      // 禁用右键菜单
      document.oncontextmenu = function() {
        return false;
      };
    });

    // 获取联络站Id
    this.stationId = this.$route.query.stationId || 0
    console.log('联络站ID:', this.stationId)

    // 获取组织机构数据
    await this.getOrganizationDataFromApi()

    // 数据加载完成后恢复页面状态
    this.restorePageState()

    document.addEventListener('keydown', this.handleKeydown)
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.handleKeydown)
  }
}
</script>

<style lang="scss" scoped>
.organization {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f8f8f8, #efefef);
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  margin: 0;
  padding: 0;

  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 和 Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.back-button {
  position: fixed;
  bottom: 40px;
  right: 40px;
  width: 80px;
  height: 80px;
  background: rgba(215, 23, 24, 0.9);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(215, 23, 24, 0.3);
  backdrop-filter: blur(10px);
  z-index: 1000;

  &:hover {
    background: rgba(215, 23, 24, 1);
    transform: scale(1.1);
    box-shadow: 0 8px 30px rgba(215, 23, 24, 0.5);
  }

  .back-icon {
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 8px 12px 8px 0;
    border-color: transparent #fff transparent transparent;
    margin-bottom: 4px;
  }

  span {
    color: #fff;
    font-size: 12px;
    font-weight: 500;
    font-family: 'AL-R';
  }
}

.content {
  flex: 1;
  padding: 80px 100px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;

  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 和 Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  .background-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 0;
    pointer-events: none;

    .circle {
      position: absolute;
      border-radius: 50%;
      opacity: 0.04;

      &.circle-1 {
        width: 800px;
        height: 800px;
        background: #d71718;
        top: -300px;
        right: -200px;
      }

      &.circle-2 {
        width: 600px;
        height: 600px;
        background: #d71718;
        bottom: -200px;
        left: -150px;
      }
    }

    .shape {
      position: absolute;
      opacity: 0.03;
      background: #d71718;

      &.shape-1 {
        width: 400px;
        height: 400px;
        transform: rotate(45deg);
        top: 40%;
        left: -200px;
      }
    }
  }

  .page-title {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
    z-index: 1;

    h1 {
      font-size: 64px;
      font-weight: 500;
      color: #d71718;
      margin: 0 0 20px;
      font-family: 'AL-BL';
      text-shadow: 0 2px 10px rgba(215, 23, 24, 0.1);
    }

    .title-decoration {
      width: 120px;
      height: 4px;
      background: linear-gradient(90deg, #d71718, #ff4444);
      margin: 0 auto 25px;
      border-radius: 2px;
    }

    p {
      font-size: 22px;
      color: #666;
      margin: 0;
      font-family: 'AL-L';
    }
  }

  .section {
    margin-bottom: 150px;
    position: relative;
    z-index: 1;

    .section-title {
      text-align: center;
      margin-bottom: 50px;

      h2 {
        font-size: 42px;
        font-weight: 500;
        color: #333;
        margin: 0 0 15px;
        font-family: 'AL-BL';
      }

      .title-line {
        width: 80px;
        height: 3px;
        background: linear-gradient(90deg, #d71718, #ff6666);
        margin: 0 auto;
        border-radius: 2px;
      }
    }
  }

  .director-container {
    display: flex;
    justify-content: center;

    .director-card {
      transform: scale(1.1);
      background: linear-gradient(135deg, rgba(215, 23, 24, 0.95), rgba(180, 22, 22, 0.95));
      color: #fff;

      .person-info {
        .person-name {
          font-family: 'AL-BL';
          font-weight: 500;
          color: #fff;
          font-size: 26px;
        }

        .person-unit, .person-position {
          font-family: 'AL-L';
          color: rgba(255, 255, 255, 0.9);
        }
      }

      .card-decoration {
        background: rgba(255, 255, 255, 0.2);
      }

      .photo-overlay {
        background: rgba(215, 23, 24, 0.1);
      }
    }
  }

  .deputy-container {
    display: flex;
    justify-content: center;
    gap: 120px;
    max-width: 2400px;
    margin: 0 auto;
  }

  .liaison-container {
    display: flex;
    justify-content: center;
    gap: 100px;
    max-width: 3000px;
    margin: 0 auto;
    flex-wrap: wrap;
  }

  .person-card {
    background: rgba(255, 255, 255, 0.98);
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    cursor: pointer;

    &:hover {
      transform: translateY(-10px) scale(1.02);
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    &:active {
      transform: translateY(-8px) scale(1.01);
    }

    .photo-container {
      position: relative;
      overflow: hidden;

      .photo-frame {
        position: relative;
        overflow: hidden;

        .person-photo {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.4s;
        }

        .photo-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.1);
          opacity: 0;
          transition: opacity 0.3s;
        }
      }

      .qr-code {
        position: absolute;
        bottom: 8px;
        right: 8px;
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 8px;
        padding: 6px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        z-index: 10;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }

        &:hover {
          transform: scale(1.1);
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.25);
        }
      }

      &:hover .photo-overlay {
        opacity: 1;
      }

      &:hover .person-photo {
        transform: scale(1.05);
      }
    }

    .person-info {
      padding: 25px 20px;
      text-align: center;

      .person-name {
        font-size: 22px;
        font-weight: 500;
        color: #333;
        margin: 0 0 12px;
        font-family: 'AL-BL';
      }

      .person-unit {
        font-size: 16px;
        color: #666;
        margin: 0 0 8px;
        line-height: 1.4;
        font-family: 'AL-L';
      }

      .person-position {
        font-size: 18px;
        color: #d71718;
        margin: 0;
        font-weight: 600;
        font-family: 'AL-L';
      }
    }

    .card-decoration {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: linear-gradient(90deg, #d71718, #ff4444);
      transform: scaleX(0);
      transform-origin: left;
      transition: transform 0.3s;
    }

    &:hover .card-decoration {
      transform: scaleX(1);
    }
  }

  // 站长卡片样式
  .director-card {
    width: 320px;

    .photo-container {
      height: 400px; // 3:4 比例基础
    }
  }

  // 副站长卡片样式
  .deputy-card {
    width: 280px;

    .photo-container {
      height: 350px; // 3:4 比例基础
    }
  }

  // 联络员卡片样式
  .liaison-card {
    width: 240px;

    .photo-container {
      height: 300px; // 3:4 比例基础
    }
  }

  // 进站代表区域样式
  .representative-section {
    .filter-container {
      margin-bottom: 40px;

      .filter-buttons {
        display: flex;
        justify-content: center;
        gap: 20px;
        flex-wrap: wrap;

        .filter-btn {
          padding: 12px 24px;
          background: rgba(255, 255, 255, 0.9);
          border: 2px solid rgba(215, 23, 24, 0.2);
          border-radius: 30px;
          color: #666;
          font-size: 16px;
          font-weight: 500;
          font-family: 'AL-R';
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background: rgba(215, 23, 24, 0.1);
            border-color: rgba(215, 23, 24, 0.4);
            color: #d71718;
            transform: translateY(-2px);
          }

          &.active {
            background: #d71718;
            border-color: #d71718;
            color: #fff;
            box-shadow: 0 4px 15px rgba(215, 23, 24, 0.3);
          }
        }
      }
    }

    .representative-container {
      display: flex;
      justify-content: center;
      gap: 100px;
      max-width: 3000px;
      margin: 0 auto;
      flex-wrap: wrap;

      .representative-card {
        width: 240px; // 与联络员卡片保持一致
        position: relative;

        .photo-container {
          height: 300px; // 与联络员卡片保持一致
        }

        .category-badge {
          position: absolute;
          top: 15px;
          right: 15px;
          background: rgba(215, 23, 24, 0.9);
          color: #fff;
          padding: 6px 12px;
          border-radius: 15px;
          font-size: 12px;
          font-weight: 500;
          font-family: 'AL-R';
          backdrop-filter: blur(10px);
          z-index: 10;
        }
      }
    }

    .pagination-container {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 20px;
      margin-top: 60px;

      .pagination-btn {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(215, 23, 24, 0.2);
        border-radius: 25px;
        color: #666;
        font-size: 16px;
        font-weight: 500;
        font-family: 'AL-R';
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover:not(:disabled) {
          background: rgba(215, 23, 24, 0.1);
          border-color: rgba(215, 23, 24, 0.4);
          color: #d71718;
          transform: translateY(-2px);
        }

        &:disabled {
          opacity: 0.4;
          cursor: not-allowed;
        }

        .arrow-left, .arrow-right {
          width: 0;
          height: 0;
          border-style: solid;
        }

        .arrow-left {
          border-width: 6px 8px 6px 0;
          border-color: transparent currentColor transparent transparent;
        }

        .arrow-right {
          border-width: 6px 0 6px 8px;
          border-color: transparent transparent transparent currentColor;
        }
      }

      .page-numbers {
        display: flex;
        gap: 8px;

        .page-number {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(255, 255, 255, 0.9);
          border: 2px solid rgba(215, 23, 24, 0.2);
          border-radius: 50%;
          color: #666;
          font-size: 16px;
          font-weight: 500;
          font-family: 'AL-R';
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover:not(.active) {
            background: rgba(215, 23, 24, 0.1);
            border-color: rgba(215, 23, 24, 0.4);
            color: #d71718;
            transform: scale(1.1);
          }

          &.active {
            background: #d71718;
            border-color: #d71718;
            color: #fff;
            box-shadow: 0 4px 15px rgba(215, 23, 24, 0.3);
          }
        }
      }
    }
  }
}

// 4K分辨率适配
@media (min-width: 3000px) {
  .organization {
    .back-button {
      width: 120px;
      height: 120px;
      bottom: 60px;
      right: 60px;

      .back-icon {
        border-width: 12px 18px 12px 0;
        margin-bottom: 6px;
      }

      span {
        font-size: 24px;
      }
    }
  }

  .organization .content {
    .page-title {
      h1 {
        font-size: 96px;
      }
      p {
        font-size: 42px;
      }
    }

    .section .section-title h2 {
      font-size: 64px;
    }

    .person-card {
      .person-info {
        padding: 40px 30px;

        .person-name {
          font-size: 36px;
        }

        .person-unit {
          font-size: 24px;
        }

        .person-position {
          font-size: 28px;
        }
      }

      .photo-container .qr-code {
        width: 100px;
        height: 100px;
        bottom: 12px;
        right: 12px;
        padding: 4px;
        border-radius: 12px;
      }
    }

    .director-card {
      width: 480px;

      .photo-container {
        height: 600px;
      }
    }

    .deputy-card {
      width: 420px;

      .photo-container {
        height: 525px;
      }
    }

    .liaison-card {
      width: 360px;

      .photo-container {
        height: 450px;
      }
    }

    // 4K分辨率下的进站代表样式
    .representative-section {
      .filter-container .filter-buttons .filter-btn {
        font-size: 24px;
        padding: 18px 36px;
        border-radius: 40px;
      }

      .representative-container {
        gap: 100px;
        max-width: 3000px;

        .representative-card {
          width: 360px;

          .photo-container {
            height: 450px; // 与联络员卡片在4K下保持一致
          }

          .category-badge {
            font-size: 18px;
            padding: 9px 18px;
            border-radius: 20px;
            top: 20px;
            right: 20px;
          }
        }
      }

      .pagination-container {
        margin-top: 90px;
        gap: 30px;

        .pagination-btn {
          font-size: 24px;
          padding: 18px 30px;
          border-radius: 35px;

          .arrow-left {
            border-width: 9px 12px 9px 0;
          }

          .arrow-right {
            border-width: 9px 0 9px 12px;
          }
        }

        .page-numbers {
          gap: 12px;

          .page-number {
            width: 60px;
            height: 60px;
            font-size: 24px;
          }
        }
      }
    }
  }
}
</style>
