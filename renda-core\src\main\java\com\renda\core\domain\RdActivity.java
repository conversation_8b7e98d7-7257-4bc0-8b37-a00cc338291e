package com.renda.core.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.renda.common.annotation.Excel;
import com.renda.common.core.domain.BaseEntity;

/**
 * 活动对象 rd_activity
 *
 * <AUTHOR>
 * @date 2023-11-13
 */
public class RdActivity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 活动标题 */
    @Excel(name = "活动标题")
    private String title;

    /** 活动时间 */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @Excel(name = "活动时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date activityDate;

    /** 活动地点 */
    @Excel(name = "活动地点")
    private String address;

    /** 发布单位 */
    @Excel(name = "发布单位")
    private String publisher;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String phone;

    /** 活动内容 */
    @Excel(name = "活动内容")
    private String content;

    /** 其它要求 */
    @Excel(name = "其它要求")
    private String other;

    /** 报名模式：0-自由报名；1-指定范围报名 */
    private String registrationMode;

    /** 是否发送短信：0-不发送；1-发送 */
    private String sendSms;

    /** 能否报名：0-不能；1-能 */
    private String enabled;

    /** 状态：0-关闭；1-开启 */
    private String status;

    /** 活动总人数 */
    private Integer total;

    /** 报名人数 */
    private Integer registeredCount;

    /** 请假人数 */
    private Integer leaveCount;

    /** 未报名人数 */
    private Integer notRegisteredCount;

    private List<RdActivityRegistration> registrationList;

    private Long deputyId;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getTitle()
    {
        return title;
    }
    public void setActivityDate(Date activityDate)
    {
        this.activityDate = activityDate;
    }

    public Date getActivityDate()
    {
        return activityDate;
    }
    public void setAddress(String address)
    {
        this.address = address;
    }

    public String getAddress()
    {
        return address;
    }
    public void setPublisher(String publisher)
    {
        this.publisher = publisher;
    }

    public String getPublisher()
    {
        return publisher;
    }
    public void setPhone(String phone)
    {
        this.phone = phone;
    }

    public String getPhone()
    {
        return phone;
    }
    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }
    public void setOther(String other)
    {
        this.other = other;
    }

    public String getOther()
    {
        return other;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("title", getTitle())
            .append("activityDate", getActivityDate())
            .append("address", getAddress())
            .append("publisher", getPublisher())
            .append("phone", getPhone())
            .append("content", getContent())
            .append("other", getOther())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRegistrationMode() {
        return registrationMode;
    }

    public void setRegistrationMode(String registrationMode) {
        this.registrationMode = registrationMode;
    }

    public String getSendSms() {
        return sendSms;
    }

    public void setSendSms(String sendSms) {
        this.sendSms = sendSms;
    }

    public List<RdActivityRegistration> getRegistrationList() {
        return registrationList;
    }

    public void setRegistrationList(List<RdActivityRegistration> registrationList) {
        this.registrationList = registrationList;
    }

    public Integer getRegisteredCount() {
        return registeredCount;
    }

    public void setRegisteredCount(Integer registeredCount) {
        this.registeredCount = registeredCount;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Integer getLeaveCount() {
        return leaveCount;
    }

    public void setLeaveCount(Integer leaveCount) {
        this.leaveCount = leaveCount;
    }

    public Integer getNotRegisteredCount() {
        return notRegisteredCount;
    }

    public void setNotRegisteredCount(Integer notRegisteredCount) {
        this.notRegisteredCount = notRegisteredCount;
    }

    public Long getDeputyId() {
        return deputyId;
    }

    public void setDeputyId(Long deputyId) {
        this.deputyId = deputyId;
    }

    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }
}
