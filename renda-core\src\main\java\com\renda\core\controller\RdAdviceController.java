package com.renda.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.renda.core.domain.vo.RdAdviceWithFeedback;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.renda.common.annotation.Log;
import com.renda.common.core.controller.BaseController;
import com.renda.common.core.domain.AjaxResult;
import com.renda.common.enums.BusinessType;
import com.renda.core.domain.RdAdvice;
import com.renda.core.service.IRdAdviceService;
import com.renda.common.utils.poi.ExcelUtil;
import com.renda.common.core.page.TableDataInfo;

/**
 * 群众建议Controller
 *
 * <AUTHOR>
 * @date 2023-10-30
 */
@RestController
@RequestMapping("/renda/advice")
public class RdAdviceController extends BaseController
{
    @Autowired
    private IRdAdviceService rdAdviceService;

    /**
     * 查询群众建议列表
     */
    @PreAuthorize("@ss.hasPermi('renda:advice:list')")
    @GetMapping("/list")
    public TableDataInfo list(RdAdvice rdAdvice)
    {
        startPage();
        List<RdAdvice> list = rdAdviceService.selectRdAdviceList(rdAdvice);
        return getDataTable(list);
    }

    /**
     * 导出群众建议列表
     */
    @PreAuthorize("@ss.hasPermi('renda:advice:export')")
    @Log(title = "群众建议", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RdAdvice rdAdvice)
    {
        List<RdAdvice> list = rdAdviceService.selectRdAdviceList(rdAdvice);
        ExcelUtil<RdAdvice> util = new ExcelUtil<RdAdvice>(RdAdvice.class);
        util.exportExcel(response, list, "群众建议数据");
    }

    /**
     * 获取群众建议详细信息
     */
    @PreAuthorize("@ss.hasPermi('renda:advice:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rdAdviceService.selectRdAdviceById(id));
    }

    /**
     * 新增群众建议
     */
    @PreAuthorize("@ss.hasPermi('renda:advice:add')")
    @Log(title = "群众建议", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RdAdvice rdAdvice)
    {
        return toAjax(rdAdviceService.insertRdAdvice(rdAdvice));
    }

    /**
     * 修改群众建议
     */
    @PreAuthorize("@ss.hasPermi('renda:advice:edit')")
    @Log(title = "群众建议", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RdAdvice rdAdvice)
    {
        return toAjax(rdAdviceService.updateRdAdvice(rdAdvice));
    }

    /**
     * 删除群众建议
     */
    @PreAuthorize("@ss.hasPermi('renda:advice:remove')")
    @Log(title = "群众建议", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rdAdviceService.deleteRdAdviceByIds(ids));
    }

    /**
     * 查询群众建议列表
     */
    @PreAuthorize("@ss.hasPermi('renda:advice:list')")
    @GetMapping("/listWithFeedback")
    public TableDataInfo listWithFeedback(RdAdvice rdAdvice)
    {
        startPage();
        List<RdAdviceWithFeedback> list = rdAdviceService.selectRdAdviceWithFeedbackList(rdAdvice);
        return getDataTable(list);
    }

    /**
     * 更新建议处理状态
     */
    @PreAuthorize("@ss.hasPermi('renda:advice:edit')")
    @Log(title = "群众建议状态", businessType = BusinessType.UPDATE)
    @PutMapping("/status")
    public AjaxResult updateStatus(@RequestBody RdAdvice rdAdvice)
    {
        return toAjax(rdAdviceService.updateRdAdvice(rdAdvice));
    }

    /**
     * 更新建议服务评分
     */
    @PreAuthorize("@ss.hasPermi('renda:advice:edit')")
    @Log(title = "群众建议评分", businessType = BusinessType.UPDATE)
    @PutMapping("/rating")
    public AjaxResult updateRating(@RequestBody RdAdvice rdAdvice)
    {
        return toAjax(rdAdviceService.updateRdAdvice(rdAdvice));
    }
}
