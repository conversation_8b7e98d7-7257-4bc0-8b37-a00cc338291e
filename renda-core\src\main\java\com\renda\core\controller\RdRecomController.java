package com.renda.core.controller;

import java.io.File;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.renda.common.config.RendaConfig;
import com.renda.common.core.domain.entity.SysUser;
import com.renda.common.utils.file.FileUploadUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.renda.common.annotation.Log;
import com.renda.common.core.controller.BaseController;
import com.renda.common.core.domain.AjaxResult;
import com.renda.common.enums.BusinessType;
import com.renda.core.domain.RdRecom;
import com.renda.core.service.IRdRecomService;
import com.renda.common.utils.poi.ExcelUtil;
import com.renda.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 代表建议Controller
 *
 * <AUTHOR>
 * @date 2023-11-23
 */
@RestController
@RequestMapping("/renda/recom")
public class RdRecomController extends BaseController
{
    @Autowired
    private IRdRecomService rdRecomService;

    /**
     * 查询代表建议列表
     */
    @PreAuthorize("@ss.hasPermi('renda:recom:list')")
    @GetMapping("/list")
    public TableDataInfo list(RdRecom rdRecom)
    {
        startPage();
        List<RdRecom> list = rdRecomService.selectRdRecomList(rdRecom);
        return getDataTable(list);
    }

    /**
     * 导出代表建议列表
     */
    @PreAuthorize("@ss.hasPermi('renda:recom:export')")
    @Log(title = "代表建议", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RdRecom rdRecom)
    {
        List<RdRecom> list = rdRecomService.selectRdRecomList(rdRecom);
        ExcelUtil<RdRecom> util = new ExcelUtil<RdRecom>(RdRecom.class);
        util.exportExcel(response, list, "代表建议数据");
    }

    /**
     * 获取代表建议详细信息
     */
    @PreAuthorize("@ss.hasPermi('renda:recom:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rdRecomService.selectRdRecomById(id));
    }

    /**
     * 新增代表建议
     */
    @PreAuthorize("@ss.hasPermi('renda:recom:add')")
    @Log(title = "代表建议", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RdRecom rdRecom)
    {
        return toAjax(rdRecomService.insertRdRecom(rdRecom));
    }

    /**
     * 修改代表建议
     */
    @PreAuthorize("@ss.hasPermi('renda:recom:edit')")
    @Log(title = "代表建议", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RdRecom rdRecom)
    {
        return toAjax(rdRecomService.updateRdRecom(rdRecom));
    }

    /**
     * 删除代表建议
     */
    @PreAuthorize("@ss.hasPermi('renda:recom:remove')")
    @Log(title = "代表建议", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rdRecomService.deleteRdRecomByIds(ids));
    }

    @PreAuthorize("@ss.hasPermi('renda:recom:add')")
    @PostMapping("/uploadFile")
    public AjaxResult uploadFile(MultipartFile file) throws Exception
    {
        // 保存文件
        String filename = FileUploadUtils.upload(RendaConfig.getUploadPath(), file);
        return AjaxResult.success(filename);
    }

    @PreAuthorize("@ss.hasPermi('renda:recom:add')")
    @PostMapping("/deleteUploadedFile")
    public AjaxResult deleteUploadedFile(@RequestBody String filename) throws Exception
    {
        // 处理文件名
        filename = filename.substring(8);
        filename = RendaConfig.getProfile() + filename;

        // 删除filename文件
        File file = new File(filename);
        if(file.exists()) {
            // 如果存在，则删除文件
            file.delete();
        }

        return AjaxResult.success();
    }

    /***
     * 查询主办协办列表
     * @param keyword 关键字
     * @return 主办协办列表
     */
    @PreAuthorize("@ss.hasPermi('renda:recom:list')")
    @GetMapping("/listHost")
    public TableDataInfo listHost(String keyword)
    {
        List<String> list = rdRecomService.listHost(keyword);
        return getDataTable(list);
    }

}
