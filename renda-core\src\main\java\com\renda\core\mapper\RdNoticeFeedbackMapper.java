package com.renda.core.mapper;

import java.util.List;
import com.renda.core.domain.RdNoticeFeedback;
import com.renda.system.domain.SysNotice;

/**
 * 工作通知反馈Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
public interface RdNoticeFeedbackMapper
{
    /**
     * 查询工作通知反馈
     *
     * @param id 工作通知反馈主键
     * @return 工作通知反馈
     */
    public RdNoticeFeedback selectRdNoticeFeedbackById(Long id);

    /**
     * 查询工作通知反馈列表
     *
     * @param rdNoticeFeedback 工作通知反馈
     * @return 工作通知反馈集合
     */
    public List<RdNoticeFeedback> selectRdNoticeFeedbackList(RdNoticeFeedback rdNoticeFeedback);

    /**
     * 新增工作通知反馈
     *
     * @param rdNoticeFeedback 工作通知反馈
     * @return 结果
     */
    public int insertRdNoticeFeedback(RdNoticeFeedback rdNoticeFeedback);

    /**
     * 修改工作通知反馈
     *
     * @param rdNoticeFeedback 工作通知反馈
     * @return 结果
     */
    public int updateRdNoticeFeedback(RdNoticeFeedback rdNoticeFeedback);

    /**
     * 删除工作通知反馈
     *
     * @param id 工作通知反馈主键
     * @return 结果
     */
    public int deleteRdNoticeFeedbackById(Long id);

    /**
     * 批量删除工作通知反馈
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRdNoticeFeedbackByIds(Long[] ids);

    /***
     * 获取工作通知反馈接口
     * @param notice 工作通知信息
     * @return 反馈信息
     */
    List<RdNoticeFeedback> getNoticeFeedback(SysNotice notice);
}
