<template>
  <div class="app-container bg">
    <ScreenHeader ref="headerComp" />
    <div class="my-screen">
      <ScreenTitle caption="民意征集" />
      <div class="job-container">
        <div class="job-table">
          <div class="column">
            <el-table
              :data="adviceList1"
              v-el-table-infinite-scroll="loadAdviceList"
              infinite-scroll-distance="5"
              :show-header="false"
              height="492px;"
              @row-click="onAdvice"
            >
              <el-table-column prop="title" label="标题" align="center">
                <template slot-scope="scope">
                  <div class="advice-item">
                    <div class="advice-header">
                      <div class="left">
                        <el-image class="img" :src="scope.row.massAvatar ? baseURL + scope.row.massAvatar : '@/assets/images/avatar.png'" fit="cover"></el-image>
                        <div class="name">{{scope.row.name ? scope.row.name : '匿名'}}</div>
                        <div class="advice-date">（{{formatReadableDate(scope.row.createTime)}}）</div>
                      </div>
                      <div class="deputy-avatar">
                        <div class="name">{{scope.row.deputyName}}</div>
                        <el-image class="img" :src="baseURL + scope.row.deputyAvatar" fit="cover"></el-image>
                      </div>
                    </div>
                    <div class="advice-content"><B>建议：</B>{{scope.row.content}}</div>
                    <div v-if="scope.row.feedbackList && scope.row.feedbackList.length > 0">
                      <div class="seperator-dashed"></div>
                      <div class="advice-feedback"><B>答复：</B>{{scope.row.feedbackList[0].content}}</div>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <el-alert v-if="isLoadingAdvice" title="加载中..." type="success" center :closable="false" show-icon></el-alert>
            <el-alert v-if="noMoreAdvice" title="没有更多了" type="warning" center show-icon></el-alert>
          </div>
          <div class="column">
            <el-table
              :data="adviceList2"
              v-el-table-infinite-scroll="loadAdviceList"
              infinite-scroll-distance="5"
              :show-header="false"
              height="492px;"
              @row-click="onAdvice"
            >
              <el-table-column prop="title" label="标题" align="center">
                <template slot-scope="scope">
                  <div class="advice-item">
                    <div class="advice-header">
                      <div class="left">
                        <el-image class="img" :src="scope.row.massAvatar ? baseURL + scope.row.massAvatar : '@/assets/images/avatar.png'" fit="cover"></el-image>
                        <div class="name">{{scope.row.name ? scope.row.name : '匿名'}}</div>
                        <div class="advice-date">（{{formatReadableDate(scope.row.createTime)}}）</div>
                      </div>
                      <div class="deputy-avatar">
                        <div class="name">{{scope.row.deputyName}}</div>
                        <el-image class="img" :src="baseURL + scope.row.deputyAvatar" fit="cover"></el-image>
                      </div>
                    </div>
                    <div class="advice-content"><B>建议：</B>{{scope.row.content}}</div>
                    <div v-if="scope.row.feedbackList && scope.row.feedbackList.length > 0">
                      <div class="seperator-dashed"></div>
                      <div class="advice-feedback"><B>答复：</B>{{scope.row.feedbackList[0].content}}</div>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <el-alert v-if="isLoadingAdvice" title="加载中..." type="success" center :closable="false" show-icon></el-alert>
            <el-alert v-if="noMoreAdvice" title="没有更多了" type="warning" center show-icon></el-alert>
          </div>
        </div>
      </div>
    </div>
    <el-image class="return-btn" :src="require('@/assets/images/screen/return.png')" @click="onBack"></el-image>
  </div>
</template>

<script>

import ScreenHeader from './components/screenHeader';
import ScreenTitle from './components/screenTitle';
import ElTableInfiniteScroll from 'el-table-infinite-scroll';
import { formatReadableDate } from '@/api/renda/utils';
import { listAdviceWithFeedback } from '@/api/renda/screen';

export default {
  name: "RecomList",
  components: { ScreenHeader, ScreenTitle },
  dicts: ['yw_mz', 'rd_recom_type', 'rd_session', 'rd_times', 'job_type'],
  directives: {
    "el-table-infinite-scroll": ElTableInfiniteScroll,
  },
  data() {
    return {
      defaultAvatar: require("@/assets/images/avatar.png"), // 默认头像
      baseURL: process.env.VUE_APP_BASE_API, // 图片上传地址头
      adviceList1: [], // 履职信息列表
      adviceList2: [], // 履职信息列表
      advicePage: 0, // 履职信息页码
      pageSize: 30, // 履职信息每页条数
      lastTimeLoadAdvice: new Date().getTime() - 2000, // 上次加载时间
      isLoadingAdvice: false,
      noMoreAdvice: false,
    };
  },
  activated() {
    this.adviceList1 = [];
    this.adviceList2 = [];
    this.advicePage = 0;
    this.loadAdviceList();
  },
  mounted() {
    this.$nextTick(() => {
      document.oncontextmenu = function() {
        return false;
      };
    });
  },
  methods: {
    loadAdviceList() {
      let nowTime = new Date().getTime()
      let diffTime = ( nowTime - this.lastTimeLoadAdvice ) / 1000
      if (diffTime > 0.3) {
        this.isLoadingAdvice = true;
        this.advicePage++;
        listAdviceWithFeedback({
          pageNum: this.advicePage,
          pageSize: this.pageSize,
        }).then(res => {
          this.isLoadingAdvice = false;
          if (this.advicePage > Math.floor(res.total / this.pageSize) + 1 ) {
            this.advicePage = Math.floor(res.total / this.pageSize) + 1;
            this.noMoreAdvice = true;
            setTimeout(()=>{
              this.noMoreAdvice = false
            },1000)
          } else {
            let count = Math.floor(res.rows.length / 2);
            if (res.rows.length % 2 === 1) {
              count++;
            }
            this.adviceList1 = this.adviceList1.concat(res.rows.slice(0, count));
            this.adviceList2 = this.adviceList2.concat(res.rows.slice(count));
          }
        });
      }
      this.lastTimeLoadAdvice = nowTime
    },
    formatReadableDate(date) {
      return formatReadableDate(date);
    },
    onBack() {
      this.$refs.headerComp.onClick();
    },
    onAdvice(row) {
      this.$router.push({ path: "AdviceDetail", query: { adviceId: row.id }});
    },
  }
};
</script>

<style lang="scss" scoped>

@import '../../../common/font/font.css';

.my-screen {
  margin: 250px 550px 100px 550px;
  padding: 50px 50px;
  height: 1800px;
  display: flex;
  flex-direction: column;

  .job-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 1590px;
    background: #FFF6EF;
    margin-top: 20px;
    padding: 50px;
    overflow: hidden;

    .job-table {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      .column {
        display: flex;
        flex-direction: column;
        width: 48%;
        padding: 30px;
        height: 1452px;

        .advice-item {
          margin: 24px 0;
          padding: 0 30px;
          display: flex;
          flex-direction: column;
          .advice-header {
            height: 64px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            .left {
              height: 64px;
              display: flex;
              flex-direction: row;
              align-items: center;
              .img {
                width: 64px;
                height: 64px;
                border-radius: 32px;
                border: #fff solid 2px;
                box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.5);
                display: flex;
                flex-shrink: 0;
              }
              .name {
                margin-left: 20px;
                font-size: 30px;
                font-family: AL-B;
                font-weight: 300;
                color: #2F2F2F;
              }
              .advice-date {
                //margin: 10px 0;
                display: flex;
                justify-content: right;
                text-align: right;
                align-items: center;
                font-size: 24px;
                font-family: AL-R;
                font-weight: 300;
                color: #999694;
              }
            }
            .deputy-avatar {
              height: 64px;
              display: flex;
              flex-direction: row;
              text-align: right;
              align-items: center;
              font-size: 24px;
              font-family: AL-R;
              font-weight: 300;
              color: #999694;
              .img {
                width: 64px;
                height: 64px;
                border-radius: 32px;
                border: #fff solid 2px;
                box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.5);
                display: flex;
                flex-shrink: 0;
              }
              .name {
                margin: 20px;
                font-size: 30px;
                font-family: AL-B;
                font-weight: 300;
                color: #2F2F2F;
              }
            }
          }
          .advice-content {
            margin: 12px 0;
            font-family: AL-B;
            font-size: 24px;
            line-height: 30px;
            text-align: left;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 2;
            display: -webkit-box;
            -webkit-box-orient: vertical;
          }
          .advice-feedback {
            margin: 12px 0;
            font-family: AL-R;
            font-size: 24px;
            line-height: 30px;
            text-align: left;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 3;
            display: -webkit-box;
            -webkit-box-orient: vertical;
          }
        }
      }
    }
  }
}

</style>
