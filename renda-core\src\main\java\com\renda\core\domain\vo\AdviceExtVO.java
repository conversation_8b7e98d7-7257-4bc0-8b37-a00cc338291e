package com.renda.core.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AdviceExtVO {

    /** 建议ID */
    private  Long AdviceId;

    /** 人大代表ID */
    private Long deputyId;

    /** 标题 */
    private String title;

    /** 内容 */
    private String content;

    /** 建议人 */
    private String name;

    /** 联系电话 */
    private String phone;

    /** 建议时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    /** 建议类别 */
    private String category;

    /** 建议类别名称 */
    private String categoryName;

    /** 处理状态 (0-待处理, 1-处理中, 2-已处理) */
    private String status;

    /** 服务评分 (1-5分) */
    private Integer serviceRating;

    /** 群众ID */
    private Long massId;

    /** 人大代表头像 */
    private String deputyAvatar;

    /** 建议人头像 */
    private String massAvatar;

    /** 人大代表回复数量 */
    private Long deputyFeedbackCount;

}
