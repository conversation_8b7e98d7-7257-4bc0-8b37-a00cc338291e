package com.renda.core.mapper;

import java.util.List;
import com.renda.core.domain.RdAdviceAttachment;

/**
 * 群众建议附件Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-03
 */
public interface RdAdviceAttachmentMapper 
{
    /**
     * 查询群众建议附件
     * 
     * @param id 群众建议附件主键
     * @return 群众建议附件
     */
    public RdAdviceAttachment selectRdAdviceAttachmentById(Long id);

    /**
     * 查询群众建议附件列表
     * 
     * @param rdAdviceAttachment 群众建议附件
     * @return 群众建议附件集合
     */
    public List<RdAdviceAttachment> selectRdAdviceAttachmentList(RdAdviceAttachment rdAdviceAttachment);

    /**
     * 新增群众建议附件
     * 
     * @param rdAdviceAttachment 群众建议附件
     * @return 结果
     */
    public int insertRdAdviceAttachment(RdAdviceAttachment rdAdviceAttachment);

    /**
     * 修改群众建议附件
     * 
     * @param rdAdviceAttachment 群众建议附件
     * @return 结果
     */
    public int updateRdAdviceAttachment(RdAdviceAttachment rdAdviceAttachment);

    /**
     * 删除群众建议附件
     * 
     * @param id 群众建议附件主键
     * @return 结果
     */
    public int deleteRdAdviceAttachmentById(Long id);

    /**
     * 批量删除群众建议附件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRdAdviceAttachmentByIds(Long[] ids);
}
