package com.renda.core.service;

import java.util.List;
import com.renda.core.domain.RdAdvice;
import com.renda.core.domain.vo.AdviceExtVO;
import com.renda.core.domain.vo.AdviceInfoVO;
import com.renda.core.domain.vo.RdAdviceWithFeedback;
import com.renda.core.domain.vo.TodoInfoVO;

/**
 * 群众建议Service接口
 *
 * <AUTHOR>
 * @date 2023-10-30
 */
public interface IRdAdviceService
{
    /**
     * 查询群众建议
     *
     * @param id 群众建议主键
     * @return 群众建议
     */
    public RdAdvice selectRdAdviceById(Long id);

    /**
     * 查询群众建议列表
     *
     * @param rdAdvice 群众建议
     * @return 群众建议集合
     */
    public List<RdAdvice> selectRdAdviceList(RdAdvice rdAdvice);

    /**
     * 新增群众建议
     *
     * @param rdAdvice 群众建议
     * @return 结果
     */
    public int insertRdAdvice(RdAdvice rdAdvice);

    /**
     * 修改群众建议
     *
     * @param rdAdvice 群众建议
     * @return 结果
     */
    public int updateRdAdvice(RdAdvice rdAdvice);

    /**
     * 批量删除群众建议
     *
     * @param ids 需要删除的群众建议主键集合
     * @return 结果
     */
    public int deleteRdAdviceByIds(Long[] ids);

    /**
     * 删除群众建议信息
     *
     * @param id 群众建议主键
     * @return 结果
     */
    public int deleteRdAdviceById(Long id);

    /***
     * 提交意见建议
     * @param adviceInfoVO 意见建议
     * @return
     */
    void saveAdvice(AdviceInfoVO adviceInfoVO);

    /***
     * 获取建议详情
     * @param adviceId 建议ID
     * @return
     */
    AdviceInfoVO selectAdviceInfoById(Long adviceId);

    /***
     * 获取意见建议接口-所有
     * @param advice 意见建议信息
     * @return 意见建议列表
     */
    List<AdviceExtVO> selectAdviceExtList(RdAdvice advice);

    /**
     * 查询群众建议列表
     */
    List<RdAdviceWithFeedback> selectRdAdviceWithFeedbackList(RdAdvice rdAdvice);

    /***
     * 获取待办信息接口
     * @return 待办信息
     */
    TodoInfoVO getTodoInfo(Long deputyId);

    /***
     * 建议办结接口
     * @return 建议办结结果
     */
    void completeAdvice(RdAdvice advice);
}
