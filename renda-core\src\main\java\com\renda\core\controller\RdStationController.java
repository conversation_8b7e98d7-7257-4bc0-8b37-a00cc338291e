package com.renda.core.controller;

import com.renda.common.annotation.Log;
import com.renda.common.core.controller.BaseController;
import com.renda.common.core.domain.AjaxResult;
import com.renda.common.core.domain.entity.SysDept;
import com.renda.common.enums.BusinessType;
import com.renda.common.utils.StringUtils;
import com.renda.common.core.domain.entity.RdStation;
import com.renda.core.service.IRdStationService;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 站点信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/renda/station")
public class RdStationController extends BaseController
{
    @Autowired
    private IRdStationService stationService;

    /**
     * 获取站点列表
     */
    @PreAuthorize("@ss.hasPermi('renda:station:list')")
    @GetMapping("/list")
    public AjaxResult list(RdStation station)
    {
        List<RdStation> stations = stationService.selectStationList(station);
        return success(stations);
    }

    /**
     * 查询站点列表（排除节点）
     */
    @PreAuthorize("@ss.hasPermi('renda:station:list')")
    @GetMapping("/list/exclude/{stationId}")
    public AjaxResult excludeChild(@PathVariable(value = "stationId", required = false) Long stationId)
    {
        List<RdStation> stations = stationService.selectStationList(new RdStation());
        stations.removeIf(d -> d.getStationId().intValue() == stationId || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), stationId + ""));
        return success(stations);
    }

    /**
     * 根据站点编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('renda:station:query')")
    @GetMapping(value = "/{stationId}")
    public AjaxResult getInfo(@PathVariable Long stationId)
    {
        stationService.checkStationDataScope(stationId);
        return success(stationService.selectStationById(stationId));
    }

    /**
     * 新增站点
     */
    @PreAuthorize("@ss.hasPermi('renda:station:add')")
    @Log(title = "站点管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody RdStation station)
    {
        if (!stationService.checkStationNameUnique(station))
        {
            return error("新增站点'" + station.getStationName() + "'失败，站点名称已存在");
        }
        station.setCreateBy(getUsername());
        return toAjax(stationService.insertStation(station));
    }

    /**
     * 修改站点
     */
    @PreAuthorize("@ss.hasPermi('renda:station:edit')")
    @Log(title = "站点管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody RdStation station)
    {
        Long stationId = station.getStationId();
        stationService.checkStationDataScope(stationId);
        if (!stationService.checkStationNameUnique(station))
        {
            return error("修改站点'" + station.getStationName() + "'失败，站点名称已存在");
        }
        else if (station.getParentId().equals(stationId))
        {
            return error("修改站点'" + station.getStationName() + "'失败，上级站点不能是自己");
        }
        else if (stationService.selectNormalChildrenStationById(stationId) > 0)
        {
            return error("该站点包含未停用的子站点！");
        }
        station.setUpdateBy(getUsername());
        return toAjax(stationService.updateStation(station));
    }

    /**
     * 删除站点
     */
    @PreAuthorize("@ss.hasPermi('renda:station:remove')")
    @Log(title = "站点管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{stationId}")
    public AjaxResult remove(@PathVariable Long stationId)
    {
        if (stationService.hasChildByStationId(stationId))
        {
            return warn("存在下级站点,不允许删除");
        }
        if (stationService.checkStationExistDeputy(stationId))
        {
            return warn("站点存在人大代表,不允许删除");
        }
        stationService.checkStationDataScope(stationId);
        return toAjax(stationService.deleteStationById(stationId));
    }

    /**
     * 获取站点树列表
     */
    @PreAuthorize("@ss.hasPermi('renda:station:list')")
    @GetMapping("/stationTree")
    public AjaxResult stationTree(RdStation station)
    {
        return success(stationService.selectStationTreeList(station));
    }

    /**
     * 获取站点树列表（附各站点人大代表）
     */
    //@PreAuthorize("@ss.hasPermi('renda:station:list')")
    @GetMapping("/stationTreeWithDeputy")
    public AjaxResult stationTreeWithDeputy(RdStation station)
    {
        return success(stationService.selectStationTreeListWithDeputy(station));
    }

    /**
     * 获取代表团树列表（附各代表团人大代表）
     */
    //@PreAuthorize("@ss.hasPermi('renda:station:list')")
    @GetMapping("/groupTreeSelectWithDeputy")
    public AjaxResult groupTreeSelectWithDeputy()
    {
        return success(stationService.groupTreeSelectWithDeputy());
    }

}
