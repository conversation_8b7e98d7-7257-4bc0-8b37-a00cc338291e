<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renda.core.mapper.RdFeedbackAttachmentMapper">

    <resultMap type="RdFeedbackAttachment" id="RdFeedbackAttachmentResult">
        <result property="id"    column="id"    />
        <result property="feedbackId"    column="feedback_id"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileUrl"    column="file_url"    />
    </resultMap>

    <sql id="selectRdFeedbackAttachmentVo">
        select id, feedback_id, file_type, file_name, file_url from rd_feedback_attachment
    </sql>

    <select id="selectRdFeedbackAttachmentList" parameterType="RdFeedbackAttachment" resultMap="RdFeedbackAttachmentResult">
        <include refid="selectRdFeedbackAttachmentVo"/>
        <where>
            <if test="feedbackId != null "> and feedback_id = #{feedbackId}</if>
            <if test="fileType != null "> and file_type = #{fileType}</if>
            <if test="fileName != null  and fileName != ''"> and file_name = #{fileName}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
        </where>
    </select>

    <select id="selectRdFeedbackAttachmentById" parameterType="Long" resultMap="RdFeedbackAttachmentResult">
        <include refid="selectRdFeedbackAttachmentVo"/>
        where id = #{id}
    </select>

    <insert id="insertRdFeedbackAttachment" parameterType="RdFeedbackAttachment" useGeneratedKeys="true" keyProperty="id">
        insert into rd_feedback_attachment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="feedbackId != null">feedback_id,</if>
            <if test="fileType != null">file_type,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="feedbackId != null">#{feedbackId},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
         </trim>
    </insert>

    <update id="updateRdFeedbackAttachment" parameterType="RdFeedbackAttachment">
        update rd_feedback_attachment
        <trim prefix="SET" suffixOverrides=",">
            <if test="feedbackId != null">feedback_id = #{feedbackId},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRdFeedbackAttachmentById" parameterType="Long">
        delete from rd_feedback_attachment where id = #{id}
    </delete>

    <delete id="deleteRdFeedbackAttachmentByIds" parameterType="String">
        delete from rd_feedback_attachment where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
