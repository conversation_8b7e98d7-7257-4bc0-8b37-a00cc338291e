<template>
  <div class="app-container bg">
    <ScreenHeader ref="headerComp" />
    <div class="deputy-screen">
      <div class="phone-container">
        <el-image class="advice-phone" :src="require('@/assets/images/screen/advice-phone.png')"></el-image>
        <div class="conversation">
          <div class="mass">
            <div class="left">
              <el-image class="avatar" :src="advice.massAvatar ? baseURL + advice.massAvatar : defaultAvatar" fit="cover"></el-image>
              <div class="name">{{advice.name ? advice.name : '匿名'}}</div>
            </div>
            <div class="right">
              <div class="time">{{advice.createTime}}</div>
              <div class="words">
                <div class="triangle-left" />
                <div class="question">{{advice.title}}</div>
                <div class="content">{{advice.content}}</div>
              </div>
              <div class="filelist" v-if="advice.attachmentList && advice.attachmentList.length > 0">
                <div v-for="(item, index) in advice.attachmentList">
                  <div class="file" v-if="item.fileType === 2">
                    <el-image class="filetype" :src="item.icon" fit="contain"></el-image>
                    <div class="filename" @click="onPreview(item)">{{item.fileName}}</div>
                  </div>
                </div>
              </div>
              <div class="imgs" v-if="advice.attachmentList && advice.attachmentList.length > 0">
                <div v-for="(item, index) in advice.attachmentList">
                  <el-image class="img" :src="baseURL + item.fileUrl" fit="contain"></el-image>
                </div>
              </div>
            </div>
          </div>
          <div v-for="(item, index) in advice.feedbackList">
            <div class="mass" v-if="item.userType === 1">
              <div class="left">
                <el-image class="avatar" :src="item.avatar ? baseURL + item.avatar : defaultAvatar" fit="cover"></el-image>
                <div class="name">{{advice.name ? advice.name : '匿名'}}</div>
              </div>
              <div class="right">
                <div class="time">{{item.createTime}}</div>
                <div class="words">
                  <div class="triangle-left" />
                  <div class="content">{{item.content}}</div>
                </div>
                <div class="filelist" v-if="item.attachmentList && item.attachmentList.length > 0">
                  <div v-for="(feedbackItem, index) in item.attachmentList">
                    <div class="file" v-if="feedbackItem.fileType === 2">
                      <el-image class="filetype" :src="feedbackItem.icon" fit="contain"></el-image>
                      <div class="filename" @click="onPreview(feedbackItem)">{{feedbackItem.fileName}}</div>
                    </div>
                  </div>
                </div>
                <div class="imgs" v-if="item.attachmentList && item.attachmentList.length > 0">
                  <div v-for="(feedbackItem, index) in item.attachmentList">
                    <el-image class="img" :src="baseURL + feedbackItem.fileUrl" fit="contain"></el-image>
                  </div>
                </div>
              </div>
            </div>
            <div class="deputy" v-if="item.userType === 2">
              <div class="left">
                <el-image class="avatar" :src="baseURL + item.avatar" fit="cover"></el-image>
                <div class="name">{{item.name}}</div>
              </div>
              <div class="right">
                <div class="time">{{item.createTime}}</div>
                <div class="wrap">
                  <div class="words">
                    <div class="triangle-right" />
                    <div class="content">{{item.content}}</div>
                  </div>
                </div>
                <div class="filelist" v-if="item.attachmentList && item.attachmentList.length > 0">
                  <div v-for="(feedbackItem, index) in item.attachmentList">
                    <div class="file" v-if="feedbackItem.fileType === 2">
                      <el-image class="filetype" :src="feedbackItem.icon" fit="contain"></el-image>
                      <div class="filename" @click="onPreview(feedbackItem)">{{feedbackItem.fileName}}</div>
                    </div>
                  </div>
                </div>
                <div class="imgs" v-if="item.attachmentList && item.attachmentList.length > 0">
                  <div v-for="(feedbackItem, index) in item.attachmentList">
                    <el-image class="img" :src="baseURL + feedbackItem.fileUrl" fit="contain"></el-image>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <el-image class="advice-slogen" :src="require('@/assets/images/screen/advice-slogen.png')"></el-image>
    </div>
    <el-image class="return-btn" :src="require('@/assets/images/screen/return.png')" @click="onBack"></el-image>
    <!-- 预览代表建议对话框 -->
    <el-dialog :visible.sync="openPreview" modal width="1240px" height="1640px" top="300px!important">
      <div slot="title" style="font-family: AL-R; font-size: 34px; text-align: center;">{{fileTitle}}</div>
      <iframe
        v-if="previewUrl"
        :src="previewUrl"
        width="1200px"
        height="1500px"
        frameborder="0"
        scrolling="yes" />
    </el-dialog>
  </div>
</template>

<script>

import ScreenHeader from './components/screenHeader';
import { getAdvice } from '@/api/renda/screen';
import { Base64 } from 'js-base64'

export default {
  name: "AdviceDetail",
  components: { ScreenHeader },
  data() {
    return {
      defaultAvatar: require("@/assets/images/avatar.png"), // 默认头像
      baseURL: process.env.VUE_APP_BASE_API, // 图片上传地址头
      adviceId: 0, // 建议id
      advice: {}, // 建议详情
      openPreview: false, // 是否打开预览对话框
      fileTitle: '', // 附件标题
      previewUrl: '', // 预览地址
    };
  },
  activated() {
    this.adviceId = this.$route.query.adviceId;
    if (!this.adviceId) {
      this.$router.go(-1);
    }
    this.loadData()
  },
  mounted() {
    this.$nextTick(() => {
      document.oncontextmenu = function() {
        return false;
      };
    });
  },
  methods: {
    /** 加载数据 */
    loadData() {
      getAdvice(this.adviceId).then(res => {
        this.advice = res.data
        this.advice.attachmentList.forEach(item => {
          if (item.fileType === 2) {
            // 文件
            // 获取文件扩展名
            const ext = item.fileUrl.substring(item.fileUrl.lastIndexOf('.') + 1).toLowerCase();
            if (ext === 'pdf') {
              item.icon = require('@/assets/images/screen/filetype/pdf.png');
            } else if (ext === 'doc' || ext === 'docx') {
              item.icon = require('@/assets/images/screen/filetype/doc.png');
            } else if (ext === 'xls' || ext === 'xlsx') {
              item.icon = require('@/assets/images/screen/filetype/xls.png');
            } else if (ext === 'ppt' || ext === 'pptx') {
              item.icon = require('@/assets/images/screen/filetype/ppt.png');
            } else {
              item.icon = require('@/assets/images/screen/filetype/unknow.png');
            }
          }
        });
        this.advice.feedbackList.forEach(item => {
          item.attachmentList.forEach(feedbackItem => {
            if (feedbackItem.fileType === 2) {
              // 文件
              // 获取文件扩展名
              const ext = feedbackItem.fileUrl.substring(feedbackItem.fileUrl.lastIndexOf('.') + 1).toLowerCase();
              if (ext === 'pdf') {
                feedbackItem.icon = require('@/assets/images/screen/filetype/pdf.png');
              } else if (ext === 'doc' || ext === 'docx') {
                feedbackItem.icon = require('@/assets/images/screen/filetype/doc.png');
              } else if (ext === 'xls' || ext === 'xlsx') {
                feedbackItem.icon = require('@/assets/images/screen/filetype/xls.png');
              } else if (ext === 'ppt' || ext === 'pptx') {
                feedbackItem.icon = require('@/assets/images/screen/filetype/ppt.png');
              } else {
                feedbackItem.icon = require('@/assets/images/screen/filetype/unknow.png');
              }
            }
          });
        });
      })
    },
    onBack() {
      this.$refs.headerComp.onClick();
    },
    onPreview(item) {
      const fileUrl = item.fileUrl;
      if (fileUrl) {
        let file = 'https://rd.juruifeng.cn:9000/prod-api' + fileUrl; //要预览文件的访问地址
        let url = 'https://rd.juruifeng.cn:8888/onlinePreview?url='+encodeURIComponent(Base64.encode(file));
        // let file = 'http://localhost:9000/dev-api' + fileUrl; //要预览文件的访问地址
        // let url = 'http://localhost:8888/onlinePreview?url='+encodeURIComponent(Base64.encode(file));
        this.previewUrl = url;
        this.fileTitle = item.fileName;
        this.openPreview = true;
      }
    },
  }
};
</script>

<style lang="scss" scoped>

@import '../../../common/font/font.css';

.deputy-screen {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-around;
  .phone-container {
    position: relative;
    height: 80%;
    margin: 300px 0 auto 300px;
    .advice-phone {
      height: 100%;
    }
    .conversation {
      position: absolute;
      width: 743px;
      height: 1325px;
      top: 185px;
      left: 28px;
      background: #F5F5F5;
      display: flex;
      flex-direction: column;
      padding: 32px;
      font-family: AL-R;
      font-size: 26px;
      overflow: scroll;
      .mass {
        display: flex;
        flex-direction: row;
        margin: 24px 0;
        .left {
          display: flex;
          flex-direction: column;
          .avatar {
            display: flex;
            flex-shrink: 0;
            margin: 20px 6px 6px 6px;
            width: 100px;
            height: 100px;
            border-radius: 50px;
            border: #fff 1px solid;
            box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.3);
          }
          .name {
            margin: 0 6px;
            width: 100px;
            text-align: center;
            font-family: AL-R;
            font-size: 24px;
          }
        }
        .right {
          display: flex;
          flex-direction: column;
          flex-grow: 1;
          .time {
            margin-left: 16px;
            font-size: 24px;
            color: #717171;
          }
          .words {
            display: flex;
            flex-direction: column;
            margin: 20px 16px;
            padding: 12px 24px;
            background: #fff;
            border-radius: 12px;
            position: relative;
            width: fit-content;
            .triangle-left {
              width: 0;
              height: 0;
              position: absolute;
              top: 16px;
              left: -28px;
              border-top: 16px solid transparent;
              border-left: 16px solid transparent;
              border-right: 16px solid #fff;
              border-bottom: 16px solid transparent;
            }
            .question {
              font-weight: bold;
            }
          }
          .file {
            display: flex;
            flex-direction: row;
            margin: 10px 20px;
            .filetype {
              width: 50px;
              height: 50px;
              flex-shrink: 0;
            }
            .filename {
              margin-left: 20px;
              line-height: 50px;
              font-size: 28px;
              font-family: AL-L;
              color: #504B4A;
              text-decoration: underline;
              cursor: pointer;
            }
          }
          .imgs {
            display: flex;
            flex-direction: column;
            margin: 20px 16px;
            .img {
              width: 100%;
              margin-right: 8px;
            }
          }
        }
      }
      .deputy {
        display: flex;
        flex-direction: row-reverse;
        margin: 24px 0;
        .left {
          display: flex;
          flex-direction: column;
          .avatar {
            display: flex;
            flex-shrink: 0;
            margin: 20px 6px 6px 6px;
            width: 100px;
            height: 100px;
            border-radius: 50px;
            border: #fff 1px solid;
            box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.3);
          }
          .name {
            width: 100px;
            margin: 0 6px;
            text-align: center;
            font-family: AL-R;
            font-size: 24px;
          }
        }
        .right {
          display: flex;
          flex-direction: column;
          flex-grow: 1;
          .time {
            display: flex;
            flex-direction: row-reverse;
            margin-right: 16px;
            font-size: 24px;
            color: #717171;
          }
          .wrap {
            display: flex;
            flex-direction: row;
            justify-content: end;
            .words {
              margin: 20px 16px;
              padding: 12px 24px;
              border-radius: 12px;
              position: relative;
              width: fit-content;
              background-color: #95EC69;
              .triangle-right {
                width: 0;
                height: 0;
                position: absolute;
                top: 16px;
                right: -28px;
                border-top: 16px solid transparent;
                border-left: 16px solid #95EC69;
                border-right: 16px solid transparent;
                border-bottom: 16px solid transparent;
              }
            }
          }
          .file {
            display: flex;
            flex-direction: row;
            line-height: 60px;
            margin-left: 20px;
            .filetype {
              width: 50px;
              height: 50px;
              flex-shrink: 0;
            }
            .filename {
              margin-left: 20px;
              line-height: 50px;
              font-size: 28px;
              font-family: AL-L;
              color: #504B4A;
              text-decoration: underline;
              cursor: pointer;
            }
          }
          .imgs {
            display: flex;
            flex-direction: column;
            margin: 20px 16px;
            .img {
              width: 100%;
              margin-right: 8px;
            }
          }
        }
      }
    }
  }
  .advice-slogen {
    height: 80%;
    margin: auto 100px auto 0;
  }
}

</style>
