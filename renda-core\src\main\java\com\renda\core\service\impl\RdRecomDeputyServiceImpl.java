package com.renda.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.renda.core.mapper.RdRecomDeputyMapper;
import com.renda.core.domain.RdRecomDeputy;
import com.renda.core.service.IRdRecomDeputyService;

/**
 * 建议代Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-23
 */
@Service
public class RdRecomDeputyServiceImpl implements IRdRecomDeputyService
{
    @Autowired
    private RdRecomDeputyMapper rdRecomDeputyMapper;

    /**
     * 查询建议代
     *
     * @param id 建议代主键
     * @return 建议代
     */
    @Override
    public RdRecomDeputy selectRdRecomDeputyById(Long id)
    {
        return rdRecomDeputyMapper.selectRdRecomDeputyById(id);
    }

    /**
     * 查询建议代列表
     *
     * @param rdRecomDeputy 建议代
     * @return 建议代
     */
    @Override
    public List<RdRecomDeputy> selectRdRecomDeputyList(RdRecomDeputy rdRecomDeputy)
    {
        return rdRecomDeputyMapper.selectRdRecomDeputyList(rdRecomDeputy);
    }

    /**
     * 新增建议代
     *
     * @param rdRecomDeputy 建议代
     * @return 结果
     */
    @Override
    public int insertRdRecomDeputy(RdRecomDeputy rdRecomDeputy)
    {
        return rdRecomDeputyMapper.insertRdRecomDeputy(rdRecomDeputy);
    }

    /**
     * 修改建议代
     *
     * @param rdRecomDeputy 建议代
     * @return 结果
     */
    @Override
    public int updateRdRecomDeputy(RdRecomDeputy rdRecomDeputy)
    {
        return rdRecomDeputyMapper.updateRdRecomDeputy(rdRecomDeputy);
    }

    /**
     * 批量删除建议代
     *
     * @param ids 需要删除的建议代主键
     * @return 结果
     */
    @Override
    public int deleteRdRecomDeputyByIds(Long[] ids)
    {
        return rdRecomDeputyMapper.deleteRdRecomDeputyByIds(ids);
    }

    /**
     * 删除建议代信息
     *
     * @param id 建议代主键
     * @return 结果
     */
    @Override
    public int deleteRdRecomDeputyById(Long id)
    {
        return rdRecomDeputyMapper.deleteRdRecomDeputyById(id);
    }

    /**
     * 删除建议代表信息
     *
     * @param recomId 建议主键
     * @return 结果
     */
    @Override
    public int deleteRdRecomDeputyByRecomId(Long recomId) {
        return rdRecomDeputyMapper.deleteRdRecomDeputyByRecomId(recomId);
    }

    @Override
    public int deleteRdRecomDeputyByRecomIds(Long[] ids) {
        return rdRecomDeputyMapper.deleteRdRecomDeputyByRecomIds(ids);
    }

}
