package com.renda.core.domain;

import com.renda.common.annotation.Excel;
import com.renda.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 制度文件对象 rd_file
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
@Data
public class RdFile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 文件名称 */
    @Excel(name = "文件名称")
    private String fileName;

    /** 文件URL */
    private String fileUrl;

    /** 文件大小 */
    @Excel(name = "文件大小")
    private String fileSize;

    /** 顺序 */
    @Excel(name = "顺序")
    private Integer orderNum;

}
