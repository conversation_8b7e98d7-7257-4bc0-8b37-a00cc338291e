<template>
  <div class="app-container bg">
    <ScreenHeader ref="headerComp" />
    <div class="main-screen">
      <ScreenTitle caption="代表动态" />
      <div class="deputy-info-content">
        <div class="top">
          <div class="top-news">
            <el-carousel class="top-carousel" :interval="4000" :show-overflow="true" height="800px" arrow="always">
              <el-carousel-item v-for="(item) in topNews">
                <div @click="onShowNewsDetail(item.noticeId)">
                  <el-image class="img" :src="baseURL + item.coverUrl" fit="cover"></el-image>
                  <div class="caption">{{item.noticeTitle}}</div>
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>
          <el-tabs class="tabs" :value="dict.type.rd_news_category[0].label">
            <el-tab-pane :label="category.label" :name="category.label" v-for="(category) in dict.type.rd_news_category">
              <div class="tab-container">
                <div class="tab-item"
                     v-for="(news) in categoryNews.filter(el => el.noticeCategory === category.value)"
                     @click="onShowNewsDetail(news.noticeId)"
                >
                  <div class="caption">{{news.noticeTitle}}</div>
                  <div class="date">{{formatReadableDate(news.createTime)}}</div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div class="bottom">
          <div class="news-card" v-for="(item) in cardNews" @click="onShowNewsDetail(item.noticeId)">
            <el-image class="card-img" :src="baseURL + item.coverUrl" fit="cover"></el-image>
            <div class="card-caption">{{item.noticeTitle}}</div>
            <div class="card-footer">
              <div class="card-btn">查看详情</div>
              <div class="news-date">{{formatReadableDate(item.createTime)}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-image class="return-btn" :src="require('@/assets/images/screen/return.png')" @click="onBack"></el-image>
  </div>
</template>

<script>

import ScreenHeader from './components/screenHeader';
import ScreenTitle from './components/screenTitle';
import { listNotice } from '@/api/renda/screen';
import { formatReadableDate } from '@/api/renda/utils';

export default {
  name: "News",
  components: { ScreenHeader, ScreenTitle },
  dicts: ['rd_news_category'],
  data() {
    return {
      defaultNewsImage: require("@/assets/images/screen/defaultNewsImage.jpg"), // 默认新闻图片
      baseURL: process.env.VUE_APP_BASE_API, // 图片上传地址头
      topNews: [], // 轮播新闻列表
      categoryNews: [], // 分类新闻列表
      cardNews: [], // 卡片新闻列表
    };
  },
  created() {
    this.loadData();
  },
  mounted() {
    this.$nextTick(() => {
      document.oncontextmenu = function() {
        return false;
      };
    });
  },
  methods: {
    /** 加载轮播新闻 */
    loadData() {
      listNotice({
        pageNum: 1,
        pageSize: 10,
        noticeType: '1',
        status: '1'
      }).then((res) => {
        this.topNews = res.rows;
      });
      /** 加载分类新闻 */
      listNotice({
        pageNum: 1,
        pageSize: 30,
        noticeType: '2',
        status: '1'
      }).then((res) => {
        this.categoryNews = res.rows;
      });
      /** 加载卡片新闻 */
      listNotice({
        pageNum: 1,
        pageSize: 5,
        noticeType: '3',
        status: '1'
      }).then((res) => {
        this.cardNews = res.rows;
      });

    },
    onShowNewsDetail(noticeId) {
      this.$router.push({ path: "NewsDetail", query: { noticeId: noticeId }});
    },
    onBack() {
      this.$refs.headerComp.onClick();
    },
    formatReadableDate(date) {
      return formatReadableDate(date);
    },
  }
};
</script>

<style lang="scss" scoped>

@import '../../../common/font/font.css';

.main-screen {
  margin: 250px 550px 100px 550px;
  padding: 50px 50px;
  height: 1800px;
  display: flex;
  flex-direction: column;

  .deputy-info-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background: #FFF6EF;
    margin-top: 20px;
    padding: 100px;
    overflow: hidden;
    .top {
      display: flex;
      flex-direction: row;
      .top-news {
        width: 1300px;
        height: 800px;
        display: flex;
        flex-direction: column;
        flex-shrink: 0;
        position: relative;
        .top-carousel {
          width: 100%;
          height: 100%;
          .img {
            width: 1300px;
            height: 800px;
          }
          .caption {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 140px;
            position: absolute;
            bottom: 0;
            margin-bottom: 20px;
            padding: 10px 20px;
            background-color: rgba(0, 0, 0, 0.5);
            font-family: AL-B;
            font-size: 44px;
            color: #FFF;
          }
        }
      }
      .tabs {
        margin-left: 40px;
        flex-grow: 1;
        height: 100%;
        background-color: #fff;
        padding: 0 40px;
        .tab-container {
          height: 720px;
          display: flex;
          flex-direction: column;
          overflow-y: auto;
          scrollbar-width: none; /* Firefox */
          -ms-overflow-style: none;  /* Internet Explorer 10+ */
          .tab-item {
            display: flex;
            flex-direction: row;
            margin: 20px 0;
            padding-left: 48px;
            justify-content: space-between;
            border-left: #E61B17 10px solid;
            .caption {
              display: flex;
              align-items: center;
              height: 100px;
              font-family: AL-B;
              font-size: 34px;
              color: #2F2F2F;
            }
            .date {
              display: flex;
              flex-shrink: 0;
              align-items: center;
              margin-left: 50px;
              font-family: AL-L;
              font-size: 30px;
            }
          }
        }
      }
    }
    .bottom {
      margin: 72px 0;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      .news-card {
        display: flex;
        flex-direction: column;
        width: 400px;
        height: 500px;
        border-radius: 24px;
        overflow: hidden;
        background-color: #fff;
        box-shadow: 0 0 20px 10px #fcdcdc;
        .card-img {
          height: 250px;
        }
        .card-caption {
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          height: 150px;
          padding: 20px 30px;
          font-family: AL-B;
          font-size: 28px;
          line-height: 42px;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 3;
          display: -webkit-box;
          -webkit-box-orient: vertical;
        }
        .card-footer {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          padding: 0 25px;
          flex-grow: 1;
          .card-btn {
            margin: 10px;
            width: 130px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 25px;
            background-color: #DE0011;
            font-family: AL-B;
            font-size: 24px;
            color: #fff;
          }
          .news-date {
            font-family: AL-R;
            font-size: 22px;
          }
        }
      }
    }
  }
}

</style>
