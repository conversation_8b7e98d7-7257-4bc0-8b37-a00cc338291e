package com.renda.core.service;

import java.util.List;
import com.renda.core.domain.RdDeputy;
import com.renda.core.domain.vo.AdviceInfoVO;
import com.renda.core.domain.vo.DeputyInfoVO;
import com.renda.core.domain.vo.DeputyStatVO;
import com.renda.core.domain.vo.DeputyStatisticsInfoVO;

/**
 * 人大代表管理Service接口
 *
 * <AUTHOR>
 * @date 2023-10-23
 */
public interface IRdDeputyService
{
    /**
     * 查询人大代表管理
     *
     * @param id 人大代表管理主键
     * @return 人大代表管理
     */
    public RdDeputy selectRdDeputyById(Long id);

    /**
     * 查询人大代表管理列表
     *
     * @param rdDeputy 人大代表管理
     * @return 人大代表管理集合
     */
    public List<RdDeputy> selectRdDeputyList(RdDeputy rdDeputy);

    /**
     * 查询人大代表管理列表
     *
     * @param rdDeputy 人大代表管理
     * @return 人大代表管理集合
     */
    public List<RdDeputy> selectRdDeputyList2(RdDeputy rdDeputy);

    /***
     * 根据部门查询人大代表列表
     * @param id 部门id
     * @return
     */
    public List<RdDeputy> selectRdDeputyListByDeptId(Long id);

    /**
     * 新增人大代表管理
     *
     * @param rdDeputy 人大代表管理
     * @return 结果
     */
    public int insertRdDeputy(RdDeputy rdDeputy);

    /**
     * 修改人大代表管理
     *
     * @param rdDeputy 人大代表管理
     * @return 结果
     */
    public int updateRdDeputy(RdDeputy rdDeputy);

    /**
     * 批量删除人大代表管理
     *
     * @param ids 需要删除的人大代表管理主键集合
     * @return 结果
     */
    public int deleteRdDeputyByIds(Long[] ids);

    /**
     * 删除人大代表管理信息
     *
     * @param id 人大代表管理主键
     * @return 结果
     */
    public int deleteRdDeputyById(Long id);

    /**
     * 查询人大代表管理（小程序端展示）
     *
     * @param id 人大代表管理主键
     * @return 人大代表管理
     */
    public DeputyInfoVO selectRdDeputyInfoById(Long id);

    /***
     * 根据手机号获取人大代表信息
     * @param phone
     * @return 人大代表信息
     */
    RdDeputy selectDeputyByPhone(String phone);

    /***
     * 获取代表统计信息接口
     * @return
     */
    DeputyStatisticsInfoVO getDeputyStatistics();

    /**
     * 获取人大代表履职统计信息
     */
    List<DeputyStatVO> getDeputyStat(Long id);

    /**
     * 批量生成代表二维码
     */
    int genDeputyQrcode(Long[] ids);

}
