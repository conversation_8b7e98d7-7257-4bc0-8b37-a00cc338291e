<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="工作类型" prop="jobType">
        <el-select v-model="queryParams.jobType" placeholder="请选择工作类型" clearable>
          <el-option
            v-for="dict in dict.type.job_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="关键字" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入关键字"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="起始时间" prop="beginDate">
        <el-date-picker clearable
          v-model="queryParams.beginDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择起始时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="截止时间" prop="endDate">
        <el-date-picker clearable
          v-model="queryParams.endDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择截止时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['renda:job:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['renda:job:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="jobList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" align="center" width="55">
        <template slot-scope="scope">
          <span>{{(queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <el-table-column label="代表姓名" align="center" prop="deputyName" width="150" />
      <el-table-column label="履职类型" align="center" prop="jobType" width="140">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.job_type" :value="scope.row.jobType"/>
        </template>
      </el-table-column>
      <el-table-column label="组织单位" align="center" prop="organizer" width="150" />
      <el-table-column label="起始时间" align="center" prop="beginDate" width="100">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.beginDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="截止时间" align="center" prop="endDate" width="100">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="标题" align="left" prop="title" min-width="200" show-overflow-tooltip />
      <el-table-column label="工作内容" align="left" prop="content" min-width="200" show-overflow-tooltip />
      <el-table-column label="提交时间" align="center" prop="createTime" width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['renda:job:list']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['renda:job:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改代表履职对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="工作类型" prop="jobType">
          <el-select v-model="form.jobType" placeholder="请选择工作类型">
            <el-option
              v-for="dict in dict.type.job_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="组织单位" prop="organizer">
          <el-input v-model="form.organizer" placeholder="请输入组织单位" />
        </el-form-item>
        <el-form-item label="起始时间" prop="beginDate">
          <el-date-picker clearable
            v-model="form.beginDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择起始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="截止时间" prop="endDate">
          <el-date-picker clearable
            v-model="form.endDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择截止时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" />
        </el-form-item>
        <el-form-item label="工作内容">
          <editor v-model="form.content" :min-height="192"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 建议详情对话框 -->
    <el-dialog title="代表履职详情" :visible.sync="showJobDetail" width="1000px" append-to-body>
      <el-form ref="form" :model="jobForm" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="标题" prop="title">
              <el-input v-model="jobForm.title" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="时间" prop="createTime">
              <el-date-picker clearable
                              readonly
                              v-model="jobForm.createTime"
                              type="datetime"
                              value-format="yyyy-MM-dd HH:mm:ss"
                              placeholder="请选择活动时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="内容" prop="content">
              <el-input v-model="jobForm.content" type="textarea" :rows="10" readonly />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="jobForm && jobForm.attachments && jobForm.attachments.length > 0">
          <el-col :span="24">
            <el-form-item label="附件" prop="attachments">
              <div v-for="(item, index) of jobForm.attachments" :key="index" >
                <el-link v-if="item.fileType === 2" type="primary" icon="el-icon-document" :href="item.fileUrl" target="_blank">{{item.fileName}}</el-link>
              </div>
              <div class="img-container" >
                <el-image v-for="(item, index) of jobForm.attachments"
                          :key="index"
                          v-if="item.fileType === 1"
                          class="img-item"
                          :src="item.fileUrl"
                          :preview-src-list="getPreviewImgList(index)"></el-image>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showJobDetail = false">关闭</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listJob, getJob, delJob, addJob, updateJob, getJobExt } from "@/api/renda/job";

export default {
  name: "Job",
  dicts: ['job_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 代表履职表格数据
      jobList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 工作内容时间范围
      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jobType: null,
        organizer: null,
        beginDate: null,
        endDate: null,
        title: null,
        content: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      showJobDetail: false, // 显示履职详情对话框
      jobForm: {}, // 履职详情表单
      baseURL: process.env.VUE_APP_BASE_API, // 图片上传地址头
      srcList: [], // 图片预览列表
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询代表履职列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      listJob(this.queryParams).then(response => {
        this.jobList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        deputyId: null,
        jobType: null,
        organizer: null,
        beginDate: null,
        endDate: null,
        title: null,
        content: null,
        createTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加代表履职";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getJob(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改代表履职";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateJob(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addJob(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('确认删除吗？').then(function() {
        return delJob(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('renda/Job/export', {
        ...this.queryParams
      }, `job_${new Date().getTime()}.xlsx`)
    },
    handleDetail(row) {
      getJobExt(row.id).then(response => {
        this.jobForm = response.data;
        this.srcList = [];
        this.jobForm.attachments.forEach(item => {
          item.fileUrl = this.baseURL + item.fileUrl
          if (item.fileType === 1) {
            this.srcList.push(item.fileUrl)
          }
        });
        this.showJobDetail = true;
      });
    },
    // 大图预览，实现点击当前图片显示当前图片大图，可以随机切换到其他图片进行展示
    getPreviewImgList:function(index) {
      let arr = []
      let i = 0;
      for(i;i < this.srcList.length;i++){
        arr.push(this.srcList[i+index])
        if(i+index >= this.srcList.length-1){
          index = 0-(i+1);
        }
      }
      return arr;
    },
  }
};
</script>

<style lang="scss" scoped>
  .img-container {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
  }
  .img-item {
    width: 150px;
    height: 150px;
    margin: 12px;
  }
</style>
