package com.renda.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.renda.core.mapper.RdNoticeFeedbackAttachmentMapper;
import com.renda.core.domain.RdNoticeFeedbackAttachment;
import com.renda.core.service.IRdNoticeFeedbackAttachmentService;

/**
 * 工作通知反馈意见附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
@Service
public class RdNoticeFeedbackAttachmentServiceImpl implements IRdNoticeFeedbackAttachmentService
{
    @Autowired
    private RdNoticeFeedbackAttachmentMapper rdNoticeFeedbackAttachmentMapper;

    /**
     * 查询工作通知反馈意见附件
     *
     * @param id 工作通知反馈意见附件主键
     * @return 工作通知反馈意见附件
     */
    @Override
    public RdNoticeFeedbackAttachment selectRdNoticeFeedbackAttachmentById(Long id)
    {
        return rdNoticeFeedbackAttachmentMapper.selectRdNoticeFeedbackAttachmentById(id);
    }

    /**
     * 查询工作通知反馈意见附件列表
     *
     * @param rdNoticeFeedbackAttachment 工作通知反馈意见附件
     * @return 工作通知反馈意见附件
     */
    @Override
    public List<RdNoticeFeedbackAttachment> selectRdNoticeFeedbackAttachmentList(RdNoticeFeedbackAttachment rdNoticeFeedbackAttachment)
    {
        return rdNoticeFeedbackAttachmentMapper.selectRdNoticeFeedbackAttachmentList(rdNoticeFeedbackAttachment);
    }

    /**
     * 新增工作通知反馈意见附件
     *
     * @param rdNoticeFeedbackAttachment 工作通知反馈意见附件
     * @return 结果
     */
    @Override
    public int insertRdNoticeFeedbackAttachment(RdNoticeFeedbackAttachment rdNoticeFeedbackAttachment)
    {
        return rdNoticeFeedbackAttachmentMapper.insertRdNoticeFeedbackAttachment(rdNoticeFeedbackAttachment);
    }

    /**
     * 修改工作通知反馈意见附件
     *
     * @param rdNoticeFeedbackAttachment 工作通知反馈意见附件
     * @return 结果
     */
    @Override
    public int updateRdNoticeFeedbackAttachment(RdNoticeFeedbackAttachment rdNoticeFeedbackAttachment)
    {
        return rdNoticeFeedbackAttachmentMapper.updateRdNoticeFeedbackAttachment(rdNoticeFeedbackAttachment);
    }

    /**
     * 批量删除工作通知反馈意见附件
     *
     * @param ids 需要删除的工作通知反馈意见附件主键
     * @return 结果
     */
    @Override
    public int deleteRdNoticeFeedbackAttachmentByIds(Long[] ids)
    {
        return rdNoticeFeedbackAttachmentMapper.deleteRdNoticeFeedbackAttachmentByIds(ids);
    }

    /**
     * 删除工作通知反馈意见附件信息
     *
     * @param id 工作通知反馈意见附件主键
     * @return 结果
     */
    @Override
    public int deleteRdNoticeFeedbackAttachmentById(Long id)
    {
        return rdNoticeFeedbackAttachmentMapper.deleteRdNoticeFeedbackAttachmentById(id);
    }
}
