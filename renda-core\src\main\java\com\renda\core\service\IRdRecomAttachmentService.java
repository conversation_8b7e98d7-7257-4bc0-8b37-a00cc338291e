package com.renda.core.service;

import java.util.List;
import com.renda.core.domain.RdRecomAttachment;

/**
 * 代建议附件Service接口
 *
 * <AUTHOR>
 * @date 2023-11-23
 */
public interface IRdRecomAttachmentService
{
    /**
     * 查询代建议附件
     *
     * @param id 代建议附件主键
     * @return 代建议附件
     */
    public RdRecomAttachment selectRdRecomAttachmentById(Long id);

    /**
     * 查询代建议附件列表
     *
     * @param rdRecomAttachment 代建议附件
     * @return 代建议附件集合
     */
    public List<RdRecomAttachment> selectRdRecomAttachmentList(RdRecomAttachment rdRecomAttachment);

    /**
     * 新增代建议附件
     *
     * @param rdRecomAttachment 代建议附件
     * @return 结果
     */
    public int insertRdRecomAttachment(RdRecomAttachment rdRecomAttachment);

    /**
     * 修改代建议附件
     *
     * @param rdRecomAttachment 代建议附件
     * @return 结果
     */
    public int updateRdRecomAttachment(RdRecomAttachment rdRecomAttachment);

    /**
     * 批量删除代建议附件
     *
     * @param ids 需要删除的代建议附件主键集合
     * @return 结果
     */
    public int deleteRdRecomAttachmentByIds(Long[] ids);

    /**
     * 删除建议附件信息
     *
     * @param id 代建议附件主键
     * @return 结果
     */
    public int deleteRdRecomAttachmentById(Long id);

    /**
     * 删除建议附件信息
     *
     * @param recomId 建议主键
     */
    public int deleteRdRecomAttachmentByRecomId(Long recomId);

    /**
     * 删除建议附件信息
     *
     * @param ids 建议主键
     */
    int deleteRdRecomAttachmentByRecomIds(Long[] ids);

}
