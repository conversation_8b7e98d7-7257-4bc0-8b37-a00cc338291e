package com.renda.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.renda.common.annotation.Log;
import com.renda.common.core.controller.BaseController;
import com.renda.common.core.domain.AjaxResult;
import com.renda.common.enums.BusinessType;
import com.renda.core.domain.RdStationStaff;
import com.renda.core.service.IRdStationStaffService;
import com.renda.common.utils.poi.ExcelUtil;
import com.renda.common.core.page.TableDataInfo;

/**
 * 工作人员管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@RestController
@RequestMapping("/renda/stationstaff")
public class RdStationStaffController extends BaseController
{
    @Autowired
    private IRdStationStaffService rdStationStaffService;

    /**
     * 查询工作人员管理列表
     */
    @PreAuthorize("@ss.hasPermi('renda:stationstaff:list')")
    @GetMapping("/list")
    public TableDataInfo list(RdStationStaff rdStationStaff)
    {
        startPage();
        List<RdStationStaff> list = rdStationStaffService.selectRdStationStaffList(rdStationStaff);
        return getDataTable(list);
    }

    /**
     * 导出工作人员管理列表
     */
    @PreAuthorize("@ss.hasPermi('renda:stationstaff:export')")
    @Log(title = "工作人员管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RdStationStaff rdStationStaff)
    {
        List<RdStationStaff> list = rdStationStaffService.selectRdStationStaffList(rdStationStaff);
        ExcelUtil<RdStationStaff> util = new ExcelUtil<RdStationStaff>(RdStationStaff.class);
        util.exportExcel(response, list, "工作人员管理数据");
    }

    /**
     * 获取工作人员管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('renda:stationstaff:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rdStationStaffService.selectRdStationStaffById(id));
    }

    /**
     * 新增工作人员管理
     */
    @PreAuthorize("@ss.hasPermi('renda:stationstaff:add')")
    @Log(title = "工作人员管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RdStationStaff rdStationStaff)
    {
        return toAjax(rdStationStaffService.insertRdStationStaff(rdStationStaff));
    }

    /**
     * 修改工作人员管理
     */
    @PreAuthorize("@ss.hasPermi('renda:stationstaff:edit')")
    @Log(title = "工作人员管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RdStationStaff rdStationStaff)
    {
        return toAjax(rdStationStaffService.updateRdStationStaff(rdStationStaff));
    }

    /**
     * 删除工作人员管理
     */
    @PreAuthorize("@ss.hasPermi('renda:stationstaff:remove')")
    @Log(title = "工作人员管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rdStationStaffService.deleteRdStationStaffByIds(ids));
    }
}
