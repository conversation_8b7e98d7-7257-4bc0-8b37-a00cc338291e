<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>renda</artifactId>
        <groupId>com.renda</groupId>
        <version>3.8.6</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>renda-core</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.renda</groupId>
            <artifactId>renda-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.renda</groupId>
            <artifactId>renda-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.renda</groupId>
            <artifactId>renda-framework</artifactId>
        </dependency>

    </dependencies>

</project>
