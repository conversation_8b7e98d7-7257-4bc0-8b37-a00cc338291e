package com.renda.core.service.impl;

import java.io.File;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.renda.core.mapper.RdJobAttachmentMapper;
import com.renda.core.domain.RdJobAttachment;
import com.renda.core.service.IRdJobAttachmentService;

/**
 * 履职工作附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-12
 */
@Service
public class RdJobAttachmentServiceImpl implements IRdJobAttachmentService
{
    @Autowired
    private RdJobAttachmentMapper rdJobAttachmentMapper;

    @Value("${renda.profile}")
    private String profile;

    /**
     * 查询履职工作附件
     *
     * @param id 履职工作附件主键
     * @return 履职工作附件
     */
    @Override
    public RdJobAttachment selectRdJobAttachmentById(Long id)
    {
        return rdJobAttachmentMapper.selectRdJobAttachmentById(id);
    }

    /**
     * 查询履职工作附件列表
     *
     * @param rdJobAttachment 履职工作附件
     * @return 履职工作附件
     */
    @Override
    public List<RdJobAttachment> selectRdJobAttachmentList(RdJobAttachment rdJobAttachment)
    {
        return rdJobAttachmentMapper.selectRdJobAttachmentList(rdJobAttachment);
    }

    /**
     * 新增履职工作附件
     *
     * @param rdJobAttachment 履职工作附件
     * @return 结果
     */
    @Override
    public int insertRdJobAttachment(RdJobAttachment rdJobAttachment)
    {
        return rdJobAttachmentMapper.insertRdJobAttachment(rdJobAttachment);
    }

    /**
     * 修改履职工作附件
     *
     * @param rdJobAttachment 履职工作附件
     * @return 结果
     */
    @Override
    public int updateRdJobAttachment(RdJobAttachment rdJobAttachment)
    {
        return rdJobAttachmentMapper.updateRdJobAttachment(rdJobAttachment);
    }

    /**
     * 批量删除履职工作附件
     *
     * @param ids 需要删除的履职工作附件主键
     * @return 结果
     */
    @Override
    public int deleteRdJobAttachmentByIds(Long[] ids)
    {
        return rdJobAttachmentMapper.deleteRdJobAttachmentByIds(ids);
    }

    /**
     * 删除履职工作附件信息
     *
     * @param id 履职工作附件主键
     * @return 结果
     */
    @Override
    public int deleteRdJobAttachmentById(Long id)
    {
        return rdJobAttachmentMapper.deleteRdJobAttachmentById(id);
    }

    @Override
    public void deleteJobAttachments(Long jobId) {
        // 先删除文件
        RdJobAttachment attachment = new RdJobAttachment();
        attachment.setJobId(jobId);
        selectRdJobAttachmentList(attachment).forEach(item -> {
            String fileUrl = item.getFileUrl();
            if (fileUrl != null) {
                fileUrl = fileUrl.replaceFirst("/profile", profile);
                // 如果文件存在则删除
                File file = new File(fileUrl);
                if (file.exists()) {
                    file.delete();
                }
            }
        });
        rdJobAttachmentMapper.deleteJobAttachments(jobId);
    }
}
