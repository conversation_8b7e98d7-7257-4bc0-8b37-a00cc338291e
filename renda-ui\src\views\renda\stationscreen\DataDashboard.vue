<template>
  <div class="data-dashboard">
    <app-header />

    <!-- 加载遮罩 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <p class="loading-text">正在初始化设备，请稍候...</p>
      </div>
    </div>

    <!-- 未绑定设备提示 -->
    <div v-if="!isLoading && isUnbound" class="unbound-overlay">
      <div class="unbound-content">
        <div class="unbound-icon">⚠️</div>
        <h3 class="unbound-title">设备未绑定</h3>
        <p class="unbound-message">设备ID：{{ deviceId }}</p>
        <p class="unbound-message">该设备尚未绑定联络站，请联系管理员进行配置</p>
        <div class="unbound-actions">
          <button class="retry-btn" @click="initDevice">重新检测</button>
        </div>
      </div>
    </div>

    <main class="content" @click="handleContentClick" v-if="!isLoading && !isUnbound">
      <!-- 屏保提示 -->
      <div v-if="isScreensaverMode" class="screensaver-hint">
        <div class="hint-content">
          <i class="el-icon-info"></i>
          <span>屏保模式 - 点击任意位置返回</span>
        </div>
      </div>

      <!-- 背景装饰元素 -->
      <div class="background-elements">
        <div class="circle circle-1"></div>
        <div class="circle circle-2"></div>
        <div class="circle circle-3"></div>
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
      </div>

      <!-- 顶部统计卡片 -->
      <div class="stats-grid">
        <div class="stat-card" v-for="(stat, index) in statsData" :key="index">
          <div class="stat-icon" :class="stat.iconClass">
            <span class="icon-text">{{ stat.iconText }}</span>
          </div>
          <div class="stat-content">
            <div class="stat-title">{{ stat.title }}</div>
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-trend" :class="stat.trend.type">
              {{ stat.trend.text }}
            </div>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="charts-container">
        <!-- 第一行：饼图 -->
        <div class="chart-row">
          <div class="chart-card half-width">
            <div class="chart-header">
              <h3 class="chart-title">意见类型分布</h3>
            </div>
            <div class="chart-content" ref="opinionTypeChart"></div>
          </div>

          <div class="chart-card half-width">
            <div class="chart-header">
              <h3 class="chart-title">意见处理状态</h3>
            </div>
            <div class="chart-content" ref="opinionStatusChart"></div>
          </div>
        </div>

        <!-- 第二行：趋势图 -->
        <div class="chart-row">
          <div class="chart-card full-width">
            <div class="chart-header">
              <h3 class="chart-title">近12个月意见数量趋势</h3>
            </div>
            <div class="chart-content large" ref="opinionTrendChart"></div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import AppHeader from '@/components/StationScreen/AppHeader.vue'
import { getStationByDevice, getStationStatistics } from '@/api/renda/stationscreen'
import { exitScreensaver } from '@/utils/screensaver'

export default {
  name: 'DataDashboard',
  components: {
    AppHeader
  },
  data() {
    return {
      deviceId: '', // 本机ID
      stationId: '', // 联络站ID
      isLoading: true, // 加载状态
      isUnbound: false, // 是否未绑定设备
      isScreensaverMode: false, // 是否为屏保模式
      statsData: [
        {
          title: '收到意见总数',
          value: '0',
          iconText: '📊',
          iconClass: 'icon-red',
          trend: { type: 'up', text: '数据加载中...' }
        },
        {
          title: '已处理意见数',
          value: '0',
          iconText: '✓',
          iconClass: 'icon-red-light',
          trend: { type: 'up', text: '数据加载中...' }
        },
        {
          title: '意见办结率',
          value: '0%',
          iconText: '◉',
          iconClass: 'icon-red-medium',
          trend: { type: 'up', text: '数据加载中...' }
        },
        {
          title: '办理满意度',
          value: '0%',
          iconText: '😊',
          iconClass: 'icon-red-dark',
          trend: { type: 'up', text: '数据加载中...' }
        }
      ],
      charts: {}
    }
  },
  computed: {
    ...mapState(['statistics'])
  },
  methods: {
    // 刷新数据的统一方法
    refreshData() {
      // 检测是否为屏保模式
      this.isScreensaverMode = this.$route.query.screensaver === 'true'
      
      // 重新初始化设备和数据
      this.initDevice()
    },

    // 获取设备ID
    getDeviceId() {
      try {
        // 首先尝试从本地存储获取设备名称
        let deviceId = localStorage.getItem('rd_device_id');

        if (!deviceId) {
          // 如果没有保存的设备ID，生成一个基于浏览器指纹的ID
          deviceId = this.generateDeviceFingerprint();
        }

        return deviceId;
      } catch (error) {
        console.error('获取设备ID失败:', error);
        return 'DEVICE-' + Date.now();
      }
    },

    // 生成设备指纹
    generateDeviceFingerprint() {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('Device fingerprint', 2, 2);

        const fingerprint = [
          navigator.userAgent,
          navigator.language,
          screen.width + 'x' + screen.height,
          screen.colorDepth,
          new Date().getTimezoneOffset(),
          canvas.toDataURL()
        ].join('|');

        // 生成简短的哈希值
        const hash = this.simpleHash(fingerprint);
        const deviceId = 'DEVICE-' + hash;

        // 保存到本地存储
        localStorage.setItem('rd_device_id', deviceId);

        return deviceId;
      } catch (error) {
        console.error('生成设备指纹失败:', error);
        const fallbackId = 'DEVICE-' + Date.now();
        localStorage.setItem('rd_device_id', fallbackId);
        return fallbackId;
      }
    },

    // 简单哈希函数
    simpleHash(str) {
      let hash = 0;
      for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
      }
      return Math.abs(hash).toString(36).toUpperCase().substring(0, 8);
    },

    // 初始化设备和获取联络站ID
    async initDevice() {
      this.isLoading = true;
      try {
        // 获取设备ID
        this.deviceId = this.getDeviceId();

        // 调用后台接口获取联络站ID
        const response = await getStationByDevice(this.deviceId);

        if (response.code === 200) {
          this.stationId = response.data.stationId;

          if (this.stationId === '0') {
            // 联络站ID为0，提示用户联系管理员
            this.isUnbound = true;
            this.isLoading = false;
          } else {
            // 获取统计数据（会在内部设置状态）
            await this.loadStatisticsData();
          }
        } else {
          throw new Error(response.msg || '获取联络站ID失败');
        }
      } catch (error) {
        console.error('初始化设备失败:', error);
        this.$message({
          message: '初始化设备失败：' + error.message,
          type: 'error',
          duration: 5000
        });
        this.isLoading = false;
      }
    },

    // 加载统计数据
    async loadStatisticsData() {
      try {
        const response = await getStationStatistics(this.stationId);

        if (response.code === 200) {
          const statistics = response.data;

          // 更新统计卡片数据
          this.updateStatsData(statistics.basicStats);

          // 先设置页面为可见状态，再初始化图表
          this.isLoading = false;
          this.isUnbound = false;

          // 等待DOM更新完成后初始化图表
          this.$nextTick(() => {
            setTimeout(() => {
              this.initCharts();

              // 再等待图表初始化完成后更新数据
              setTimeout(() => {
                this.updateChartsData(statistics);
              }, 800);
            }, 200);
          });
        } else {
          throw new Error(response.msg || '获取统计数据失败');
        }
      } catch (error) {
        console.error('加载统计数据失败:', error);
        this.$message({
          message: '加载统计数据失败：' + error.message,
          type: 'error',
          duration: 3000
        });
        // 设置页面为可见状态
        this.isLoading = false;
        this.isUnbound = false;

        // 初始化图表显示错误状态
        this.$nextTick(() => {
          setTimeout(() => {
            this.initCharts();

            setTimeout(() => {
              this.updateChartsData({
                typeDistribution: [{ name: '暂无数据', value: 1 }],
                statusDistribution: [{ name: '暂无数据', value: 1 }],
                trendData: {
                  months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                  received: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                  processed: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
                }
              });
            }, 800);
          }, 200);
        });
      }
    },

    // 更新统计卡片数据
    updateStatsData(basicStats) {
      if (basicStats) {
        this.statsData[0].value = this.formatNumber(basicStats.totalAdvices);
        this.statsData[1].value = this.formatNumber(basicStats.processedAdvices);
        this.statsData[2].value = basicStats.completionRate + '%';
        this.statsData[3].value = (basicStats.satisfactionRate || 0) + '%';

        // 更新趋势数据
        if (basicStats.totalAdvicesTrend) {
          this.statsData[0].trend = {
            type: basicStats.totalAdvicesTrend.type,
            text: basicStats.totalAdvicesTrend.text
          };
        }

        if (basicStats.processedAdvicesTrend) {
          this.statsData[1].trend = {
            type: basicStats.processedAdvicesTrend.type,
            text: basicStats.processedAdvicesTrend.text
          };
        }

        if (basicStats.completionRateTrend) {
          this.statsData[2].trend = {
            type: basicStats.completionRateTrend.type,
            text: basicStats.completionRateTrend.text
          };
        }

        if (basicStats.satisfactionRateTrend) {
          this.statsData[3].trend = {
            type: basicStats.satisfactionRateTrend.type,
            text: basicStats.satisfactionRateTrend.text
          };
        }
      }
    },

    // 格式化数字
    formatNumber(num) {
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },

    // 更新图表数据
    updateChartsData(statistics) {

      // 更新意见类型分布图表
      if (statistics.typeDistribution && statistics.typeDistribution.length > 0) {
        if (this.charts.opinionType) {
          const chartData = statistics.typeDistribution.map((item, index) => ({
            value: item.value,
            name: item.name,
            itemStyle: { color: this.getTypeColor(index) }
          }));

          const option = {
            series: [{
              name: '意见类型',
              type: 'pie',
              data: chartData
            }]
          };
          this.charts.opinionType.setOption(option);
        } else {
          console.error('意见类型分布图表对象不存在');
        }
      }

      // 更新意见处理状态图表
      if (statistics.statusDistribution && statistics.statusDistribution.length > 0) {
        if (this.charts.opinionStatus) {
          const chartData = statistics.statusDistribution.map((item, index) => ({
            value: item.value,
            name: item.name,
            itemStyle: { color: this.getStatusColor(index) }
          }));

          const option = {
            series: [{
              name: '处理状态',
              type: 'pie',
              data: chartData
            }]
          };
          this.charts.opinionStatus.setOption(option);
        } else {
          console.error('意见处理状态图表对象不存在');
        }
      }

      // 更新月度趋势图表
      if (statistics.trendData && statistics.trendData.months) {
        if (this.charts.opinionTrend) {

          // 计算办结率
          const completionRates = statistics.trendData.received.map((received, index) => {
            const processed = statistics.trendData.processed[index];
            return received > 0 ? parseFloat((processed / received * 100).toFixed(1)) : 0;
          });

          const option = {
            xAxis: {
              data: statistics.trendData.months
            },
            series: [
              {
                name: '收到意见',
                type: 'bar',
                data: statistics.trendData.received
              },
              {
                name: '已处理',
                type: 'bar',
                data: statistics.trendData.processed
              },
              {
                name: '办结率',
                type: 'line',
                yAxisIndex: 1,
                data: completionRates
              }
            ]
          };
          this.charts.opinionTrend.setOption(option);
        } else {
          console.error('月度趋势图表对象不存在');
        }
      }
    },

    // 获取类型颜色
    getTypeColor(index) {
      const colors = ['#d71718', '#e74c3c', '#c0392b', '#a93226', '#922b21', '#7b241c'];
      return colors[index % colors.length];
    },

    // 获取状态颜色
    getStatusColor(index) {
      const colors = ['#d71718', '#e74c3c', '#95a5a6'];
      return colors[index % colors.length];
    },

    initCharts() {
      this.$nextTick(() => {
        this.initOpinionTypeChart()
        this.initOpinionStatusChart()
        this.initOpinionTrendChart()
      })
    },

    initOpinionTypeChart() {
      if (!this.$refs.opinionTypeChart) {
        console.error('意见类型分布图表DOM元素不存在');
        return;
      }

              try {
          const chart = this.$echarts.init(this.$refs.opinionTypeChart)
          const option = {
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c} ({d}%)',
              textStyle: {
                fontSize: 28
              }
            },
            legend: {
              orient: 'vertical',
              left: 'left',
              textStyle: {
                color: '#666',
                fontSize: 28
              }
            },
            series: [
              {
                name: '意见类型',
                type: 'pie',
                radius: ['40%', '70%'],
                center: ['60%', '50%'],
                avoidLabelOverlap: false,
                itemStyle: {
                  borderRadius: 8,
                  borderColor: '#fff',
                  borderWidth: 2
                },
                label: {
                  show: false
                },
                emphasis: {
                  label: {
                    show: true,
                    fontSize: 22,
                    fontWeight: 'bold'
                  }
                },
                data: []
              }
            ]
          }
          chart.setOption(option)
          this.charts.opinionType = chart
        } catch (error) {
          console.error('意见类型分布图表初始化失败:', error);
        }
      },

    initOpinionStatusChart() {
      if (!this.$refs.opinionStatusChart) {
        console.error('意见处理状态图表DOM元素不存在');
        return;
      }

      try {
        const chart = this.$echarts.init(this.$refs.opinionStatusChart)
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)',
            textStyle: {
              fontSize: 28
            }
          },
          legend: {
            bottom: '5%',
            left: 'center',
            textStyle: {
              color: '#666',
              fontSize: 28
            }
          },
          series: [
            {
              name: '处理状态',
              type: 'pie',
              radius: ['40%', '70%'],
              center: ['50%', '45%'],
              itemStyle: {
                borderRadius: 8,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: false
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 22,
                  fontWeight: 'bold'
                }
              },
              data: []
            }
          ]
        }
        chart.setOption(option)
        this.charts.opinionStatus = chart
      } catch (error) {
        console.error('意见处理状态图表初始化失败:', error);
      }
    },

    initOpinionTrendChart() {
      if (!this.$refs.opinionTrendChart) {
        console.error('月度趋势图表DOM元素不存在');
        return;
      }

      try {
        const chart = this.$echarts.init(this.$refs.opinionTrendChart)

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          textStyle: {
            fontSize: 28
          }
        },
        legend: {
          data: ['收到意见', '已处理', '办结率'],
          textStyle: {
            color: '#666',
            fontSize: 28
          },
          top: '2%'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '8%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLine: {
            lineStyle: {
              color: '#e0e0e0'
            }
          },
          axisLabel: {
            color: '#666',
            fontSize: 20
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '数量',
            position: 'left',
            axisLine: {
              lineStyle: {
                color: '#e0e0e0'
              }
            },
            axisLabel: {
              color: '#666',
              fontSize: 20
            },
            splitLine: {
              lineStyle: {
                color: '#f0f0f0'
              }
            }
          },
          {
            type: 'value',
            name: '办结率(%)',
            position: 'right',
            axisLine: {
              lineStyle: {
                color: '#e0e0e0'
              }
            },
            axisLabel: {
              color: '#666',
              formatter: '{value}%',
              fontSize: 20
            }
          }
        ],
        series: [
          {
            name: '收到意见',
            type: 'bar',
            data: [],
            itemStyle: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#d71718' },
                { offset: 1, color: '#e74c3c' }
              ])
            }
          },
          {
            name: '已处理',
            type: 'bar',
            data: [],
            itemStyle: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#c0392b' },
                { offset: 1, color: '#a93226' }
              ])
            }
          },
          {
            name: '办结率',
            type: 'line',
            yAxisIndex: 1,
            data: [],
            symbol: 'circle',
            symbolSize: 10,
            itemStyle: {
              color: '#95a5a6'
            },
            lineStyle: {
              color: '#95a5a6',
              width: 4
            }
          }
        ]
        }
        chart.setOption(option)
        this.charts.opinionTrend = chart
      } catch (error) {
        console.error('月度趋势图表初始化失败:', error);
      }
    },

    handleResize() {
      Object.values(this.charts).forEach(chart => {
        chart && chart.resize()
      })
    },

    // 处理键盘事件
    handleKeydown(event) {
      // ESC键返回首页
      if (event.key === 'Escape') {
        this.handleContentClick()
      }
    },

    // 处理内容点击事件
    handleContentClick() {
      if (this.isScreensaverMode || this.$route.query.screensaver === 'true') {
        // 屏保模式下点击，退出屏保
        exitScreensaver()
      } else {
        // 非屏保模式下点击，跳转到首页
        this.goToHome()
      }
    },

    goToHome() {
      // 尝试跳转到 StationScreen Home 页面
      this.$router.push({ 
        name: 'StationScreenHome',
        query: { stationId: this.stationId }
      }).catch(err => {
        console.error('跳转失败，尝试备用路径:', err)
        // 如果路由名称不存在，尝试直接使用路径
        this.$router.push({ 
          path: '/renda/stationscreen/Home',
          query: { stationId: this.stationId }
        }).catch(err2 => {
          console.error('跳转失败:', err2)
        })
      })
    }
  },
  watch: {
    // 监听路由变化，确保每次进入页面都刷新数据
    $route(to, from) {
      if (to.name === 'DataDashboard' || to.path.includes('DataDashboard')) {
        this.refreshData()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      // 禁用右键菜单
      document.oncontextmenu = function() {
        return false;
      };
    })

    // 初始化数据
    this.refreshData()

    window.addEventListener('resize', this.handleResize)
    document.addEventListener('keydown', this.handleKeydown)
  },
  activated() {
    // 当使用 keep-alive 时，每次激活组件都刷新数据
    this.refreshData()
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    document.removeEventListener('keydown', this.handleKeydown)
    Object.values(this.charts).forEach(chart => {
      chart && chart.dispose()
    })
  }
}
</script>

<style lang="scss" scoped>
.data-dashboard {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f8f8f8, #efefef);
  color: #333;
  overflow: hidden;
  font-family: 'AL-L' !important;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  margin: 0;
  padding: 0;
}

.content {
  flex: 1;
  padding: 20px;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

// 屏保提示
.screensaver-hint {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  pointer-events: none;
  
  .hint-content {
    background: rgba(215, 23, 24, 0.9);
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    animation: pulse 2s infinite;
    
    i {
      font-size: 18px;
    }
  }
}

@keyframes pulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

// 背景装饰元素
.background-elements {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 0;
  pointer-events: none;

  .circle {
    position: absolute;
    border-radius: 50%;
    opacity: 0.06;

    &.circle-1 {
      width: 600px;
      height: 600px;
      background: #d71718;
      top: -200px;
      left: -200px;
    }

    &.circle-2 {
      width: 400px;
      height: 400px;
      background: #d71718;
      bottom: 10%;
      right: 10%;
    }

    &.circle-3 {
      width: 300px;
      height: 300px;
      background: #d71718;
      bottom: 35%;
      left: 5%;
    }
  }

  .shape {
    position: absolute;
    opacity: 0.04;
    background: #d71718;

    &.shape-1 {
      width: 600px;
      height: 300px;
      transform: rotate(45deg);
      top: 30%;
      right: -300px;
    }

    &.shape-2 {
      width: 400px;
      height: 400px;
      transform: rotate(30deg);
      bottom: -50px;
      left: 30%;
    }
  }
}

// 统计卡片网格
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
  flex-shrink: 0;
}

// 统计卡片
.stat-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 6px;
    height: 100%;
    background: rgba(215, 23, 24, 0.7);
    border-radius: 20px 0 0 20px;
    transform: scaleY(0);
    transform-origin: top;
    transition: transform 0.3s;
  }

  &:hover::before {
    transform: scaleY(1);
  }
}

// 统计图标
.stat-icon {
  width: 70px;
  height: 70px;
  border-radius: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20px;
  transition: all 0.3s;

  .icon-text {
    font-size: 40px;
    line-height: 1;
    color: #d71718;
    font-weight: bold;
  }

  &.icon-red {
    background: linear-gradient(135deg, rgba(215, 23, 24, 0.15), rgba(215, 23, 24, 0.25));
    border: 2px solid rgba(215, 23, 24, 0.2);
  }

  &.icon-red-light {
    background: linear-gradient(135deg, rgba(215, 23, 24, 0.12), rgba(215, 23, 24, 0.22));
    border: 2px solid rgba(215, 23, 24, 0.18);
  }

  &.icon-red-medium {
    background: linear-gradient(135deg, rgba(215, 23, 24, 0.1), rgba(215, 23, 24, 0.2));
    border: 2px solid rgba(215, 23, 24, 0.15);
  }

  &.icon-red-dark {
    background: linear-gradient(135deg, rgba(215, 23, 24, 0.08), rgba(215, 23, 24, 0.18));
    border: 2px solid rgba(215, 23, 24, 0.12);
  }
}

.stat-card:hover .stat-icon {
  transform: scale(1.1);
}

// 统计内容
.stat-content {
  flex: 1;
}

.stat-title {
  color: #666;
  font-size: 20px;
  margin-bottom: 8px;
  font-weight: 500;
  font-family: 'AL-L' !important;
}

.stat-value {
  color: #333;
  font-size: 38px;
  font-weight: 500;
  margin-bottom: 6px;
  line-height: 1;
  font-family: 'AL-BL' !important;
}

.stat-trend {
  font-size: 18px;
  display: flex;
  align-items: center;
  font-weight: 500;
  margin-left: 240px;

  &.up {
    color: #00b894;

    &::before {
      content: '↗';
      margin-right: 6px;
      font-size: 20px;
    }
  }

  &.down {
    color: #e17055;

    &::before {
      content: '↘';
      margin-right: 6px;
      font-size: 20px;
    }
  }

  &.neutral {
    color: #74b9ff;

    &::before {
      content: '→';
      margin-right: 6px;
      font-size: 20px;
    }
  }
}

// 图表容器
.charts-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: relative;
  z-index: 1;
  flex: 1;
  min-height: 0;
}

.chart-row {
  display: flex;
  gap: 20px;
  flex: 1;
  min-height: 0;

  &:first-child {
    flex: 1.2;
  }

  &:last-child {
    flex: 1.5;
  }
}

.chart-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  min-height: 0;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  }

  &.half-width {
    flex: 1;
  }

  &.full-width {
    width: 100%;
  }
}

.chart-header {
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 3px solid rgba(215, 23, 24, 0.1);
  position: relative;
  flex-shrink: 0;

  &::before {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 50px;
    height: 3px;
    background: #d71718;
  }
}

.chart-title {
  color: #333;
  font-size: 26px;
  font-weight: 500;
  margin: 0;
  font-family: 'AL-BL' !important;
}

.chart-content {
  flex: 1;
  min-height: 0;
}

// 4K分辨率适配
@media (min-width: 3000px) {
  .content {
    padding: 30px;
  }

  .stats-grid {
    gap: 30px;
    margin-bottom: 30px;
  }

  .stat-card {
    padding: 30px;
    border-radius: 30px;
  }

  .stat-icon {
    width: 105px;
    height: 105px;
    border-radius: 24px;
    margin-right: 30px;

    .icon-text {
      font-size: 60px;
    }
  }

  .stat-title {
    font-size: 30px;
    margin-bottom: 12px;
  }

  .stat-value {
    font-size: 57px;
    margin-bottom: 9px;
  }

  .stat-trend {
    font-size: 27px;

    &::before {
      font-size: 30px;
      margin-right: 9px;
    }
  }

  .charts-container {
    gap: 30px;
  }

  .chart-row {
    gap: 30px;
  }

  .chart-card {
    padding: 30px;
    border-radius: 30px;
  }

  .chart-header {
    margin-bottom: 22px;
    padding-bottom: 15px;
    border-bottom-width: 5px;

    &::before {
      width: 75px;
      height: 5px;
      bottom: -5px;
    }
  }

  .chart-title {
    font-size: 39px;
  }

  .screensaver-hint {
    top: 30px;
    right: 30px;

    .hint-content {
      padding: 18px 30px;
      border-radius: 35px;
      font-size: 24px;
      gap: 12px;

      i {
        font-size: 27px;
      }
    }
  }
}

// 加载遮罩样式
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 6px solid rgba(255, 255, 255, 0.3);
  border-top: 6px solid #d71718;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 24px;
  margin: 0;
  font-family: 'AL-L' !important;
}

// 未绑定设备样式
.unbound-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #f8f8f8, #efefef);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.unbound-content {
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  padding: 60px;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 600px;
}

.unbound-icon {
  font-size: 80px;
  margin-bottom: 20px;
}

.unbound-title {
  color: #d71718;
  font-size: 36px;
  margin-bottom: 20px;
  font-family: 'AL-BL' !important;
}

.unbound-message {
  color: #666;
  font-size: 20px;
  margin-bottom: 15px;
  font-family: 'AL-L' !important;
  line-height: 1.5;
}

.unbound-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 20px;
}

.retry-btn, .settings-btn {
  border: none;
  padding: 15px 30px;
  border-radius: 12px;
  font-size: 18px;
  font-family: 'AL-L' !important;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }
}

.retry-btn {
  background: linear-gradient(135deg, #d71718, #e74c3c);
  color: white;

  &:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    box-shadow: 0 5px 15px rgba(215, 23, 24, 0.3);
  }
}

.settings-btn {
  background: linear-gradient(135deg, #95a5a6, #7f8c8d);
  color: white;

  &:hover {
    background: linear-gradient(135deg, #7f8c8d, #6c7b7d);
    box-shadow: 0 5px 15px rgba(149, 165, 166, 0.3);
  }
}

// 响应式设计
@media (max-width: 1400px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .chart-row {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .content {
    padding: 15px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 15px;
    margin-bottom: 15px;
  }

  .stat-card {
    padding: 18px;
    border-radius: 16px;
  }

  .stat-icon {
    width: 55px;
    height: 55px;
    margin-right: 15px;

    .icon-text {
      font-size: 30px;
    }
  }

  .stat-title {
    font-size: 16px;
  }

  .stat-value {
    font-size: 28px;
  }

  .stat-trend {
    font-size: 14px;
  }

  .charts-container {
    gap: 15px;
  }

  .chart-card {
    padding: 18px;
    border-radius: 16px;
  }

  .chart-title {
    font-size: 20px;
  }
}
</style>
