<template>
  <div class="app-container bg flex col">
    <ScreenHeader />
    <div class="root" @click="onDeputyGroup('6522')">
      <el-image class="logo" :src="require('@/assets/images/screen/gh256.png')"></el-image>
      <div class="title">哈密市人大代表团</div>
    </div>
    <div class="line1"></div>
    <div class="line2"></div>
    <div class="line3">
      <div class="line4"></div>
      <div class="line4"></div>
      <div class="line4"></div>
      <div class="line4"></div>
    </div>
    <div class="level2">
      <div class="group" @click="onDeputyGroup('652201')">
        <el-image class="logo" :src="require('@/assets/images/screen/gh256.png')"></el-image>
        <div class="title">伊州区人大代表团</div>
      </div>
      <div class="group" @click="onDeputyGroup('652222')">
        <el-image class="logo" :src="require('@/assets/images/screen/gh256.png')"></el-image>
        <div class="title">巴里坤县人大代表团</div>
      </div>
      <div class="group" @click="onDeputyGroup('652223')">
        <el-image class="logo" :src="require('@/assets/images/screen/gh256.png')"></el-image>
        <div class="title">伊吾县人大代表团</div>
      </div>
      <div class="group" @click="onDeputyGroup('652230')">
        <el-image class="logo" :src="require('@/assets/images/screen/gh256.png')"></el-image>
        <div class="title">
          兵团十三师人大代表团 解放军人大代表团
        </div>
      </div>
    </div>
    <div class="line5">
      <div class="line6"></div>
      <div class="line-none"></div>
      <div class="line-none"></div>
      <div class="line-none"></div>
    </div>
    <div class="line7"></div>
    <div class="line8">
      <div class="line9"></div>
      <div class="line9"></div>
      <div class="line9"></div>
      <div class="line9"></div>
    </div>
    <div class="level3">
      <div class="group" @click="onDeputyGroup('65220101')">
        <el-image class="logo" :src="require('@/assets/images/screen/gh256.png')"></el-image>
        <div class="title">第一组</div>
      </div>
      <div class="group" @click="onDeputyGroup('65220102')">
        <el-image class="logo" :src="require('@/assets/images/screen/gh256.png')"></el-image>
        <div class="title">第二组</div>
      </div>
      <div class="group" @click="onDeputyGroup('65220103')">
        <el-image class="logo" :src="require('@/assets/images/screen/gh256.png')"></el-image>
        <div class="title">第三组</div>
      </div>
      <div class="group" @click="onDeputyGroup('65220104')">
        <el-image class="logo" :src="require('@/assets/images/screen/gh256.png')"></el-image>
        <div class="title">第四组</div>
      </div>
    </div>
  </div>
</template>

<script>

import ScreenHeader from "./components/screenHeader";

export default {
  name: "DeputyGroup",
  components: { ScreenHeader },
  data() {
    return {
    };
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      document.oncontextmenu = function() {
        return false;
      };
    });
  },
  methods: {
    onDeputyGroup(groupId) {
      this.$router.push({ path: 'DeputyList', query: { groupId: groupId } })
    },
  }
};
</script>

<style lang="scss" scoped>

@import '../../../common/font/font.css';

.root {
  margin: 400px auto 20px auto;
  display: flex;
  flex-direction: row;
  position: relative;
  width: 800px;
  height: 200px;
  border-radius: 24px;
  box-shadow: 0 2px 12px 2px rgba(255, 255, 255, 1);
  background-image: linear-gradient(to bottom, rgba(252, 56, 56, 0.8), rgb(245, 44, 44, 0.9));
  .logo {
    margin: auto 40px;
    width: 150px;
    height: 150px;
    opacity: 0.9;
    flex-shrink: 0;
  }
  .title {
    margin: auto 0;
    font-family: AL-BL;
    font-size: 64px;
    color: #FFF;
    text-shadow: 0px 2px 6px #B07878;
  }
}

.line1 {
  margin: 0 auto;
  width: 3px;
  height: 200px;
  background-color: #ffeb5e;
  box-shadow: 0px 2px 12px 0px rgba(0,0,0,0.3);
}
.line2 {
  margin: 0 auto;
  width: 2104px;
  height: 3px;
  background-color: #ffeb5e;
  box-shadow: 0px 2px 12px 0px rgba(0,0,0,0.3);
}
.line3 {
  margin: 0 500px;
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  .line4 {
    margin: 0 auto;
    width: 4px;
    height: 200px;
    background-color: #ffeb5e;
    box-shadow: 0px 2px 12px 0px rgba(0,0,0,0.3);
  }
}

.level2 {
  margin: 0 500px;
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  .group {
    margin: 20px auto 20px auto;
    display: flex;
    flex-direction: row;
    position: relative;
    width: 600px;
    height: 200px;
    border-radius: 24px;
    box-shadow: 0px 2px 12px 0px rgba(0,0,0,0.5);
    background-image: linear-gradient(to bottom, rgba(255,246,239,0.9), rgba(250, 233, 221, 1));
    .logo {
      margin: auto 30px;
      width: 120px;
      height: 120px;
      opacity: 0.9;
      flex-shrink: 0;
    }
    .title {
      margin: auto 0;
      font-family: AL-R;
      font-size: 40px;
      color: #F04040;
      text-shadow: 0px 2px 6px #B07878;
    }
  }
}

.line5 {
  margin: 0 500px;
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  .line6 {
    margin: 0 auto;
    width: 4px;
    height: 200px;
    background-color: #ffeb5e;
    box-shadow: 0px 2px 12px 0px rgba(0,0,0,0.3);
  }
  .line-none {
    margin: 0 auto;
    width: 4px;
    height: 200px;
    background-color: rgba(0,0,0,0);
  }
}
.line7 {
  margin-left: 358px;
  width: 964px;
  height: 4px;
  background-color: #ffeb5e;
  box-shadow: 0px 2px 12px 0px rgba(0,0,0,0.3);
}
.line8 {
  margin-left: 200px;
  width: 1280px;
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  .line9 {
    margin: 0 auto;
    width: 4px;
    height: 150px;
    background-color: #ffeb5e;
    box-shadow: 0px 2px 12px 0px rgba(0,0,0,0.3);
  }
}


.level3 {
  display: flex;
  flex-direction: row;
  width: 1280px;
  margin-left: 200px;
  .group {
    margin: 20px auto 0 auto;
    display: flex;
    flex-direction: row;
    position: relative;
    width: 300px;
    height: 130px;
    border-radius: 24px;
    box-shadow: 0px 2px 12px 0px rgba(0,0,0,0.5);
    background-image: linear-gradient(to bottom, rgba(255,246,239,0.9), rgba(250, 233, 221, 1));
    .logo {
      margin: auto 30px;
      width: 80px;
      height: 80px;
      opacity: 0.9;
      flex-shrink: 0;
    }
    .title {
      margin: auto 0;
      font-family: AL-R;
      font-size: 40px;
      color: #F04040;
      text-shadow: 0px 2px 6px #B07878;
    }
  }
}


</style>
