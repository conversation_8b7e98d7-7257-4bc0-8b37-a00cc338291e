<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renda.core.mapper.RdJobAttachmentMapper">

    <resultMap type="RdJobAttachment" id="RdJobAttachmentResult">
        <result property="id"    column="id"    />
        <result property="jobId"    column="Job_id"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileUrl"    column="file_url"    />
    </resultMap>

    <sql id="selectRdJobAttachmentVo">
        select id, Job_id, file_type, file_name, file_url from rd_job_attachment
    </sql>

    <select id="selectRdJobAttachmentList" parameterType="RdJobAttachment" resultMap="RdJobAttachmentResult">
        <include refid="selectRdJobAttachmentVo"/>
        <where>
            <if test="jobId != null "> and Job_id = #{jobId}</if>
            <if test="fileType != null "> and file_type = #{fileType}</if>
            <if test="fileName != null  and fileName != ''"> and file_name = #{fileName}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
        </where>
    </select>

    <select id="selectRdJobAttachmentById" parameterType="Long" resultMap="RdJobAttachmentResult">
        <include refid="selectRdJobAttachmentVo"/>
        where id = #{id}
    </select>

    <insert id="insertRdJobAttachment" parameterType="RdJobAttachment" useGeneratedKeys="true" keyProperty="id">
        insert into rd_job_attachment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="jobId != null">Job_id,</if>
            <if test="fileType != null">file_type,</if>
            <if test="fileName != null and fileName != ''">file_name,</if>
            <if test="fileUrl != null and fileUrl != ''">file_url,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="jobId != null">#{jobId},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="fileName != null and fileName != ''">#{fileName},</if>
            <if test="fileUrl != null and fileUrl != ''">#{fileUrl},</if>
         </trim>
    </insert>

    <update id="updateRdJobAttachment" parameterType="RdJobAttachment">
        update rd_job_attachment
        <trim prefix="SET" suffixOverrides=",">
            <if test="jobId != null">Job_id = #{jobId},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="fileName != null and fileName != ''">file_name = #{fileName},</if>
            <if test="fileUrl != null and fileUrl != ''">file_url = #{fileUrl},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRdJobAttachmentById" parameterType="Long">
        delete from rd_job_attachment where id = #{id}
    </delete>

    <delete id="deleteRdJobAttachmentByIds" parameterType="String">
        delete from rd_job_attachment where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteJobAttachments" parameterType="Long">
        delete from rd_job_attachment where Job_id = #{jobId}
    </delete>

</mapper>
