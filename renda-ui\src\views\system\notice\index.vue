<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="标题" prop="noticeTitle">
        <el-input
          v-model="queryParams.noticeTitle"
          placeholder="请输入标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="操作人员" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入操作人员"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类型" prop="noticeType">
        <el-select v-model="queryParams.noticeType" placeholder="动态类型" clearable>
          <el-option
            v-for="dict in dict.type.sys_notice_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:notice:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:notice:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:notice:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="noticeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" width="55" align="center" prop="noticeId" />
      <el-table-column label="标题" align="left" prop="noticeTitle" :show-overflow-tooltip="true" />
      <el-table-column label="代表反馈" align="center" width="80">
        <template slot-scope="scope">
          <span class="feedback" v-if="scope.row.noticeType == '5'" @click="handleFeedback(scope.row)" >{{ scope.row.feedbackCount }}</span>
          <span v-else></span>
        </template>
      </el-table-column>
      <el-table-column label="类型" align="center" prop="noticeType" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_notice_type" :value="scope.row.noticeType"/>
        </template>
      </el-table-column>
      <el-table-column label="分类" align="center" prop="noticeCategory" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.rd_news_category" :value="scope.row.noticeCategory" v-if="scope.row.noticeType === '2'" />
        </template>
      </el-table-column>
      <el-table-column label="置顶状态" align="center" key="isTop" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.rd_istop" :value="scope.row.isTop" v-if="scope.row.isTop === '1'" />
        </template>
      </el-table-column>
      <el-table-column label="发布状态" align="center" key="status" width="100">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="1"
            inactive-value="0"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建者" align="center" prop="createBy" width="100" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="100">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:notice:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:notice:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公告对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="780px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="标题" prop="noticeTitle">
              <el-input v-model="form.noticeTitle" placeholder="请输入标题" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="类型" prop="noticeType">
              <el-select v-model="form.noticeType" placeholder="请选择类型">
                <el-option
                  v-for="dict in dict.type.sys_notice_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分类" prop="noticeType" v-if="form.noticeType === '2'">
              <el-select v-model="form.noticeCategory" placeholder="请选择类别">
                <el-option
                  v-for="dict in dict.type.rd_news_category"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="允许反馈" prop="noticeType" v-if="form.noticeType === '5'">
              <el-radio-group v-model="form.allowFeedback">
                <el-radio :key="0" :label="0">不允许</el-radio>
                <el-radio :key="1" :label="1">允许</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="发布人" prop="noticePublisher">
              <el-input v-model="form.noticePublisher" placeholder="请输入发布人" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="发布状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_notice_status"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="置顶状态">
              <el-radio-group v-model="form.isTop">
                <el-radio
                  v-for="dict in dict.type.rd_istop"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="封面图片" prop="coverUrl">
              <el-upload
                class="cover-uploader"
                :headers="headers"
                :action="uploadUrl"
                :show-file-list="false"
                :on-success="handleCoverSuccess"
                :before-upload="beforeCoverUpload">
                <el-image v-if="form.coverUrl" class="cover-img" :src="baseURL + form.coverUrl" fit="cover"></el-image>
                <i v-else class="el-icon-plus cover-uploader-icon"></i>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="内容">
              <editor v-model="form.noticeContent" :min-height="192"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 建议详情对话框 -->
    <el-dialog title="通知反馈信息" :visible.sync="showNoticeFeedback" width="1000px" append-to-body>
      <el-row>
        <el-col :span="24">
          <div class="item">
            <div class="content">
              <el-steps direction="vertical" :active="feedbackList.length">
                <el-step v-for="(item, index) of feedbackList">
                  <div slot="title" class="step-label">{{ item.deputyName + ' - ' + item.phone }} ( {{item.createTime}} )</div>
                  <div slot="description" class="step-desc">
                    <div class="feedback-content">{{item.content}}</div>
                    <div v-for="(fileItem, index) of item.attachments" :key="index" >
                      <div class="file-box" v-if="fileItem.fileType === 2">
                        <el-image class="img-icon" :src="fileItem.icon" />
                        <el-link type="primary" :href="fileItem.fileUrl" target="_blank">{{fileItem.fileName}}</el-link>
                      </div>
                    </div>
                    <div class="img-container" >
                      <el-image v-for="(imgItem, index) of item.attachments"
                                :key="index"
                                v-if="imgItem.fileType === 1"
                                class="img-item"
                                :src="imgItem.fileUrl"
                                :preview-src-list="getPreviewImgList(item.attachments, index)"></el-image>
                    </div>
                  </div>
                </el-step>
              </el-steps>
            </div>
          </div>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showNoticeFeedback = false">关闭</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listNotice, getNotice, delNotice, addNotice, updateNotice, changeNoticeStatus } from "@/api/system/notice";
import { getToken } from '@/utils/auth'
import { getNoticeFeedback } from '@/api/renda/noticefeedback'

export default {
  name: "Notice",
  dicts: ['sys_notice_status', 'sys_notice_type', 'rd_news_category', 'rd_istop'],
  data() {
    return {
      baseURL: process.env.VUE_APP_BASE_API, // 图片上传地址头
      uploadUrl: process.env.VUE_APP_BASE_API + "/common/upload", // 上传的图片服务器地址
      headers: {
        Authorization: "Bearer " + getToken()
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      noticeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        noticeTitle: undefined,
        createBy: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        noticeTitle: [
          { required: true, message: "标题不能为空", trigger: "blur" }
        ],
        noticeType: [
          { required: true, message: "类型不能为空", trigger: "change" }
        ],
        noticePublisher: [
          { required: true, message: "发布人不能为空", trigger: "change" }
        ]
      },
      showNoticeFeedback: false,
      feedbackList: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询公告列表 */
    getList() {
      this.loading = true;
      listNotice(this.queryParams).then(response => {
        this.noticeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        noticeId: undefined,
        noticeTitle: undefined,
        noticeType: undefined,
        noticeCategory: undefined,
        noticeContent: undefined,
        noticePublisher: undefined,
        coverUrl: undefined,
        status: "0",
        isTop: "0",
        allowFeedback: 0
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.noticeId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加公告";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const noticeId = row.noticeId || this.ids
      getNotice(noticeId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改动态";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.noticeId != undefined) {
            updateNotice(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addNotice(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const noticeIds = row.noticeId || this.ids
      this.$modal.confirm('是否确认删除公告编号为"' + noticeIds + '"的数据项？').then(function() {
        return delNotice(noticeIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 通知状态修改 */
    handleStatusChange(row) {
      let text = row.status === "1" ? "上线" : "下线";
      this.$modal.confirm('确认要【' + text + '】本动态吗？').then(function() {
        return changeNoticeStatus(row.noticeId, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.status = row.status === "0" ? "1" : "0";
      });
    },
    beforeCoverUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isJPG && !isPNG) {
        this.$message.error('上传图片只能是 JPG/PNG 格式!')
      }
      if (!isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!')
      }
      return (isJPG || isPNG) && isLt5M
    },
    handleCoverSuccess(res, file) {
      this.form.coverUrl = res.fileName
    },
    handleFeedback(row) {
      getNoticeFeedback(row).then(response => {
        this.feedbackList = response.data
        this.feedbackList.forEach(item => {
          item.attachments.forEach(attach => {
            attach.fileUrl = this.baseURL + attach.fileUrl
            if (attach.fileType === 2) {
              // 获取文件扩展名
              const ext = attach.fileUrl.substring(attach.fileUrl.lastIndexOf('.') + 1).toLowerCase();
              if (ext === 'pdf') {
                attach.icon = require('@/assets/images/screen/filetype/pdf.png');
              } else if (ext === 'doc' || ext === 'docx') {
                attach.icon = require('@/assets/images/screen/filetype/doc.png');
              } else if (ext === 'xls' || ext === 'xlsx') {
                attach.icon = require('@/assets/images/screen/filetype/xls.png');
              } else if (ext === 'ppt' || ext === 'pptx') {
                attach.icon = require('@/assets/images/screen/filetype/ppt.png');
              } else {
                attach.icon = require('@/assets/images/screen/filetype/unknow.png');
              }
            }
          })
        });
        this.showNoticeFeedback = true
      })
    },
    // 大图预览，实现点击当前图片显示当前图片大图，可以随机切换到其他图片进行展示
    getPreviewImgList:function(attachmentList, index) {
      let arr = []
      attachmentList.forEach(item => {
        if (item.fileType == 1) {
          arr.push(item.fileUrl)
        }
      })
      return arr;
    },
  }
};
</script>

<style lang="scss" scoped>

  .cover-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 300px;
    height: 200px;
    line-height: 200px;
    text-align: center;
  }

  .cover-img {
    width: 300px;
    height: 200px;
    display: block;
  }

  .feedback {
    color: #337ab7;
    text-decoration: none;
  }
  .feedback:hover {
    color: red;
    text-decoration: underline;
    cursor: pointer;
  }

  .img-item {
    width: 200px;
    height: 150px;
    margin: 12px;
  }

  .file-box {
    display: flex;
    margin: 6px 0;
    .img-icon {
      width: 24px;
      height: 24px;
      margin-right: 6px;
    }
  }


</style>
