package com.renda.system.service;

import java.util.List;
import com.renda.system.domain.SysNotice;

/**
 * 公告 服务层
 *
 * <AUTHOR>
 */
public interface ISysNoticeService
{
    /**
     * 查询公告信息
     *
     * @param noticeId 公告ID
     * @return 公告信息
     */
    public SysNotice selectNoticeById(Long noticeId);

    /**
     * 查询公告列表
     *
     * @param notice 公告信息
     * @return 公告集合
     */
    public List<SysNotice> selectNoticeList(SysNotice notice);

    /**
     * 新增公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    public int insertNotice(SysNotice notice);

    /**
     * 修改公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    public int updateNotice(SysNotice notice);

    /**
     * 删除公告信息
     *
     * @param noticeId 公告ID
     * @return 结果
     */
    public int deleteNoticeById(Long noticeId);

    /**
     * 批量删除公告信息
     *
     * @param noticeIds 需要删除的公告ID
     * @return 结果
     */
    public int deleteNoticeByIds(Long[] noticeIds);

    /***
     * 获取人大动态接口
     * @return 人大动态信息
     */
    List<SysNotice> getNews(SysNotice notice);

    /***
     * 获取人大动态接口-所有
     * @return 人大动态信息
     */
    List<SysNotice> getAllNews(SysNotice notice);

    /***
     * 获取人大动态接口-工作通知
     * @return 工作通知信息
     */
    List<SysNotice> getAllWorkNotice(SysNotice notice);
}
