package com.renda.core.service;

import java.util.List;
import com.renda.core.domain.RdRecomDeputy;

/**
 * 建议代Service接口
 *
 * <AUTHOR>
 * @date 2023-11-23
 */
public interface IRdRecomDeputyService
{
    /**
     * 查询建议代
     *
     * @param id 建议代主键
     * @return 建议代
     */
    public RdRecomDeputy selectRdRecomDeputyById(Long id);

    /**
     * 查询建议代列表
     *
     * @param rdRecomDeputy 建议代
     * @return 建议代集合
     */
    public List<RdRecomDeputy> selectRdRecomDeputyList(RdRecomDeputy rdRecomDeputy);

    /**
     * 新增建议代
     *
     * @param rdRecomDeputy 建议代
     * @return 结果
     */
    public int insertRdRecomDeputy(RdRecomDeputy rdRecomDeputy);

    /**
     * 修改建议代
     *
     * @param rdRecomDeputy 建议代
     * @return 结果
     */
    public int updateRdRecomDeputy(RdRecomDeputy rdRecomDeputy);

    /**
     * 批量删除建议代
     *
     * @param ids 需要删除的建议代主键集合
     * @return 结果
     */
    public int deleteRdRecomDeputyByIds(Long[] ids);

    /**
     * 删除建议代信息
     *
     * @param id 建议代主键
     * @return 结果
     */
    public int deleteRdRecomDeputyById(Long id);

    /**
     * 删除建议代表信息
     *
     * @param recomId 建议主键
     * @return 结果
     */
    int deleteRdRecomDeputyByRecomId(Long recomId);

    /**
     * 批量删除代表建议
     *
     * @param ids 需要删除的代表建议主键
     * @return 结果
     */
    int deleteRdRecomDeputyByRecomIds(Long[] ids);

}
