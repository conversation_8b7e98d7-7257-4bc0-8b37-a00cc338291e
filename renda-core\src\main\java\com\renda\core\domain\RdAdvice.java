package com.renda.core.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.renda.common.annotation.Excel;
import com.renda.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 群众建议对象 rd_advice
 *
 * <AUTHOR>
 * @date 2023-10-30
 */
@Data
public class RdAdvice extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 人民代表ID */
    private Long deputyId;

    /** 代表姓名 */
    private String deputyName;

    /** 代表头像 */
    private String deputyAvatar;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 建议内容 */
    private String content;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 电话 */
    @Excel(name = "电话")
    private String phone;

    /** 群众ID */
    private Long massId;

    /** 群众 */
    private String massAvatar;

    /** 附件 */
    private List<RdAdviceAttachment> attachmentList;

    /** 反馈信息 */
    private List<RdFeedback> feedbackList;

    /** 代表单位 */
    private String deputyCompany;

    /** 代表职务 */
    private String deputyDuty;

    /** 代表电话 */
    private String deputyPhone;

    /** 建议类别 */
    @Excel(name = "建议类别")
    private String category;

    /** 建议类别名称 */
    private String categoryName;

    /** 处理状态 (0-待处理, 1-处理中, 2-已处理) */
    @Excel(name = "处理状态", readConverterExp = "0=待处理,1=处理中,2=已处理")
    private String status;

    /** 服务评分 (1-5分) */
    @Excel(name = "服务评分")
    private Integer serviceRating;

    /** 联络站ID (查询参数，不持久化) */
    private Long stationId;

    /** 是否是站代表 (查询参数，不持久化) */
    private String isStationDeputy;

}
