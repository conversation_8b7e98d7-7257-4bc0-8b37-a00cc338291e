<template>
  <div class="app-container bg">
    <ScreenHeader ref="headerComp" />
    <div class="my-screen">
      <ScreenTitle caption="代表履职" more="全部" @clickMore="allJobType" />
      <div class="job-container">
        <div class="job-stat">
          <div :class="item.jobType === jobType ? 'stat-item actived' : 'stat-item'" v-for="(item) in jobStats" @click="selectJobType(item.jobType)">
            <div class="icon"><i :class="item.icon" /></div>
            <div class="title">{{item.title}}</div>
            <div class="desc">{{item.desc}}</div>
            <div class="count">{{item.count}}</div>
          </div>
        </div>
        <div class="job-table">
          <div class="column">
            <el-table
              :data="jobList1"
              v-el-table-infinite-scroll="loadJobList"
              infinite-scroll-distance="5"
              height="1144px"
              :show-header="false"
              @row-click="onJobDetail"
            >
              <el-table-column prop="title" label="标题" align="center">
                <template slot-scope="scope">
                  <div class="job-item">
                    <el-image class="img" :src="selectImage(scope.row.attachments, defaultDblz)"></el-image>
                    <div class="right">
                      <div class="top">
                        <div class="title">{{scope.row.title}}</div>
                        <div class="content">{{scope.row.content}}</div>
                        <div class="type">
                          <dict-tag class="item" :options="dict.type.job_type" :value="scope.row.jobType"/>
                        </div>
                      </div>
                      <div class="footer">
                        <div class="name">{{scope.row.deputyName}}</div>
                        <div class="time">{{formatReadableDate(scope.row.beginDate)}}</div>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <el-alert v-if="isLoadingJobList" title="加载中..." type="success" center :closable="false" show-icon></el-alert>
            <el-alert v-if="noMoreJobList" title="没有更多了" type="warning" center show-icon></el-alert>
          </div>
          <div class="column">
            <el-table
              :data="jobList2"
              v-el-table-infinite-scroll="loadJobList"
              infinite-scroll-distance="5"
              height="1144px"
              :show-header="false"
              @row-click="onJobDetail"
            >
              <el-table-column prop="title" label="标题" align="center">
                <template slot-scope="scope">
                  <div class="job-item">
                    <el-image class="img" :src="selectImage(scope.row.attachments, defaultDblz)"></el-image>
                    <div class="right">
                      <div class="top">
                        <div class="title">{{scope.row.title}}</div>
                        <div class="content">{{scope.row.content}}</div>
                        <div class="type">
                          <dict-tag class="item" :options="dict.type.job_type" :value="scope.row.jobType"/>
                        </div>
                      </div>
                      <div class="footer">
                        <div class="name">{{scope.row.deputyName}}</div>
                        <div class="time">{{formatReadableDate(scope.row.beginDate)}}</div>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <el-alert v-if="isLoadingJobList" title="加载中..." type="success" center :closable="false" show-icon></el-alert>
            <el-alert v-if="noMoreJobList" title="没有更多了" type="warning" center show-icon></el-alert>
          </div>
        </div>
      </div>
    </div>
    <el-image class="return-btn" :src="require('@/assets/images/screen/return.png')" @click="onBack"></el-image>
  </div>
</template>

<script>

import ScreenHeader from './components/screenHeader';
import ScreenTitle from './components/screenTitle';
import ElTableInfiniteScroll from 'el-table-infinite-scroll';
import { formatReadableDate } from '@/api/renda/utils';
import { listJob, getJobStat } from '@/api/renda/screen';

export default {
  name: "JobList",
  components: { ScreenHeader, ScreenTitle },
  dicts: ['yw_mz', 'rd_recom_type', 'rd_session', 'rd_times', 'job_type'],
  directives: {
    "el-table-infinite-scroll": ElTableInfiniteScroll,
  },
  data() {
    return {
      defaultAvatar: require("@/assets/images/avatar.png"), // 默认头像
      defaultDblz: require('@/assets/images/screen/lzgz.png'), // 默认代表履职图片
      baseURL: process.env.VUE_APP_BASE_API, // 图片上传地址头
      jobStats: [], // 履职统计
      jobList1: [], // 履职信息列表
      jobList2: [], // 履职信息列表
      jobPage: 0, // 履职信息页码
      pageSize: 30, // 履职信息每页条数
      lastTimeLoadJobList: new Date().getTime() - 2000, // 上次加载时间
      isLoadingJobList: false,
      noMoreJobList: false,
      jobType: null, // 履职类型
    };
  },
  activated() {
    this.getJobStats();
    this.jobList1 = [];
    this.jobList2 = [];
    this.jobPage = 0;
    this.jobType = null;
    this.loadJobList();
  },
  mounted() {
    this.$nextTick(() => {
      document.oncontextmenu = function() {
        return false;
      };
    });
  },
  methods: {
    getJobStats() {
      getJobStat().then(res => {
        this.jobStats = res.data;
      })
    },
    loadJobList() {
      let nowTime = new Date().getTime()
      let diffTime = ( nowTime - this.lastTimeLoadJobList ) / 1000
      if (diffTime > 0.3) {
        this.isLoadingJobList = true;
        this.jobPage++;
        listJob({
          jobType: this.jobType,
          pageNum: this.jobPage,
          pageSize: this.pageSize,
        }).then(res => {
          this.isLoadingJobList = false;
          if (this.jobPage > Math.floor(res.total / this.pageSize) + 1 ) {
            this.jobPage = Math.floor(res.total / this.pageSize) + 1;
            this.noMoreJobList = true;
            setTimeout(()=>{
              this.noMoreJobList = false
            },1000)
          } else {
            let count = Math.floor(res.rows.length / 2);
            if (res.rows.length % 2 === 1) {
              count++;
            }
            this.jobList1 = this.jobList1.concat(res.rows.slice(0, count));
            this.jobList2 = this.jobList2.concat(res.rows.slice(count));
          }
        });
      }
      this.lastTimeLoadJobList = nowTime
    },
    selectImage(attachmentList, defaultImage) {
      if (!attachmentList) {
        return defaultImage
      }
      let image = attachmentList.find(item => item.fileType === 1);
      if (image) {
        return this.baseURL + image.fileUrl
      } else {
        return defaultImage
      }
    },
    formatReadableDate(date) {
      return formatReadableDate(date);
    },
    onJobDetail(row) {
      this.$router.push({ path: "JobDetail", query: { jobId: row.id, deputyId: row.deputyId }});
    },
    onBack() {
      this.$refs.headerComp.onClick();
    },
    selectJobType(jobType) {
      this.jobType = jobType;
      this.jobList1 = [];
      this.jobList2 = [];
      this.jobPage = 0;
      this.loadJobList();
    },
    allJobType() {
      this.selectJobType(null);
    },
  }
};
</script>

<style lang="scss" scoped>

@import '../../../common/font/font.css';

.my-screen {
  margin: 250px 550px 100px 550px;
  padding: 50px 50px;
  height: 1826px;
  display: flex;
  flex-direction: column;

  .job-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 1616px;
    background: #FFF6EF;
    margin-top: 20px;
    padding: 50px;
    overflow: hidden;

    .job-stat {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: space-between;
      .stat-item {
        width: 180px;
        height: 280px;
        margin: 20px 10px;
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: #fdf1f1;
        box-shadow: 0px 0px 12px 0px rgba(0,0,0,0.3);
        border-radius: 12px;
        overflow: hidden;
        background: url("~@/assets/images/screen/cover.png") no-repeat;
        background-size: 100% 100%;
        .icon {
          padding: 10px 0;
          width: 100%;
          //height: 30px;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #fff;
          font-size: 56px;
          background-color: red;
          opacity: 0.8;
        }
        .title {
          margin-top: 20px;
          display: flex;
          align-items: center;
          font-family: AL-B;
          font-size: 36px;
          color: #333;
        }
        .desc {
          //margin-top: 2px;
          display: flex;
          align-items: center;
          font-family: AL-R;
          font-size: 20px;
          line-height: 24px;
          height: 60px;
          flex-shrink: 0;
          color: #333;
          padding: 0 15px;
          text-align: center;
        }
        .count {
          margin-top: 0;
          font-family: AL-B;
          font-size: 48px;
          color: red;
          opacity: 0.9;
        }
      }
      .actived {
        background: none;
      }
    }
    .job-table {
      width: 100%;
      padding: 50px 0 0 0;
      height: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      .column {
        width: 48%;
        height: 100%;
        .job-item {
          width: 100%;
          height: 240px;
          margin: 12px 0;
          padding: 0 12px;
          display: flex;
          flex-direction: row;
          .img {
            display: flex;
            width: 360px;
            height: 240px;
            flex-shrink: 0;
          }
          .right {
            padding: 10px 30px;
            display: flex;
            flex-grow: 1;
            flex-direction: column;
            justify-content: space-between;
            .top {
              display: flex;
              flex-direction: column;
              text-align: left;
              .title {
                font-family: AL-B;
                font-size: 28px;
                line-height: 34px;
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 2;
                display: -webkit-box;
                -webkit-box-orient: vertical;
              }
              .content {
                margin-top: 15px;
                font-family: AL-R;
                font-size: 22px;
                line-height: 30px;
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 3;
                display: -webkit-box;
                -webkit-box-orient: vertical;
              }
              .type {
                margin-top: 10px;
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                .item {
                  font-family: AL-R;
                  font-size: 20px;
                }
              }
            }
            .footer {
              display: flex;
              flex-direction: row;
              justify-content: space-between;
              .name {
                font-family: AL-R;
                font-size: 20px;
              }
              .time {
                font-family: AL-L;
                font-size: 22px;
              }
            }
          }
        }
      }
    }
  }


  .container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background: #FFF6EF;
    margin-top: 17px;
    overflow: hidden;
    .item {
      width: 100%;
      display: flex;
      flex-direction: column;
      padding: 16px 32px;
      .advice-header {
        width: 100%;
        height: 32px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        .left {
          height: 32px;
          display: flex;
          flex-direction: row;
          align-items: center;
          .img {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            border: #fff solid 1px;
            box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.5);
          }
          .name {
            margin-left: 10px;
            font-size: 15px;
            font-family: SY-B;
            font-weight: 300;
            color: #2F2F2F;
          }
        }
        .advice-date {
          display: flex;
          height: 32px;
          align-items: center;
          font-size: 12px;
          font-family: SY-R;
          font-weight: 300;
          color: #999694;
        }
      }
      .station-content {
        width: 100%;
        display: flex;
        flex-direction: row;
        padding: 10px 0;
        align-items: center;
        .station-img {
          width: 160px;
          height: 110px;
          flex-shrink: 0;
        }
        .station-right {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          flex-grow: 1;
          margin-left: 24px;
          text-align: left;
          height: 110px;
          .station-title {
            font-size: 15px;
            font-family: SY-B;
            font-weight: 500;
            line-height: 18px;
            color: #2F2F2F;
          }
          .station-content {
            font-size: 15px;
            font-family: SY-R;
            font-weight: 400;
            line-height: 18px;
            color: #474747;
          }
          .station-footer {
            font-size: 12px;
            font-family: SY-R;
            font-weight: 400;
            color: #717171;
          }
        }
      }
    }
  }
}

</style>
