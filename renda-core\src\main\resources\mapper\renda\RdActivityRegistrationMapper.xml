<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renda.core.mapper.RdActivityRegistrationMapper">

    <resultMap type="RdActivityRegistration" id="RdActivityRegistrationResult">
        <result property="id"    column="id"    />
        <result property="activityId"    column="activity_id"    />
        <result property="deputyId"    column="deputy_id"    />
        <result property="registrationType"    column="registration_type"    />
        <result property="phone"    column="phone"    />
        <result property="smsSendCount"    column="sms_send_count"    />
        <result property="smsSendTime"    column="sms_send_time"    />
        <result property="smsSendStatus"    column="sms_send_status"    />
        <result property="smsMsgNo"    column="sms_msg_no"    />
        <result property="smsMsg"    column="sms_msg"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="deputyName"    column="deputyName"    />
    </resultMap>

    <sql id="selectRdActivityRegistrationVo">
        select id, activity_id, deputy_id, registration_type, phone, sms_send_count, sms_send_time, sms_send_status,
               sms_msg_no, sms_msg, remark, create_time,
               (select name from rd_deputy where id = r.deputy_id) as deputyName
        from rd_activity_registration r
    </sql>

    <select id="selectRdActivityRegistrationList" parameterType="RdActivityRegistration" resultMap="RdActivityRegistrationResult">
        <include refid="selectRdActivityRegistrationVo"/>
        <where>
            <if test="activityId != null "> and activity_id = #{activityId}</if>
            <if test="deputyId != null "> and deputy_id = #{deputyId}</if>
            <if test="registrationType != null  and registrationType != ''"> and registration_type = #{registrationType}</if>
            <if test="remark != null  and remark != ''"> and remark like concat('%', #{remark}, '%')</if>
        </where>
    </select>

    <select id="selectRdActivityRegistrationById" parameterType="Long" resultMap="RdActivityRegistrationResult">
        <include refid="selectRdActivityRegistrationVo"/>
        where id = #{id}
    </select>
    <select id="selectRdActivityRegistrationByDeputyId" parameterType="RdActivityRegistration"
            resultMap="RdActivityRegistrationResult">
        <include refid="selectRdActivityRegistrationVo"/>
        where activity_id = #{activityId}
          and deputy_id = #{deputyId}
    </select>

    <select id="selectRdActivityRegistrationByActivityIdAndDeputyId"
            resultType="com.renda.core.domain.RdActivityRegistration">
        <include refid="selectRdActivityRegistrationVo"/>
        where activity_id = #{activityId} and deputy_id = #{deputyId}
    </select>

    <insert id="insertRdActivityRegistration" parameterType="RdActivityRegistration" useGeneratedKeys="true" keyProperty="id">
        insert into rd_activity_registration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="activityId != null">activity_id,</if>
            <if test="deputyId != null">deputy_id,</if>
            <if test="registrationType != null and registrationType != ''">registration_type,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="smsSendCount != null">sms_send_count,</if>
            <if test="smsSendTime != null">sms_send_time,</if>
            <if test="smsSendStatus != null">sms_send_status,</if>
            <if test="smsMsgNo != null and smsMsgNo != ''">sms_msg_no,</if>
            <if test="smsMsg != null and smsMsg != ''">sms_msg,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="activityId != null">#{activityId},</if>
            <if test="deputyId != null">#{deputyId},</if>
            <if test="registrationType != null and registrationType != ''">#{registrationType},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="smsSendCount != null">#{smsSendCount},</if>
            <if test="smsSendTime != null">#{smsSendTime},</if>
            <if test="smsSendStatus != null">#{smsSendStatus},</if>
            <if test="smsMsgNo != null and smsMsgNo != ''">#{smsMsgNo},</if>
            <if test="smsMsg != null and smsMsg != ''">#{smsMsg},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateRdActivityRegistration" parameterType="RdActivityRegistration">
        update rd_activity_registration
        <trim prefix="SET" suffixOverrides=",">
            <if test="activityId != null">activity_id = #{activityId},</if>
            <if test="deputyId != null">deputy_id = #{deputyId},</if>
            <if test="registrationType != null and registrationType != ''">registration_type = #{registrationType},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="smsSendCount != null">sms_send_count = #{smsSendCount},</if>
            <if test="smsSendTime != null">sms_send_time = #{smsSendTime},</if>
            <if test="smsSendStatus != null">sms_send_status = #{smsSendStatus},</if>
            <if test="smsMsgNo != null and smsMsgNo != ''">sms_msg_no = #{smsMsgNo},</if>
            <if test="smsMsg != null and smsMsg != ''">sms_msg = #{smsMsg},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRdActivityRegistrationById" parameterType="Long">
        delete from rd_activity_registration where id = #{id}
    </delete>

    <delete id="deleteRdActivityRegistrationByIds" parameterType="String">
        delete from rd_activity_registration where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteRdActivityRegistrationByActivityId" parameterType="Long">
        delete from rd_activity_registration where activity_id = #{activityId}
        <if test="registrationType != null  and registrationType != ''"> and registration_type = #{registrationType}</if>
    </delete>

</mapper>
