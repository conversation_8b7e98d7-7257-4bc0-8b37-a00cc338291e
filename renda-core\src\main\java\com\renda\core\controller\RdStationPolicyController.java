package com.renda.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.renda.common.annotation.Log;
import com.renda.common.core.controller.BaseController;
import com.renda.common.core.domain.AjaxResult;
import com.renda.common.enums.BusinessType;
import com.renda.core.domain.RdStationPolicy;
import com.renda.core.service.IRdStationPolicyService;
import com.renda.common.utils.poi.ExcelUtil;
import com.renda.common.core.page.TableDataInfo;

/**
 * 联络站政策Controller
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@RestController
@RequestMapping("/renda/stationpolicy")
public class RdStationPolicyController extends BaseController
{
    @Autowired
    private IRdStationPolicyService rdStationPolicyService;

    /**
     * 查询联络站政策列表
     */
    @PreAuthorize("@ss.hasPermi('renda:stationpolicy:list')")
    @GetMapping("/list")
    public TableDataInfo list(RdStationPolicy rdStationPolicy)
    {
        startPage();
        List<RdStationPolicy> list = rdStationPolicyService.selectRdStationPolicyList(rdStationPolicy);
        return getDataTable(list);
    }

    /**
     * 导出联络站政策列表
     */
    @PreAuthorize("@ss.hasPermi('renda:stationpolicy:export')")
    @Log(title = "联络站政策", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RdStationPolicy rdStationPolicy)
    {
        List<RdStationPolicy> list = rdStationPolicyService.selectRdStationPolicyList(rdStationPolicy);
        ExcelUtil<RdStationPolicy> util = new ExcelUtil<RdStationPolicy>(RdStationPolicy.class);
        util.exportExcel(response, list, "联络站政策数据");
    }

    /**
     * 获取联络站政策详细信息
     */
    @PreAuthorize("@ss.hasPermi('renda:stationpolicy:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rdStationPolicyService.selectRdStationPolicyById(id));
    }

    /**
     * 新增联络站政策
     */
    @PreAuthorize("@ss.hasPermi('renda:stationpolicy:add')")
    @Log(title = "联络站政策", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RdStationPolicy rdStationPolicy)
    {
        return toAjax(rdStationPolicyService.insertRdStationPolicy(rdStationPolicy));
    }

    /**
     * 修改联络站政策
     */
    @PreAuthorize("@ss.hasPermi('renda:stationpolicy:edit')")
    @Log(title = "联络站政策", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RdStationPolicy rdStationPolicy)
    {
        return toAjax(rdStationPolicyService.updateRdStationPolicy(rdStationPolicy));
    }

    /**
     * 删除联络站政策
     */
    @PreAuthorize("@ss.hasPermi('renda:stationpolicy:remove')")
    @Log(title = "联络站政策", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rdStationPolicyService.deleteRdStationPolicyByIds(ids));
    }
}
