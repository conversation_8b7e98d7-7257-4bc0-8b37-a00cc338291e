package com.renda.core.service;

import com.renda.core.domain.RdFile;

import java.util.List;

/**
 * 制度文件Service接口
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
public interface IRdFileService
{
    /**
     * 查询制度文件
     *
     * @param id 制度文件主键
     * @return 制度文件
     */
    public RdFile selectRdFileById(Long id);

    /**
     * 查询制度文件列表
     *
     * @param erFile 制度文件
     * @return 制度文件集合
     */
    public List<RdFile> selectRdFileList(RdFile erFile);

    /**
     * 新增制度文件
     *
     * @param erFile 制度文件
     * @return 结果
     */
    public int insertRdFile(RdFile erFile);

    /**
     * 修改制度文件
     *
     * @param erFile 制度文件
     * @return 结果
     */
    public int updateRdFile(RdFile erFile);

    /**
     * 批量删除制度文件
     *
     * @param ids 需要删除的制度文件主键集合
     * @return 结果
     */
    public int deleteRdFileByIds(Long[] ids);

    /**
     * 删除制度文件信息
     *
     * @param id 制度文件主键
     * @return 结果
     */
    public int deleteRdFileById(Long id);

    /**
     * 根据文件名删除文件
     *
     * @param filename 文件名
     */
    void deleteRdFileByFilename(String filename);
}
