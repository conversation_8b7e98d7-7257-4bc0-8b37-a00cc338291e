package com.renda.core.mapper;

import java.util.List;
import com.renda.core.domain.RdMass;
import com.renda.core.domain.vo.MassAdviceInfoVO;
import com.renda.core.domain.vo.MassStatisticsInfoVO;

/**
 * 群众管理Mapper接口
 *
 * <AUTHOR>
 * @date 2023-10-30
 */
public interface RdMassMapper
{
    /**
     * 查询群众管理
     *
     * @param id 群众管理主键
     * @return 群众管理
     */
    public RdMass selectRdMassById(Long id);

    /**
     * 查询群众管理列表
     *
     * @param rdMass 群众管理
     * @return 群众管理集合
     */
    public List<RdMass> selectRdMassList(RdMass rdMass);

    /**
     * 新增群众管理
     *
     * @param rdMass 群众管理
     * @return 结果
     */
    public int insertRdMass(RdMass rdMass);

    /**
     * 修改群众管理
     *
     * @param rdMass 群众管理
     * @return 结果
     */
    public int updateRdMass(RdMass rdMass);

    /**
     * 删除群众管理
     *
     * @param id 群众管理主键
     * @return 结果
     */
    public int deleteRdMassById(Long id);

    /**
     * 批量删除群众管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRdMassByIds(Long[] ids);

    /***
     * 根据openid获取群众
     * @param openid
     * @return 群众
     */
    public RdMass selectRdMassByOpenid(String openid);

    /***
     * 获取用户统计信息接口
     * @return
     */
    MassStatisticsInfoVO getMassStatistics(Long userId);

    /***
     * 获取用户建议列表接口
     * @return
     */
    List<MassAdviceInfoVO> getMassAdviceList(Long userId);

}
