<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renda.core.mapper.RdStationMapper">

	<resultMap type="RdStation" id="RdStationResult">
		<id     property="stationId"     column="station_id"     />
		<result property="parentId"   column="parent_id"   />
		<result property="ancestors"  column="ancestors"   />
		<result property="stationName"   column="station_name"   />
		<result property="orderNum"   column="order_num"   />
		<result property="parentName" column="parent_name" />
		<result property="createBy"   column="create_by"   />
		<result property="createTime" column="create_time" />
		<result property="updateBy"   column="update_by"   />
		<result property="updateTime" column="update_time" />
	</resultMap>

	<sql id="selectStationVo">
        select d.station_id, d.parent_id, d.ancestors, d.station_name, d.order_num, d.create_by, d.create_time,
               d.update_by, d.update_time, (select station_name from rd_station where station_id = d.parent_id) parent_name
        from rd_station d
    </sql>

	<select id="selectStationList" parameterType="RdStation" resultMap="RdStationResult">
        <include refid="selectStationVo"/>
        where 1 = 1
		<if test="stationId != null and stationId != -1">
			AND station_id = #{stationId}
		</if>
        <if test="parentId != null and parentId != -1">
			AND parent_id = #{parentId}
		</if>
		<if test="stationName != null and stationName != ''">
			AND station_name like concat('%', #{stationName}, '%')
		</if>
		order by d.parent_id, d.order_num
    </select>

    <select id="selectStationById" parameterType="Long" resultMap="RdStationResult">
		select d.station_id, d.parent_id, d.ancestors, d.station_name, d.order_num,
			(select station_name from rd_station where station_id = d.parent_id) parent_name
		from rd_station d
		where d.station_id = #{stationId}
	</select>

    <select id="checkStationExistDeputy" parameterType="Long" resultType="int">
		select count(1) from rd_deputy where station_id = #{stationId}
	</select>

	<select id="hasChildByStationId" parameterType="Long" resultType="int">
		select count(1) from rd_station where parent_id = #{stationId} limit 1
	</select>

	<select id="selectChildrenStationById" parameterType="Long" resultMap="RdStationResult">
		select * from rd_station where find_in_set(#{stationId}, ancestors)
	</select>

	<select id="selectNormalChildrenStationById" parameterType="Long" resultType="int">
		select count(*) from rd_station where find_in_set(#{stationId}, ancestors)
	</select>

	<select id="checkStationNameUnique" resultMap="RdStationResult">
	    <include refid="selectStationVo"/>
		where station_name=#{stationName} and parent_id = #{parentId} limit 1
	</select>

    <insert id="insertStation" parameterType="RdStation">
 		insert into rd_station(
 			<if test="stationId != null and stationId != 0">station_id,</if>
 			<if test="parentId != null and parentId != 0">parent_id,</if>
 			<if test="stationName != null and stationName != ''">station_name,</if>
 			<if test="ancestors != null and ancestors != ''">ancestors,</if>
 			<if test="orderNum != null">order_num,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
 			create_time
 		)values(
 			<if test="stationId != null and stationId != 0">#{stationId},</if>
 			<if test="parentId != null and parentId != 0">#{parentId},</if>
 			<if test="stationName != null and stationName != ''">#{stationName},</if>
 			<if test="ancestors != null and ancestors != ''">#{ancestors},</if>
 			<if test="orderNum != null">#{orderNum},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
 			sysdate()
 		)
	</insert>

	<update id="updateStation" parameterType="RdStation">
 		update rd_station
 		<set>
 			<if test="parentId != null and parentId != 0">parent_id = #{parentId},</if>
 			<if test="stationName != null and stationName != ''">station_name = #{stationName},</if>
 			<if test="ancestors != null and ancestors != ''">ancestors = #{ancestors},</if>
 			<if test="orderNum != null">order_num = #{orderNum},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			update_time = sysdate()
 		</set>
 		where station_id = #{stationId}
	</update>

	<update id="updateStationChildren" parameterType="java.util.List">
	    update rd_station set ancestors =
	    <foreach collection="stations" item="item" index="index"
	        separator=" " open="case station_id" close="end">
	        when #{item.stationId} then #{item.ancestors}
	    </foreach>
	    where station_id in
	    <foreach collection="stations" item="item" index="index"
	        separator="," open="(" close=")">
	        #{item.stationId}
	    </foreach>
	</update>

	<delete id="deleteStationById" parameterType="Long">
		delete from rd_station where station_id = #{stationId}
	</delete>

</mapper>
