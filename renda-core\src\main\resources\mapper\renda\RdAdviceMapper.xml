<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renda.core.mapper.RdAdviceMapper">

    <resultMap type="RdAdvice" id="RdAdviceResult">
        <result property="id"    column="id"    />
        <result property="deputyId"    column="deputy_id"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="name"    column="name"    />
        <result property="phone"    column="phone"    />
        <result property="massId"    column="mass_id"    />
        <result property="category"    column="category"    />
        <result property="categoryName"    column="categoryName"    />
        <result property="status"    column="status"    />
        <result property="serviceRating"    column="service_rating"    />
        <result property="createTime"    column="create_time"    />
        <collection property="attachmentList" javaType="List" ofType="com.renda.core.domain.RdAdviceAttachment"
                    column="id" select="selectAttachmentById" />
    </resultMap>

    <resultMap id="RdAdviceAttachmentResult" type="RdAdviceAttachment">
        <result property="id"    column="id"    />
        <result property="adviceId"    column="advice_id"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileUrl"    column="file_url"    />
    </resultMap>

    <select id="selectAttachmentById" resultMap="RdAdviceAttachmentResult">
        select id, advice_id, file_type, file_name, file_url
        from rd_advice_attachment
        where advice_id = #{id}
    </select>

    <resultMap id="RdFeedbackAttachmentResult" type="RdFeedbackAttachment">
        <result property="id"    column="id"    />
        <result property="feedbackId"    column="feedback_id"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileUrl"    column="file_url"    />
    </resultMap>

    <select id="selectFeedbackAttachmentById" resultMap="RdFeedbackAttachmentResult">
        select id, feedback_id, file_type, file_name, file_url
        from rd_feedback_attachment
        where feedback_id = #{id}
    </select>

    <resultMap id="RdFeedbackResult" type="RdFeedback">
        <result property="id"    column="id"    />
        <result property="adviceId"    column="advice_id"    />
        <result property="userType"    column="user_type"    />
        <result property="userId"    column="user_id"    />
        <result property="name"    column="name"    />
        <result property="avatar"    column="avatar"    />
        <result property="content"    column="content"    />
        <result property="createTime"    column="create_time"    />
        <collection property="attachmentList" javaType="List" ofType="RdFeedbackAttachment"
                    column="id" select="selectFeedbackAttachmentById" />
    </resultMap>

    <select id="selectFeedbackByAdviceId" resultMap="RdFeedbackResult">
        select id, advice_id, user_type, user_id, name, avatar, content, create_time
          from rd_feedback
        where advice_id = #{id}
    </select>

    <resultMap id="AdviceWithFeedbackResult" type="RdAdvice">
        <result property="id"    column="id"    />
        <result property="deputyId"    column="deputy_id"    />
        <result property="deputyName"    column="deputyName"    />
        <result property="deputyAvatar"    column="deputyAvatar"    />
        <result property="deputyCompany"    column="deputyCompany"    />
        <result property="deputyDuty"    column="deputyDuty"    />
        <result property="deputyPhone"    column="deputyPhone"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="name"    column="name"    />
        <result property="phone"    column="phone"    />
        <result property="massId"    column="mass_id"    />
        <result property="massAvatar"    column="massAvatar"    />
        <result property="category"    column="category"    />
        <result property="categoryName"    column="categoryName"    />
        <result property="status"    column="status"    />
        <result property="serviceRating"    column="service_rating"    />
        <result property="createTime"    column="create_time"    />
        <collection property="attachmentList" javaType="List" ofType="RdAdviceAttachment"
                    column="id" select="selectAttachmentById" />
        <collection property="feedbackList" javaType="List" ofType="RdFeedback"
                    column="id" select="selectFeedbackByAdviceId" />
    </resultMap>

    <sql id="selectRdAdviceVo">
        select a.id, a.deputy_id, a.title, a.content, if(`name`='', '匿名', `name`) as `name`, a.phone, a.mass_id,
               a.category, a.status, a.service_rating, a.create_time,
               (select dict_label from sys_dict_data where dict_type = 'rd_advice_category' and dict_value = a.category) as categoryName,
               (select avatar from rd_mass m where m.id = a.mass_id) as massAvatar,
               (select name from rd_deputy d where d.id = a.deputy_id) as deputyName,
               (select avatar from rd_deputy d where d.id = a.deputy_id) as deputyAvatar,
               (select company from rd_deputy d where d.id = a.deputy_id) as deputyCompany,
               (select duty from rd_deputy d where d.id = a.deputy_id) as deputyDuty,
               (select phone from rd_deputy d where d.id = a.deputy_id) as deputyPhone
        from rd_advice a
    </sql>

    <select id="selectRdAdviceList" parameterType="RdAdvice" resultMap="RdAdviceResult">
        select a.id, a.deputy_id, a.title, a.content, if(a.`name`='', '匿名', a.`name`) as `name`, a.phone, a.mass_id,
               a.category, a.status, a.service_rating, a.create_time,
               (select dict_label from sys_dict_data where dict_type = 'rd_advice_category' and dict_value = a.category) as categoryName,
               (select avatar from rd_mass m where m.id = a.mass_id) as massAvatar,
               (select name from rd_deputy d where d.id = a.deputy_id) as deputyName,
               (select avatar from rd_deputy d where d.id = a.deputy_id) as deputyAvatar,
               (select company from rd_deputy d where d.id = a.deputy_id) as deputyCompany,
               (select duty from rd_deputy d where d.id = a.deputy_id) as deputyDuty,
               (select phone from rd_deputy d where d.id = a.deputy_id) as deputyPhone
        from rd_advice a
        left join rd_deputy dep on dep.id = a.deputy_id
        <where>
            <if test="stationId != null and stationId != 0">
                and (dep.station_id = #{stationId} or
                     exists (select 1 from rd_advice_report ar
                             join rd_deputy d2 on d2.id = ar.deputy_id
                             where ar.advice_id = a.id and d2.station_id = #{stationId})
                )
            </if>
            <if test="deputyId != null  and deputyId != ''"> and a.deputy_id = #{deputyId}</if>
            <if test="title != null  and title != ''"> and a.title like concat('%', #{title}, '%')</if>
            <if test="content != null  and content != ''"> and a.content like concat('%', #{content}, '%')</if>
            <if test="name != null  and name != ''"> and a.name like concat('%', #{name}, '%')</if>
            <if test="phone != null  and phone != ''"> and a.phone like concat('%', #{phone}, '%')</if>
            <if test="category != null  and category != ''"> and a.category = #{category}</if>
            <if test="status != null  and status != ''"> and a.status = #{status}</if>
        </where>
        order by a.id desc
    </select>

    <select id="selectRdAdviceWithFeedbackList" parameterType="RdAdvice" resultMap="AdviceWithFeedbackResult">
        select a.id, a.deputy_id, a.title, a.content, a.name, a.phone, a.mass_id,
               a.category, a.status, a.service_rating, a.create_time,
            (select dict_label from sys_dict_data where dict_type = 'rd_advice_category' and dict_value = a.category) as categoryName,
            (select avatar from rd_mass m where m.id = a.mass_id) as massAvatar,
            (select name from rd_deputy d where d.id = a.deputy_id) as deputyName,
            (select avatar from rd_deputy d where d.id = a.deputy_id) as deputyAvatar,
            f.id as feedbackId, f.advice_id, f.user_type, f.user_id, f.name as userName, f.avatar, f.content as feedbackContent, f.create_time as createTime
        from rd_advice a left join rd_feedback f on f.advice_id = a.id
        <where>
            <if test="deputyId != null  and deputyId != ''"> and deputy_id = #{deputyId}</if>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="content != null  and content != ''"> and content like concat('%', #{content}, '%')</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone like concat('%', #{phone}, '%')</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by a.id desc
    </select>

    <select id="selectAdviceExtList" resultType="com.renda.core.domain.vo.AdviceExtVO">
        select a.id as adviceId, a.deputy_id as deputyId, a.title, a.content, a.name, a.phone, a.mass_id as massId,
               a.category, a.status, a.service_rating as serviceRating, a.create_time as createTime,
               (select dict_label from sys_dict_data where dict_type = 'rd_advice_category' and dict_value = a.category) as categoryName,
               (select avatar from rd_deputy d where d.id = a.deputy_id) as deputyAvatar,
               (select avatar from rd_mass m where m.id = a.mass_id) as massAvatar,
               (select count(1) from rd_feedback f where f.advice_id = a.id and f.user_type = '2' and f.user_id = #{deputyId}) as deputyFeedbackCount
        from rd_advice a
        left join rd_deputy dep on dep.id = a.deputy_id
        <where>
            <if test="isStationDeputy != null">
                and dep.is_station_deputy = #{isStationDeputy}
            </if>
            <if test="deputyId != null  and deputyId != ''">
                and (a.deputy_id = #{deputyId} or
                     exists (select 1 from rd_advice_report f where f.advice_id = a.id and f.deputy_id = #{deputyId})
                )
            </if>
            <if test="category != null and category != ''">
                and a.category = #{category}
            </if>
            <if test="status != null and status != ''">
                and a.status = #{status}
            </if>
            <if test="title != null  and title != ''">
                and ( a.title like concat('%', #{title}, '%')
                or a.content like concat('%', #{title}, '%')
                or a.name like concat('%', #{title}, '%')
                or a.phone like concat('%', #{title}, '%') )
            </if>
        </where>
        order by a.id desc
    </select>

    <select id="selectRdAdviceById" parameterType="Long" resultMap="AdviceWithFeedbackResult">
        <include refid="selectRdAdviceVo"/>
        where id = #{id}
    </select>

    <select id="getTodoInfo" resultType="com.renda.core.domain.vo.TodoInfoVO">
        select (
           select count(1)
           from rd_activity a
           where enabled = '1'
             and `status` = '1'
             and (select count(1) from rd_activity_registration where activity_id = a.id and deputy_id = #{deputyId} and registration_type = '0') > 0
       ) as activityCount,
       ( select count(1)
         from rd_advice v
         where (deputy_id = #{deputyId} or
                exists (select 1 from rd_advice_report f where f.advice_id = v.id and f.deputy_id = #{deputyId})
             )
           and (select count(1) from rd_feedback where advice_id = v.id and user_type = '2' and user_id = #{deputyId}) = 0
       ) as adviceCount
    </select>

    <insert id="insertRdAdvice" parameterType="RdAdvice" useGeneratedKeys="true" keyProperty="id">
        insert into rd_advice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deputyId != null">deputy_id,</if>
            <if test="title != null">title,</if>
            <if test="content != null">content,</if>
            <if test="name != null">name,</if>
            <if test="phone != null">phone,</if>
            <if test="massId != null">mass_id,</if>
            <if test="category != null">category,</if>
            <if test="status != null">status,</if>
            <if test="serviceRating != null">service_rating,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deputyId != null">#{deputyId},</if>
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="name != null">#{name},</if>
            <if test="phone != null">#{phone},</if>
            <if test="massId != null">#{massId},</if>
            <if test="category != null">#{category},</if>
            <if test="status != null">#{status},</if>
            <if test="serviceRating != null">#{serviceRating},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateRdAdvice" parameterType="RdAdvice">
        update rd_advice
        <trim prefix="SET" suffixOverrides=",">
            <if test="deputyId != null">deputy_id = #{deputyId},</if>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="name != null">name = #{name},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="massId != null">mass_id = #{massId},</if>
            <if test="category != null">category = #{category},</if>
            <if test="status != null">status = #{status},</if>
            <if test="serviceRating != null">service_rating = #{serviceRating},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRdAdviceById" parameterType="Long">
        delete from rd_advice where id = #{id}
    </delete>

    <delete id="deleteRdAdviceByIds" parameterType="String">
        delete from rd_advice where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
