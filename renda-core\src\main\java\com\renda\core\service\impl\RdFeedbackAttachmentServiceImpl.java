package com.renda.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.renda.core.mapper.RdFeedbackAttachmentMapper;
import com.renda.core.domain.RdFeedbackAttachment;
import com.renda.core.service.IRdFeedbackAttachmentService;

/**
 * 人大代反馈意见附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-04
 */
@Service
public class RdFeedbackAttachmentServiceImpl implements IRdFeedbackAttachmentService
{
    @Autowired
    private RdFeedbackAttachmentMapper rdFeedbackAttachmentMapper;

    /**
     * 查询人大代反馈意见附件
     *
     * @param id 人大代反馈意见附件主键
     * @return 人大代反馈意见附件
     */
    @Override
    public RdFeedbackAttachment selectRdFeedbackAttachmentById(Long id)
    {
        return rdFeedbackAttachmentMapper.selectRdFeedbackAttachmentById(id);
    }

    /**
     * 查询人大代反馈意见附件列表
     *
     * @param rdFeedbackAttachment 人大代反馈意见附件
     * @return 人大代反馈意见附件
     */
    @Override
    public List<RdFeedbackAttachment> selectRdFeedbackAttachmentList(RdFeedbackAttachment rdFeedbackAttachment)
    {
        return rdFeedbackAttachmentMapper.selectRdFeedbackAttachmentList(rdFeedbackAttachment);
    }

    /**
     * 新增人大代反馈意见附件
     *
     * @param rdFeedbackAttachment 人大代反馈意见附件
     * @return 结果
     */
    @Override
    public int insertRdFeedbackAttachment(RdFeedbackAttachment rdFeedbackAttachment)
    {
        return rdFeedbackAttachmentMapper.insertRdFeedbackAttachment(rdFeedbackAttachment);
    }

    /**
     * 修改人大代反馈意见附件
     *
     * @param rdFeedbackAttachment 人大代反馈意见附件
     * @return 结果
     */
    @Override
    public int updateRdFeedbackAttachment(RdFeedbackAttachment rdFeedbackAttachment)
    {
        return rdFeedbackAttachmentMapper.updateRdFeedbackAttachment(rdFeedbackAttachment);
    }

    /**
     * 批量删除人大代反馈意见附件
     *
     * @param ids 需要删除的人大代反馈意见附件主键
     * @return 结果
     */
    @Override
    public int deleteRdFeedbackAttachmentByIds(Long[] ids)
    {
        return rdFeedbackAttachmentMapper.deleteRdFeedbackAttachmentByIds(ids);
    }

    /**
     * 删除人大代反馈意见附件信息
     *
     * @param id 人大代反馈意见附件主键
     * @return 结果
     */
    @Override
    public int deleteRdFeedbackAttachmentById(Long id)
    {
        return rdFeedbackAttachmentMapper.deleteRdFeedbackAttachmentById(id);
    }
}
