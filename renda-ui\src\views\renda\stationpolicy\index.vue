<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="联络站" prop="stationId">
        <treeselect
          v-model="queryParams.stationId"
          :options="stationOptions"
          :show-count="true"
          placeholder="请选择联络站"
          :searchable="true"
          :clearable="true"
          style="width: 200px;"
        />
      </el-form-item>
      <el-form-item label="政策标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入政策标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="政策摘要" prop="summary">
        <el-input
          v-model="queryParams.summary"
          placeholder="请输入政策摘要"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发布部门" prop="department">
        <el-input
          v-model="queryParams.department"
          placeholder="请输入发布部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发布时间" prop="publishTime">
        <el-date-picker clearable
          v-model="queryParams.publishTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择发布时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="执行时间" prop="implementTime">
        <el-date-picker clearable
          v-model="queryParams.implementTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择执行时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="有效期" prop="validPeriod">
        <el-input
          v-model="queryParams.validPeriod"
          placeholder="请输入有效期"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['renda:stationpolicy:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['renda:stationpolicy:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['renda:stationpolicy:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['renda:stationpolicy:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="stationpolicyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="联络站" align="center" prop="stationId" width="120">
        <template slot-scope="scope">
          <span>{{ getStationName(scope.row.stationId) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="政策标题" align="center" prop="title" />
      <el-table-column label="政策摘要" align="center" prop="summary" />
      <el-table-column label="政策类型" align="center" prop="type" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.rd_policy_type" :value="scope.row.type"/>
        </template>
      </el-table-column>
      <el-table-column label="发布部门" align="center" prop="department" />
      <el-table-column label="发布时间" align="center" prop="publishTime" width="100">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.publishTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="执行时间" align="center" prop="implementTime" width="100">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.implementTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否置顶" align="center" prop="isTop" width="80">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.isTop"
            active-value="1"
            inactive-value="0"
            @change="handleTopChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="浏览次数" align="center" prop="viewCount" width="80" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['renda:stationpolicy:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['renda:stationpolicy:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改联络站政策对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联络站" prop="stationId">
              <treeselect
                v-model="form.stationId"
                :options="stationOptions"
                :show-count="true"
                placeholder="请选择联络站"
                :searchable="true"
                :clearable="true"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="政策类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择政策类型" style="width: 100%">
                <el-option
                  v-for="dict in dict.type.rd_policy_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="政策标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入政策标题" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="政策摘要" prop="summary">
              <el-input 
                v-model="form.summary" 
                type="textarea" 
                :rows="2"
                placeholder="请输入政策摘要"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="政策内容" prop="content">
              <editor v-model="form.content" :min-height="260" :height="260"/>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="发布部门" prop="department">
              <el-input v-model="form.department" placeholder="请输入发布部门" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="有效期" prop="validPeriod">
              <el-input v-model="form.validPeriod" placeholder="请输入有效期" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="发布时间" prop="publishTime">
              <el-date-picker 
                v-model="form.publishTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择发布时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="执行时间" prop="implementTime">
              <el-date-picker 
                v-model="form.implementTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择执行时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="是否置顶" prop="isTop">
              <el-switch
                v-model="form.isTop"
                active-value="1"
                inactive-value="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :label="1">正常</el-radio>
                <el-radio :label="0">停用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="浏览次数" prop="viewCount">
              <el-input-number 
                v-model="form.viewCount" 
                :min="0" 
                style="width: 100%"
                placeholder="浏览次数"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listStationpolicy, getStationpolicy, delStationpolicy, addStationpolicy, updateStationpolicy } from "@/api/renda/stationpolicy";
import { stationTreeSelect } from "@/api/renda/station";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Stationpolicy",
  dicts: ['rd_policy_type'],
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 联络站政策表格数据
      stationpolicyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 站点树选项
      stationOptions: [],
      // 站点名称映射
      stationMap: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        stationId: null,
        title: null,
        summary: null,
        content: null,
        type: null,
        department: null,
        publishTime: null,
        implementTime: null,
        validPeriod: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        stationId: [
          { required: true, message: "联络站不能为空", trigger: "change" }
        ],
        title: [
          { required: true, message: "政策标题不能为空", trigger: "blur" }
        ],
        summary: [
          { required: true, message: "政策摘要不能为空", trigger: "blur" }
        ],
        content: [
          { required: true, message: "政策内容不能为空", trigger: "blur" }
        ],
        type: [
          { required: true, message: "政策类型不能为空", trigger: "change" }
        ],
        department: [
          { required: true, message: "发布部门不能为空", trigger: "blur" }
        ],
        isTop: [
          { required: true, message: "是否置顶不能为空", trigger: "change" }
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ],
      }
    };
  },
  async created() {
    // 先加载站点数据，然后再加载列表数据
    await this.getStationTree();
    this.getList();
  },
  methods: {
    /** 查询联络站政策列表 */
    getList() {
      this.loading = true;
      listStationpolicy(this.queryParams).then(response => {
        this.stationpolicyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询站点树数据 */
    async getStationTree() {
      try {
        const response = await stationTreeSelect();
        this.stationOptions = response.data || [];
        // 构建站点名称映射
        this.buildStationMap(this.stationOptions);
        console.log('站点数据加载完成:', this.stationMap);
      } catch (error) {
        console.error('获取站点数据失败:', error);
        this.stationOptions = [];
      }
    },
    /** 构建站点名称映射 */
    buildStationMap(stations) {
      this.stationMap = {}; // 清空映射
      const buildMap = (nodes) => {
        nodes.forEach(node => {
          this.stationMap[node.id] = node.label;
          if (node.children && node.children.length > 0) {
            buildMap(node.children);
          }
        });
      };
      if (stations && stations.length > 0) {
        buildMap(stations);
      }
    },
    /** 根据站点ID获取站点名称 */
    getStationName(stationId) {
      if (!stationId) return '-';
      const name = this.stationMap[stationId];
      return name || `站点${stationId}`;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        stationId: null,
        title: null,
        summary: null,
        content: null,
        type: null,
        department: null,
        publishTime: null,
        implementTime: null,
        validPeriod: null,
        isTop: "0",
        status: 1,
        viewCount: 0,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加联络站政策";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getStationpolicy(id).then(response => {
        this.form = response.data;
        // 确保数据类型正确
        this.form.isTop = this.form.isTop || "0";
        this.form.status = this.form.status != null ? this.form.status : 1;
        this.form.type = this.form.type != null ? this.form.type : null;
        this.form.viewCount = this.form.viewCount != null ? this.form.viewCount : 0;
        this.open = true;
        this.title = "修改联络站政策";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateStationpolicy(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addStationpolicy(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除联络站政策编号为"' + ids + '"的数据项？').then(function() {
        return delStationpolicy(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('renda/stationpolicy/export', {
        ...this.queryParams
      }, `stationpolicy_${new Date().getTime()}.xlsx`)
    },
    /** 置顶状态切换 */
    handleTopChange(row) {
      let text = row.isTop === "1" ? "置顶" : "取消置顶";
      this.$modal.confirm('确认要"' + text + '""' + row.title + '"政策吗？').then(function() {
        return updateStationpolicy(row);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.isTop = row.isTop === "0" ? "1" : "0";
      });
    },
    /** 状态切换 */
    handleStatusChange(row) {
      let text = row.status === 1 ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.title + '"政策吗？').then(function() {
        return updateStationpolicy(row);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.status = row.status === 0 ? 1 : 0;
      });
    }
  }
};
</script>
