<template>
  <div class="policy-list">
    <app-header :showBackBtn="true" @back="onBack()" />

    <main class="content">
      <!-- 页面标题区 -->
      <div class="page-header">
        <h1 class="page-title">政策列表</h1>
        <p class="page-subtitle">全面了解 · 精准查找 · 权威发布</p>
      </div>

      <!-- 搜索和筛选区 -->
      <section class="search-filter">
        <!-- <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入政策名称或关键词"
            size="large"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <i class="el-icon-search" style="font-size: 32px;"></i>
            </template>
            <template #append>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
            </template>
          </el-input>
        </div> -->

        <div class="filter-tabs">
          <div class="filter-group">
            <span class="filter-label">政策类型：</span>
            <div class="filter-options">
              <span
                v-for="type in policyTypes"
                :key="type.value"
                :class="['filter-tag', { active: activeType === type.value }]"
                @click="setActiveType(type.value)"
              >
                {{ type.label }}
              </span>
            </div>
          </div>

          <div class="filter-group">
            <span class="filter-label">发布时间：</span>
            <div class="filter-options">
              <span
                v-for="time in timeFilters"
                :key="time.value"
                :class="['filter-tag', { active: activeTime === time.value }]"
                @click="setActiveTime(time.value)"
              >
                {{ time.label }}
              </span>
            </div>
          </div>
        </div>
      </section>

      <!-- 政策列表 -->
      <section class="policies-section">
        <div class="list-header">
          <div class="result-info">
            <span class="result-count">共找到 {{ totalCount }} 条政策</span>
            <span class="sort-info">按发布时间排序</span>
          </div>
          <div class="view-mode">
            <span
              :class="['mode-btn', { active: viewMode === 'list' }]"
              @click="setViewMode('list')"
            >
              <i class="el-icon-menu"></i>
              列表
            </span>
            <span
              :class="['mode-btn', { active: viewMode === 'grid' }]"
              @click="setViewMode('grid')"
            >
              <i class="el-icon-s-grid"></i>
              网格
            </span>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <i class="el-icon-loading"></i>
          <span>加载中...</span>
        </div>

        <!-- 列表视图 -->
        <div v-else-if="viewMode === 'list'" class="policies-list">
          <div
            v-for="policy in policyList"
            :key="policy.id"
            class="policy-item"
            @click="viewPolicyDetail(policy)"
          >
            <div class="policy-main">
              <div class="policy-header">
                <div class="policy-tags">
                  <span class="policy-tag" :class="getPolicyTypeClass(policy.type)">{{ dict.type.rd_policy_type[policy.type].label }}</span>
                </div>
                <span class="publish-time">{{ policy.publishTime }}</span>
              </div>
              <h3 class="policy-title">{{ policy.title }}</h3>
              <p class="policy-summary">{{ policy.summary }}</p>
              <div class="policy-meta">
                <span class="department">{{ policy.department }}</span>
                <span class="view-count">
                  <i class="el-icon-view"></i>
                  {{ policy.viewCount }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 网格视图 -->
        <div v-else class="policies-grid">
          <div
            v-for="policy in policyList"
            :key="policy.id"
            class="policy-card"
            @click="viewPolicyDetail(policy)"
          >
            <div class="policy-header">
              <span class="policy-tag" :class="getPolicyTypeClass(policy.type)">{{ dict.type.rd_policy_type[policy.type].label }}</span>
            </div>
            <h3 class="policy-title">{{ policy.title }}</h3>
            <p class="policy-summary">{{ policy.summary }}</p>
            <div class="policy-meta">
              <span class="publish-time">
                <i class="el-icon-time"></i>
                {{ policy.publishTime }}
              </span>
              <span class="view-count">
                <i class="el-icon-view"></i>
                {{ policy.viewCount }}
              </span>
            </div>
          </div>
        </div>

        <!-- 分页控件 -->
        <div class="pagination-container" v-if="!loading && totalCount > 0">
          <button
            class="pagination-btn prev-btn"
            :disabled="currentPage === 1"
            @click="changePage(currentPage - 1)"
          >
            <i class="arrow-left"></i>
            上一页
          </button>

          <div class="page-numbers">
            <button
              class="page-number"
              v-for="page in visiblePages"
              :key="page"
              :class="{ active: page === currentPage }"
              @click="changePage(page)"
            >
              {{ page }}
            </button>
          </div>

          <button
            class="pagination-btn next-btn"
            :disabled="currentPage === totalPages"
            @click="changePage(currentPage + 1)"
          >
            下一页
            <i class="arrow-right"></i>
          </button>
        </div>
      </section>
    </main>

    <!-- 返回顶部按钮 -->
    <div class="back-to-top" v-show="showBackToTop" @click="scrollToTop">
      <i class="el-icon-top"></i>
      <span>返回顶部</span>
    </div>

    <!-- 返回按钮 -->
    <div class="back-button" @click="onBack">
      <span>返回</span>
    </div>
  </div>
</template>

<script>
import AppHeader from '@/components/StationScreen/AppHeader.vue'
import { getPolicyList } from '@/api/renda/stationscreen'

export default {
  name: 'PolicyList',
  components: {
    AppHeader
  },
  dicts: ['rd_policy_type'],
  data() {
    return {
      loading: false,
      searchKeyword: '',
      activeType: 'all',
      activeTime: 'all',
      viewMode: 'grid',
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      policyList: [],
      stationId: '0', // 默认站点ID，实际应用中可以从路由或存储中获取
      showBackToTop: false, // 控制返回顶部按钮显示
      timeFilters: [
        { label: '全部', value: 'all' },
        { label: '近一周', value: 'week' },
        { label: '近一月', value: 'month' },
        { label: '近三月', value: 'quarter' },
        { label: '近一年', value: 'year' }
      ]
    }
  },
  computed: {
    // 使用数据字典生成政策类型选项
    policyTypes() {
      const allOption = { label: '全部', value: 'all' }

      const dictOptions = this.dict.type.rd_policy_type ?
        this.dict.type.rd_policy_type.map(item => ({
          label: item.label,
          value: item.value
        })) : []

      return [allOption, ...dictOptions]
    },
    // 总页数
    totalPages() {
      return Math.ceil(this.totalCount / this.pageSize)
    },
    // 显示的页码
    visiblePages() {
      const pages = []
      const totalPages = this.totalPages
      const currentPage = this.currentPage

      if (totalPages <= 7) {
        // 总页数少于7页，显示所有页码
        for (let i = 1; i <= totalPages; i++) {
          pages.push(i)
        }
      } else {
        // 总页数大于7页，智能显示页码
        if (currentPage <= 4) {
          // 当前页在前4页
          for (let i = 1; i <= 5; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(totalPages)
        } else if (currentPage >= totalPages - 3) {
          // 当前页在后4页
          pages.push(1)
          pages.push('...')
          for (let i = totalPages - 4; i <= totalPages; i++) {
            pages.push(i)
          }
        } else {
          // 当前页在中间
          pages.push(1)
          pages.push('...')
          for (let i = currentPage - 1; i <= currentPage + 1; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(totalPages)
        }
      }

      return pages
    },
    // 本地存储的key
    scrollPositionKey() {
      return `policyListScrollPosition_${this.stationId}`
    },
    // 页面状态存储的key
    pageStateKey() {
      return `policyListPageState_${this.stationId}`
    }
  },
  mounted() {
    // 获取联络站ID
    this.stationId = this.$route.query.stationId

    // 检查是否是从详情页返回的
    const isFromDetail = sessionStorage.getItem('policyListFromDetail') === 'true'

    // 检查是否是从政策宣传栏直接进入的
    const isFromPublicity = this.$route.query.from === 'publicity' && !isFromDetail

    if (isFromPublicity) {
      // 如果是从政策宣传栏直接进入的，清除之前的缓存，确保从顶部开始
      this.clearPageState()
      this.clearScrollPosition()
    } else {
      // 否则先恢复页面状态
      this.restorePageState()
    }

    // 清除详情页返回标识
    sessionStorage.removeItem('policyListFromDetail')

    // 初始化数据
    this.loadPolicyList().then(() => {
      // 数据加载完成后，如果不是从政策宣传栏直接进入的，才恢复滚动位置
      if (!isFromPublicity) {
        this.restoreScrollPosition()
      }
    })

    // 键盘事件监听
    document.addEventListener('keydown', this.handleKeydown)

    // 添加滚动监听
    const content = document.querySelector('.content')
    if (content) {
      content.addEventListener('scroll', this.handleScroll)
    }

    // 禁用右键菜单
    this.$nextTick(() => {
      document.oncontextmenu = function() {
        return false;
      };
    });
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.handleKeydown)

    // 移除滚动监听
    const content = document.querySelector('.content')
    if (content) {
      content.removeEventListener('scroll', this.handleScroll)
    }
  },
  methods: {
    onBack() {
      this.savePageState()
      this.saveScrollPosition()
      // 清除从详情页返回的标识，因为这是主动返回操作
      sessionStorage.removeItem('policyListFromDetail')
      this.$router.back()
    },

    // 处理键盘事件
    handleKeydown(event) {
      if (event.key === 'Escape') {
        this.goToHome()
      }
    },

    goToHome() {
      // 保存页面状态和滚动位置，以备返回时使用
      this.savePageState()
      this.saveScrollPosition()
      this.$router.push({ path: "StationScreenHome"});
    },

    // 加载政策列表
    async loadPolicyList() {
      try {
        this.loading = true
        const params = {
          stationId: this.stationId,
          type: this.activeType === 'all' ? '' : this.activeType,
          keyword: this.searchKeyword,
          timeFilter: this.activeTime === 'all' ? '' : this.activeTime,
          pageNum: this.currentPage,
          pageSize: this.pageSize
        }

        const response = await getPolicyList(params)
        if (response.code === 200) {
          this.policyList = response.data.list
          this.totalCount = response.data.total
        } else {
          this.$message.error(response.msg || '获取政策列表失败')
          this.policyList = []
          this.totalCount = 0
        }
      } catch (error) {
        console.error('加载政策列表失败:', error)
        this.$message.error('加载政策列表失败')
        this.policyList = []
        this.totalCount = 0
      } finally {
        this.loading = false
      }
      return Promise.resolve()
    },

    handleSearch() {
      this.currentPage = 1
      // 用户主动搜索，清除之前的缓存
      this.clearPageState()
      this.clearScrollPosition()
      this.loadPolicyList()
    },

    setActiveType(type) {
      this.activeType = type
      this.currentPage = 1
      // 用户主动筛选，清除之前的缓存
      this.clearPageState()
      this.clearScrollPosition()
      this.loadPolicyList()
    },

    setActiveTime(time) {
      this.activeTime = time
      this.currentPage = 1
      // 用户主动筛选，清除之前的缓存
      this.clearPageState()
      this.clearScrollPosition()
      this.loadPolicyList()
    },

    viewPolicyDetail(policy) {
      // 在跳转前保存页面状态和滚动位置
      this.savePageState()
      this.saveScrollPosition()
      // 设置标识表明是从列表页跳转到详情页的
      sessionStorage.setItem('policyListFromDetail', 'true')
      this.$router.push({
        name: 'PolicyDetail',
        query: {
          id: policy.id
        }
      })
    },

    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.loadPolicyList()
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.loadPolicyList()
    },

    // 切换页面
    changePage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page
        // 用户主动翻页，清除滚动位置但保留其他状态
        this.clearScrollPosition()
        this.loadPolicyList().then(() => {
          // 翻页后滚动到政策列表第一条
          this.$nextTick(() => {
            this.scrollToPolicyList()
          })
        })
      }
    },

    // 根据政策类型值获取样式类
    getPolicyTypeClass(typeValue) {
      if (!typeValue || !this.dict.type.rd_policy_type) return 'normal'

      const typeItem = this.dict.type.rd_policy_type.find(item => item.value == typeValue)
      if (!typeItem) return 'normal'

      // 根据字典标签映射到CSS类名
      const labelToClass = {
        '重要政策': 'important',
        '民生政策': 'normal',
        '法规条例': 'regulation',
        '发展规划': 'planning'
      }

      return labelToClass[typeItem.label] || 'normal'
    },

    // 保存滚动位置
    saveScrollPosition() {
      const content = document.querySelector('.content')
      if (content) {
        const scrollPosition = {
          scrollTop: content.scrollTop,
          timestamp: Date.now()
        }
        localStorage.setItem(this.scrollPositionKey, JSON.stringify(scrollPosition))
      }
    },

    // 恢复滚动位置
    restoreScrollPosition() {
      this.$nextTick(() => {
        const scrollPosition = localStorage.getItem(this.scrollPositionKey)
        if (scrollPosition) {
          try {
            const scrollPositionObj = JSON.parse(scrollPosition)
            // 检查时间戳，如果超过24小时则不恢复
            const now = Date.now()
            if (now - scrollPositionObj.timestamp < 24 * 60 * 60 * 1000) {
              const content = document.querySelector('.content')
              if (content && scrollPositionObj.scrollTop > 0) {
                setTimeout(() => {
                  content.scrollTop = scrollPositionObj.scrollTop
                }, 100)
              }
            } else {
              // 清除过期的滚动位置
              localStorage.removeItem(this.scrollPositionKey)
            }
          } catch (e) {
            console.error('恢复滚动位置失败:', e)
            localStorage.removeItem(this.scrollPositionKey)
          }
        }
      })
    },

    // 清除滚动位置缓存
    clearScrollPosition() {
      localStorage.removeItem(this.scrollPositionKey)
    },

    // 保存页面状态
    savePageState() {
      const pageState = {
        activeType: this.activeType,
        activeTime: this.activeTime,
        searchKeyword: this.searchKeyword,
        viewMode: this.viewMode,
        currentPage: this.currentPage,
        timestamp: Date.now()
      }
      localStorage.setItem(this.pageStateKey, JSON.stringify(pageState))
    },

    // 恢复页面状态
    restorePageState() {
      const pageState = localStorage.getItem(this.pageStateKey)
      if (pageState) {
        try {
          const pageStateObj = JSON.parse(pageState)
          // 检查时间戳，如果超过24小时则不恢复
          const now = Date.now()
          if (now - pageStateObj.timestamp < 24 * 60 * 60 * 1000) {
            this.activeType = pageStateObj.activeType || 'all'
            this.activeTime = pageStateObj.activeTime || 'all'
            this.searchKeyword = pageStateObj.searchKeyword || ''
            this.viewMode = pageStateObj.viewMode || 'grid'
            this.currentPage = pageStateObj.currentPage || 1
          } else {
            // 清除过期的页面状态
            localStorage.removeItem(this.pageStateKey)
          }
        } catch (e) {
          console.error('恢复页面状态失败:', e)
          localStorage.removeItem(this.pageStateKey)
        }
      }
    },

    // 清除页面状态缓存
    clearPageState() {
      localStorage.removeItem(this.pageStateKey)
    },

    // 设置显示模式
    setViewMode(mode) {
      if (this.viewMode !== mode) {
        this.viewMode = mode
        // 切换显示模式时保存当前状态，但不清除滚动位置
        this.savePageState()
      }
    },

    // 滚动到政策列表第一条
    scrollToPolicyList() {
      const content = document.querySelector('.content')
      if (!content) return

      // 获取政策列表区域
      let policyListElement
      if (this.viewMode === 'list') {
        policyListElement = document.querySelector('.policies-list')
      } else {
        policyListElement = document.querySelector('.policies-grid')
      }

      if (policyListElement) {
        // 计算滚动位置：政策列表顶部相对于content的位置，再往上偏移一些以显示列表标题
        const listHeaderElement = document.querySelector('.list-header')
        let scrollTop = 0

        if (listHeaderElement) {
          const listHeaderRect = listHeaderElement.getBoundingClientRect()
          const contentRect = content.getBoundingClientRect()
          scrollTop = listHeaderRect.top - contentRect.top + content.scrollTop - 40 // 向上偏移40px以显示更多内容
        }

        // 平滑滚动到目标位置
        content.scrollTo({
          top: Math.max(0, scrollTop),
          behavior: 'smooth'
        })

      }
    },

    // 返回顶部
    scrollToTop() {
      const content = document.querySelector('.content')
      if (content) {
        content.scrollTo({
          top: 0,
          behavior: 'smooth'
        })
      }
    },

    // 监听滚动事件
    handleScroll() {
      const content = document.querySelector('.content')
      if (content) {
        this.showBackToTop = content.scrollTop > 500
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.policy-list {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  margin: 0;
  padding: 0;
}

.content {
  flex: 1;
  padding: 120px 180px;
  max-width: 2400px;
  margin: 0 auto;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.page-header {
  text-align: center;
  margin-bottom: 120px;

  .page-title {
    font-size: 96px;
    color: #d71718;
    margin: 0;
    font-weight: 500;
    letter-spacing: 8px;
    font-family: 'AL-BL' !important;
  }

  .page-subtitle {
    font-size: 42px;
    color: #666;
    margin: 40px 0 0;
    letter-spacing: 4px;
    font-family: 'AL-L' !important;
  }
}

.search-filter {
  background: #fff;
  border-radius: 32px;
  padding: 80px;
  margin-bottom: 80px;
  box-shadow: 0 16px 60px rgba(0, 0, 0, 0.08);
  font-family: 'AL-L';

  .search-box {
    margin-bottom: 60px;

    :deep(.el-input) {
      font-size: 32px;

      .el-input__inner {
        height: 100px;
        line-height: 100px;
        font-size: 32px;
        border-radius: 50px;
        border: 3px solid #e9ecef;

        &:focus {
          border-color: #d71718;
        }
      }

      .el-input-group__append {
        .el-button {
          height: 94px;
          padding: 0 50px;
          font-size: 32px;
          background: #d71718;
          border-color: #d71718;
          border-radius: 0 47px 47px 0;
        }
      }
    }
  }

  .filter-tabs {
    .filter-group {
      display: flex;
      align-items: center;
      margin-bottom: 40px;

      &:last-child {
        margin-bottom: 0;
      }

      .filter-label {
        font-size: 36px;
        color: #333;
        font-weight: 600;
        margin-right: 40px;
        min-width: 160px;
      }

      .filter-options {
        display: flex;
        gap: 30px;
        flex-wrap: wrap;

        .filter-tag {
          padding: 16px 32px;
          border-radius: 30px;
          font-size: 28px;
          background: #f8f9fa;
          color: #666;
          cursor: pointer;
          transition: all 0.3s;
          border: 3px solid transparent;

          &:hover {
            background: rgba(215, 23, 24, 0.1);
            color: #d71718;
          }

          &.active {
            background: #d71718;
            color: #fff;
            border-color: #d71718;
          }
        }
      }
    }
  }
}

.policies-section {
  font-family: 'AL-L';
  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 60px;

    .result-info {
      display: flex;
      gap: 60px;

      .result-count {
        font-size: 36px;
        color: #333;
        font-weight: 600;
      }

      .sort-info {
        font-size: 32px;
        color: #666;
      }
    }

    .view-mode {
      display: flex;
      gap: 30px;

      .mode-btn {
        padding: 16px 32px;
        border-radius: 25px;
        font-size: 28px;
        background: #f8f9fa;
        color: #666;
        cursor: pointer;
        transition: all 0.3s;
        display: flex;
        align-items: center;
        gap: 12px;

        &:hover {
          background: rgba(215, 23, 24, 0.1);
          color: #d71718;
        }

        &.active {
          background: #d71718;
          color: #fff;
        }
      }
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 100px;
    font-size: 32px;
    color: #666;

    i {
      margin-right: 20px;
      font-size: 36px;
    }
  }

  .policies-list {
    .policy-item {
      background: #fff;
      border-radius: 28px;
      padding: 60px;
      margin-bottom: 40px;
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.06);
      cursor: pointer;
      transition: all 0.3s ease;
      border: 3px solid transparent;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 16px 60px rgba(0, 0, 0, 0.1);
        border-color: rgba(215, 23, 24, 0.1);
      }

      .policy-main {
        .policy-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 30px;

          .policy-tags {
            display: flex;
            gap: 20px;

            .policy-tag {
              padding: 12px 24px;
              border-radius: 25px;
              font-size: 26px;
              font-weight: 600;

              &.important {
                background: linear-gradient(135deg, #d71718, #b41616);
                color: #fff;
              }

              &.normal {
                background: rgba(52, 152, 219, 0.1);
                color: #3498db;
              }

              &.regulation {
                background: rgba(155, 89, 182, 0.1);
                color: #9b59b6;
              }

              &.planning {
                background: rgba(39, 174, 96, 0.1);
                color: #27ae60;
              }
            }
          }

          .publish-time {
            font-size: 28px;
            color: #999;
          }
        }

        .policy-title {
          font-size: 42px;
          color: #333;
          margin: 0 0 20px;
          font-weight: 500;
          line-height: 1.4;
          font-family: 'AL-BL' !important;
        }

        .policy-summary {
          font-size: 30px;
          color: #666;
          margin: 0 0 30px;
          line-height: 1.6;
        }

        .policy-meta {
          display: flex;
          gap: 40px;

          span {
            font-size: 26px;
            color: #999;
            display: flex;
            align-items: center;
            gap: 8px;

            &.department {
              color: #d71718;
              font-weight: 600;
            }
          }
        }
      }
    }
  }

  .policies-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 60px;

    .policy-card {
      background: #fff;
      border-radius: 28px;
      padding: 60px;
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.06);
      cursor: pointer;
      transition: all 0.3s ease;
      border: 3px solid transparent;

      &:hover {
        transform: translateY(-8px);
        box-shadow: 0 16px 60px rgba(0, 0, 0, 0.1);
        border-color: rgba(215, 23, 24, 0.1);
      }

      .policy-header {
        margin-bottom: 35px;

        .policy-tag {
          padding: 14px 28px;
          border-radius: 25px;
          font-size: 26px;
          font-weight: 600;

          &.important {
            background: linear-gradient(135deg, #d71718, #b41616);
            color: #fff;
          }

          &.normal {
            background: rgba(52, 152, 219, 0.1);
            color: #3498db;
          }

          &.regulation {
            background: rgba(155, 89, 182, 0.1);
            color: #9b59b6;
          }

          &.planning {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
          }
        }
      }

      .policy-title {
        font-size: 36px;
        color: #333;
        margin: 0 0 25px;
        font-weight: 500;
        line-height: 1.4;
        font-family: 'AL-BL' !important;
      }

      .policy-summary {
        font-size: 28px;
        color: #666;
        margin: 0 0 35px;
        line-height: 1.6;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .policy-meta {
        display: flex;
        justify-content: space-between;

        span {
          font-size: 26px;
          color: #999;
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 60px;

    .pagination-btn {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 20px;
      background: rgba(255, 255, 255, 0.9);
      border: 2px solid rgba(215, 23, 24, 0.2);
      border-radius: 25px;
      color: #666;
      font-size: 16px;
      font-weight: 500;
      font-family: 'AL-R';
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover:not(:disabled) {
        background: rgba(215, 23, 24, 0.1);
        border-color: rgba(215, 23, 24, 0.4);
        color: #d71718;
        transform: translateY(-2px);
      }

      &:disabled {
        opacity: 0.4;
        cursor: not-allowed;
        transform: none;
      }

      .arrow-left, .arrow-right {
        width: 0;
        height: 0;
        border-style: solid;
      }

      .arrow-left {
        border-width: 6px 8px 6px 0;
        border-color: transparent currentColor transparent transparent;
      }

      .arrow-right {
        border-width: 6px 0 6px 8px;
        border-color: transparent transparent transparent currentColor;
      }
    }

    .page-numbers {
      display: flex;
      gap: 8px;

      .page-number {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(215, 23, 24, 0.2);
        border-radius: 50%;
        color: #666;
        font-size: 16px;
        font-weight: 500;
        font-family: 'AL-R';
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover:not(.active) {
          background: rgba(215, 23, 24, 0.1);
          border-color: rgba(215, 23, 24, 0.4);
          color: #d71718;
          transform: scale(1.1);
        }

        &.active {
          background: #d71718;
          border-color: #d71718;
          color: #fff;
          box-shadow: 0 4px 15px rgba(215, 23, 24, 0.3);
        }
      }
    }
  }
}

// 返回顶部按钮
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 200px;
  width: 160px;
  height: 60px;
  border-radius: 30px;
  background: linear-gradient(135deg, #d71718, #b41616);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  box-shadow: 0 8px 25px rgba(215, 23, 24, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1000;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(215, 23, 24, 0.4);
  }

  i {
    font-size: 24px;
  }

  span {
    font-size: 16px;
    font-weight: 500;
    font-family: 'AL-L';
  }
}

// 返回按钮
.back-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 80px;
  height: 80px;
  background: rgba(215, 23, 24, 0.9);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(215, 23, 24, 0.3);
  backdrop-filter: blur(10px);
  z-index: 1000;

  &:hover {
    background: rgba(215, 23, 24, 1);
    transform: scale(1.1);
    box-shadow: 0 8px 30px rgba(215, 23, 24, 0.5);
  }

  .back-icon {
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 8px 12px 8px 0;
    border-color: transparent #fff transparent transparent;
    margin-bottom: 4px;
  }

  span {
    color: #fff;
    font-size: 12px;
    font-weight: 500;
    font-family: 'AL-R';
  }
}

// 4K分辨率适配
@media (min-width: 3000px) {
  .policy-list .content .policies-section {
    .pagination-container {
      margin-top: 90px;
      gap: 30px;

      .pagination-btn {
        font-size: 24px;
        padding: 18px 30px;
        border-radius: 35px;
        gap: 12px;

        .arrow-left {
          border-width: 9px 12px 9px 0;
        }

        .arrow-right {
          border-width: 9px 0 9px 12px;
        }
      }

      .page-numbers {
        gap: 12px;

        .page-number {
          width: 60px;
          height: 60px;
          font-size: 24px;
        }
      }
    }
  }

  .back-to-top {
    bottom: 60px;
    right: 650px;
    width: 220px;
    height: 80px;
    border-radius: 40px;
    gap: 15px;

    i {
      font-size: 32px;
    }

    span {
      font-size: 24px;
    }
  }

  .back-button {
    bottom: 60px;
    right: 60px;
    width: 120px;
    height: 120px;

    .back-icon {
      border-width: 12px 18px 12px 0;
      margin-bottom: 6px;
    }

    span {
      font-size: 24px;
    }
  }
}
</style>
