package com.renda.core.service.impl;

import java.util.ArrayList;
import java.util.List;

import com.renda.common.config.RendaConfig;
import com.renda.common.utils.DateUtils;
import com.renda.common.utils.SecurityUtils;
import com.renda.common.utils.bean.BeanUtils;
import com.renda.core.domain.RdAdviceAttachment;
import com.renda.core.domain.vo.*;
import com.renda.core.service.IRdAdviceAttachmentService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.renda.core.mapper.RdAdviceMapper;
import com.renda.core.domain.RdAdvice;
import com.renda.core.service.IRdAdviceService;

/**
 * 群众建议Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-10-30
 */
@Service
public class RdAdviceServiceImpl implements IRdAdviceService
{
    @Autowired
    private RdAdviceMapper rdAdviceMapper;

    @Autowired
    private IRdAdviceAttachmentService adviceAttachmentService;

    /**
     * 查询群众建议
     *
     * @param id 群众建议主键
     * @return 群众建议
     */
    @Override
    public RdAdvice selectRdAdviceById(Long id)
    {
        return rdAdviceMapper.selectRdAdviceById(id);
    }

    /**
     * 查询群众建议列表
     *
     * @param rdAdvice 群众建议
     * @return 群众建议
     */
    @Override
    public List<RdAdvice> selectRdAdviceList(RdAdvice rdAdvice)
    {
        return rdAdviceMapper.selectRdAdviceList(rdAdvice);
    }

    /**
     * 新增群众建议
     *
     * @param rdAdvice 群众建议
     * @return 结果
     */
    @Override
    public int insertRdAdvice(RdAdvice rdAdvice)
    {
        rdAdvice.setCreateTime(DateUtils.getNowDate());
        // 设置默认处理状态为待处理
        if (rdAdvice.getStatus() == null) {
            rdAdvice.setStatus("0");
        }
        return rdAdviceMapper.insertRdAdvice(rdAdvice);
    }

    /**
     * 修改群众建议
     *
     * @param rdAdvice 群众建议
     * @return 结果
     */
    @Override
    public int updateRdAdvice(RdAdvice rdAdvice)
    {
        return rdAdviceMapper.updateRdAdvice(rdAdvice);
    }

    /**
     * 批量删除群众建议
     *
     * @param ids 需要删除的群众建议主键
     * @return 结果
     */
    @Override
    public int deleteRdAdviceByIds(Long[] ids)
    {
        return rdAdviceMapper.deleteRdAdviceByIds(ids);
    }

    /**
     * 删除群众建议信息
     *
     * @param id 群众建议主键
     * @return 结果
     */
    @Override
    public int deleteRdAdviceById(Long id)
    {
        return rdAdviceMapper.deleteRdAdviceById(id);
    }

    /***
     * 提交意见建议
     * @param adviceInfoVO 意见建议
     * @return
     */
    @Override
    public void saveAdvice(AdviceInfoVO adviceInfoVO) {

        // 保存意见建议
        RdAdvice advice = new RdAdvice();
        advice.setDeputyId(adviceInfoVO.getDeputyId());
        advice.setTitle(adviceInfoVO.getTitle());
        advice.setContent(adviceInfoVO.getContent());
        advice.setName(adviceInfoVO.getName());
        advice.setPhone(adviceInfoVO.getPhone());
        advice.setMassId(SecurityUtils.getLoginUser().getUserId());
        advice.setCreateTime(DateUtils.getNowDate());
        // 设置建议类别
        advice.setCategory(adviceInfoVO.getCategory());
        // 设置默认处理状态为待处理
        advice.setStatus("0");
        insertRdAdvice(advice);

        // 保存附件图片
        adviceInfoVO.getImgList().forEach(img->{
            RdAdviceAttachment attachment = new RdAdviceAttachment();
            attachment.setAdviceId(advice.getId());
            attachment.setFileType(1);
            attachment.setFileUrl(img);
            adviceAttachmentService.insertRdAdviceAttachment(attachment);
        });

        // 保存附件文件
        adviceInfoVO.getFileList().forEach(file->{
            RdAdviceAttachment attachment = new RdAdviceAttachment();
            attachment.setAdviceId(advice.getId());
            attachment.setFileType(2);
            attachment.setFileName(file.getFileName());
            attachment.setFileUrl(file.getFileUrl());
            adviceAttachmentService.insertRdAdviceAttachment(attachment);
        });

    }

    /***
     * 获取建议详情
     * @param adviceId 建议ID
     * @return
     */
    @Override
    public AdviceInfoVO selectAdviceInfoById(Long adviceId) {

        // 获取建议信息
        RdAdvice advice = rdAdviceMapper.selectRdAdviceById(adviceId);
        if (ObjectUtils.isEmpty(advice)) {
            return null;
        }
        AdviceInfoVO adviceInfoVO = new AdviceInfoVO();
        BeanUtils.copyBeanProp(adviceInfoVO, advice);
        List<String> imgList = new ArrayList<>();
        adviceInfoVO.setImgList(imgList);
        List<AttachFileInfoVO> fileList = new ArrayList<>();
        adviceInfoVO.setFileList(fileList);
        adviceInfoVO.setStatus(advice.getStatus());
        adviceInfoVO.setServiceRating(advice.getServiceRating());
        adviceInfoVO.setCategory(advice.getCategory());

        // 获取建议附件
        RdAdviceAttachment adviceAttachment = new RdAdviceAttachment();
        adviceAttachment.setAdviceId(adviceId);
        List<RdAdviceAttachment> adviceAttachments = adviceAttachmentService.selectRdAdviceAttachmentList(adviceAttachment);
        adviceInfoVO.setAttachs(adviceAttachments);
        adviceAttachments.forEach(attachment -> {
            if (attachment.getFileType() == 1) {
                imgList.add(attachment.getFileUrl());
            } else {
                AttachFileInfoVO attachFileInfoVO = new AttachFileInfoVO();
                attachFileInfoVO.setFileName(attachment.getFileName());
                attachFileInfoVO.setFileUrl(attachment.getFileUrl());
                fileList.add(attachFileInfoVO);
            }
        });

        return adviceInfoVO;

    }

    /***
     * 获取意见建议接口-所有
     * @param advice 意见建议信息
     * @return 意见建议列表
     */
    @Override
    public List<AdviceExtVO> selectAdviceExtList(RdAdvice advice) {
        return rdAdviceMapper.selectAdviceExtList(advice);
    }

    /**
     * 查询群众建议列表
     */
    @Override
    public List<RdAdviceWithFeedback> selectRdAdviceWithFeedbackList(RdAdvice rdAdvice) {
        return rdAdviceMapper.selectRdAdviceWithFeedbackList(rdAdvice);
    }

    /***
     * 获取待办信息接口
     * @return 待办信息
     */
    @Override
    public TodoInfoVO getTodoInfo(Long deputyId) {
        return rdAdviceMapper.getTodoInfo(deputyId);
    }

    @Override
    public void completeAdvice(RdAdvice advice) {
        RdAdvice rdAdvice = rdAdviceMapper.selectRdAdviceById(advice.getId());
        if (ObjectUtils.isEmpty(rdAdvice)) {
            throw new RuntimeException("无此建议！");
        }

        if (rdAdvice.getStatus().equals("2")) {
            throw new RuntimeException("建议已处理！");
        }

        rdAdvice.setStatus("2");
        rdAdvice.setServiceRating(advice.getServiceRating());
        updateRdAdvice(rdAdvice);

    }

}
