import request from '@/utils/request'

// 查询人大代表管理列表
export function listDeputy(query) {
  return request({
    url: '/renda/deputy/list',
    method: 'get',
    params: query
  })
}

// 查询人大代表管理列表
export function listDeputy2(query) {
  return request({
    url: '/renda/deputy/list2',
    method: 'get',
    params: query
  })
}

// 查询人大代表管理详细
export function getDeputy(id) {
  return request({
    url: '/renda/deputy/' + id,
    method: 'get'
  })
}

// 新增人大代表管理
export function addDeputy(data) {
  return request({
    url: '/renda/deputy',
    method: 'post',
    data: data
  })
}

// 修改人大代表管理
export function updateDeputy(data) {
  return request({
    url: '/renda/deputy',
    method: 'put',
    data: data
  })
}

// 删除人大代表管理
export function delDeputy(id) {
  return request({
    url: '/renda/deputy/' + id,
    method: 'delete'
  })
}

// 获取人大代表履职统计信息
export function getDeputyStat(id) {
  return request({
    url: '/renda/deputy/deputyStat/' + id,
    method: 'get'
  })
}

// 批量生成代表二维码
export function genDeputyQrcode(id) {
  return request({
    url: '/renda/deputy/genDeputyQrcode/' + id,
    method: 'post'
  })
}
