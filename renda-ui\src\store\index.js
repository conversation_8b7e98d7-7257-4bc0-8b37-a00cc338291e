import Vue from 'vue'
import Vuex from 'vuex'
import app from './modules/app'
import dict from './modules/dict'
import user from './modules/user'
import tagsView from './modules/tagsView'
import permission from './modules/permission'
import settings from './modules/settings'
import getters from './getters'

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    dict,
    user,
    tagsView,
    permission,
    settings
  },
  getters,
  state: {
    representatives: [
      {
        id: 1,
        name: '张三',
        position: '市人大代表',
        unit: '市高新区管委会',
        phone: '13800138000',
        group: '北片',
        station: '联城站点',
        qrcode: 'qr1.png',
        photo: 'avatar1.jpg',
        resume: '自2015年起担任市人大代表，曾提出20余项有效建议...'
      },
      {
        id: 2,
        name: '阿不都热依木·艾则孜',
        position: '市人大代表、电气设备员',
        unit: '市电力公司',
        phone: '13900139000',
        group: '南片',
        station: '城南站点',
        qrcode: 'qr2.png',
        photo: 'avatar2.jpg',
        resume: '从事电力设备维护工作15年，2018年当选市人大代表...'
      },
      {
        id: 3,
        name: '李四',
        position: '市人大代表、电气设备员',
        unit: '市电力公司',
        phone: '13900139000',
        group: '南片',
        station: '城南站点',
        qrcode: 'qr2.png',
        photo: 'avatar2.jpg',
        resume: '从事电力设备维护工作15年，2018年当选市人大代表...'
      },
      // 更多代表数据...
    ],
    policies: [
      {
        id: 1,
        title: '专题学习二十届会精神',
        date: '2024-12-12',
        content: '为深入学习贯彻党的二十大精神，切实把思想和行动统一到党中央决策部署上来...',
        type: '政策宣传'
      },
      {
        id: 2,
        title: '组织学习《国家湿地公园保护条例》、《老年生活环境保护条例》等法规',
        date: '2024-03-13',
        content: '为提高全民环保意识，依法保护湿地资源和改善老年人生活环境...',
        type: '政策宣传'
      },
      // 更多政策数据...
    ],
    statistics: {
      receivedSuggestions: 150,
      processedSuggestions: 120,
      meetingAttendance: 95,
      learningActivities: 32
    }
  },
})

export default store
