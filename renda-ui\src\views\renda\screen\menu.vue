<template>
  <div class="app-container bg">
    <ScreenHeader />
    <div class="menu-large" @click="onDeputy">
      <div class="title">代表风采</div>
      <div class="board"></div>
      <div class="pinyin">Dai Biao <PERSON></div>
      <el-image class="logo" :src="require('@/assets/images/screen/dbfc.png')"></el-image>
    </div>
    <div class="menu-small menu1" @click="onDeputyJob">
      <div class="title">代表履职</div>
      <div class="pinyin">Dai Biao Lv Zhi</div>
      <el-image class="logo" :src="require('@/assets/images/screen/dblz.png')"></el-image>
    </div>
    <div class="menu-small menu2" @click="onDeputyRecom">
      <div class="title">代表建议</div>
      <div class="pinyin">Dai Biao <PERSON></div>
      <el-image class="logo" :src="require('@/assets/images/screen/dbjy.png')"></el-image>
    </div>
    <div class="menu-small menu3" @click="onDeputyNews">
      <div class="title">代表动态</div>
      <div class="pinyin">Dai Biao Dong Tai</div>
      <el-image class="logo" :src="require('@/assets/images/screen/dbdt.png')"></el-image>
    </div>
    <div class="menu-small menu4" @click="onMassAdvice">
      <div class="title">民意征集</div>
      <div class="pinyin">Min Yi Zheng Ji</div>
      <el-image class="logo" :src="require('@/assets/images/screen/myzj.png')"></el-image>
    </div>
  </div>
</template>

<script>

import ScreenHeader from "./components/screenHeader";

export default {
  name: "ScreenMenu",
  components: { ScreenHeader },
  data() {
    return {
    };
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      document.oncontextmenu = function() {
        return false;
      };
    });
  },
  methods: {
    onDeputy() {
      this.$router.push({ path: 'DeputyGroup' })
    },
    onDeputyJob() {
      this.$router.push({ path: 'JobList' })
    },
    onDeputyRecom() {
      this.$router.push({ path: 'RecomList' })
    },
    onDeputyNews() {
      this.$router.push({ path: 'News' })
    },
    onMassAdvice() {
      this.$router.push({ path: 'AdviceList' })
    },
  }
};
</script>

<style lang="scss" scoped>

@import '../../../common/font/font.css';

.menu-large {
  position: relative;
  width: 1000px;
  height: 1500px;
  top: 440px;
  left: 240px;
  border-radius: 24px;
  //background-color: rgba(240, 64, 64, 0.9);
  box-shadow: 0 2px 12px 2px rgba(255, 255, 255, 0.5);
  background-image: linear-gradient(to bottom, rgba(252, 56, 56, 0.8), rgb(245, 44, 44, 0.9));
  .title {
    position: absolute;
    top: 200px;
    left: 120px;
    font-family: AL-BL;
    font-size: 144px;
    color: #FFF;
    text-shadow: 0px 2px 6px #B07878;
    z-index: 1000;
  }
  .board {
    position: absolute;
    top: 300px;
    left: 80px;
    width: 840px;
    height: 220px;
    background-color: #F0DAB0;
    box-shadow: 0px 2px 12px 0 rgba(0,0,0,0.5);
  }
  .pinyin {
    position: absolute;
    top: 380px;
    left: 120px;
    opacity: 0.4;
    font-family: AL-R;
    font-size: 80px;
    color: #000;
  }
  .logo {
    position: absolute;
    top: 520px;
    left: 50px;
    width: 900px;
    height: 900px;
    opacity: 0.9;
  }
}

.menu-small {
  position: relative;
  width: 1000px;
  height: 700px;
  border-radius: 24px;
  //background-color: rgba(255,246,239,0.9);
  box-shadow: 0px 2px 12px 0px rgba(0,0,0,0.5);
  background-image: linear-gradient(to bottom, rgba(255,246,239,0.9), rgba(250, 233, 221, 1));
  .title {
    position: absolute;
    top: 100px;
    left: 120px;
    font-family: AL-B;
    font-size: 128px;
    color: #F04040;
    text-shadow: 0px 2px 6px #B07878;
  }
  .pinyin {
    position: absolute;
    top: 270px;
    left: 130px;
    opacity: 0.4;
    font-family: AL-R;
    font-size: 60px;
    color: #000;
    z-index: 1000;
  }
  .logo {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 500px;
    height: 500px;
    opacity: 0.9;
  }
}

.menu1 {
  position: absolute;
  top: 460px;
  left: 1440px;
}

.menu2 {
  position: absolute;
  top: 460px;
  left: 2560px;
}

.menu3 {
  position: absolute;
  top: 1260px;
  left: 1440px;
}

.menu4 {
  position: absolute;
  top: 1260px;
  left: 2560px;
}

</style>
