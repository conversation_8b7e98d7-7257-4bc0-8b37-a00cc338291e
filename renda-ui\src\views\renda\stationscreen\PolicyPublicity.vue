<template>
  <div class="policy-publicity">
    <app-header :showBackBtn="true" />

    <main class="content">
      <!-- 页面标题区 -->
      <div class="page-header">
        <h1 class="page-title">政策宣传栏</h1>
        <p class="page-subtitle">权威发布 · 专业解读 · 及时更新</p>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p class="loading-text">正在加载数据...</p>
      </div>

      <!-- 热门政策 -->
      <section class="hot-policies" v-if="!loading">
        <div class="section-header">
          <h2 class="section-title">热门政策</h2>
          <router-link :to="{ path: 'PolicyList', query: { stationId: stationId, from: 'publicity' } }" class="more-link">
            更多
            <i class="el-icon-arrow-right"></i>
          </router-link>
        </div>
        <div v-if="hotPolicies.length === 0" class="empty-state">
          <i class="el-icon-document"></i>
          <p>暂无热门政策数据</p>
        </div>
        <div v-else class="policies-grid">
          <div
            v-for="policy in hotPolicies"
            :key="policy.id"
            class="policy-card"
            @click="viewPolicyDetail(policy)"
          >
            <div class="policy-header">
              <span class="policy-tag" :class="getPolicyTypeClass(policy.type)">{{ dict.type.rd_policy_type[policy.type].label }}</span>
            </div>
            <h3 class="policy-title">{{ policy.title }}</h3>
            <p class="policy-summary">{{ policy.summary }}</p>
            <div class="policy-meta">
              <span class="publish-time">
                <i class="el-icon-time"></i>
                {{ policy.publishTime }}
              </span>
              <span class="view-count">
                <i class="el-icon-view"></i>
                {{ policy.viewCount }}次浏览
              </span>
            </div>
          </div>
        </div>
      </section>

      <!-- 最新动态 -->
      <section class="latest-news" v-if="!loading">
        <div class="section-header">
          <h2 class="section-title">最新动态</h2>
          <router-link :to="{ path: 'NewsList', query: { stationId: stationId, from: 'publicity' } }" class="more-link">
            更多
            <i class="el-icon-arrow-right"></i>
          </router-link>
        </div>
        <div v-if="latestNews.length === 0" class="empty-state">
          <i class="el-icon-news"></i>
          <p>暂无最新动态数据</p>
        </div>
        <div v-else class="news-grid">
          <div
            v-for="news in latestNews"
            :key="news.id"
            class="news-card"
            @click="viewNewsDetail(news)"
          >
            <div class="news-image">
              <img :src="getImageUrl(news.image)" :alt="news.title" />
              <div class="news-date">
                <span class="date-day">{{ news.day }}</span>
                <span class="date-month">{{ news.month }}</span>
              </div>
            </div>
            <div class="news-body">
              <div class="news-header">
                <span class="news-source">{{ dict.type.rd_news_type[news.type].label }}</span>
              </div>
              <h4 class="news-title">{{ news.title }}</h4>
              <p class="news-content">{{ news.summary }}</p>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 右下角返回按钮 -->
    <div class="back-button" @click="goBack">
      <span>返回</span>
    </div>
  </div>
</template>

<script>
import AppHeader from '@/components/StationScreen/AppHeader.vue'
import { getHotPolicies, getLatestNews } from '@/api/renda/stationscreen'

export default {
  name: 'PolicyPublicity',
  components: {
    AppHeader
  },
  data() {
    return {
      stationId: null, // 联络站ID
      hotPolicies: [],
      latestNews: [],
      loading: false
    }
  },
  dicts: ['rd_policy_type', 'rd_news_type'],
  methods: {
    // 处理键盘事件
    handleKeydown(event) {
      // ESC键返回首页
      if (event.key === 'Escape') {
        this.goToHome()
      }
    },

    goToHome() {
      this.$router.push({ path: "StationScreenHome"});
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    viewPolicyDetail(policy) {
      this.$router.push({
        name: 'PolicyDetail',
        query: {
          id: policy.id
        }
      })
    },
    viewNewsDetail(news) {
      this.$router.push({
        name: 'StationNewsDetail',
        query: {
          id: news.id
        }
      })
    },

    // 加载热门政策数据
    async loadHotPolicies() {
      try {
        this.loading = true
        const response = await getHotPolicies(this.stationId || '0')
        if (response.code === 200) {
          this.hotPolicies = response.data || []
        } else {
          console.error('加载热门政策失败:', response.msg)
          this.$message.error('加载热门政策失败：' + response.msg)
        }
      } catch (error) {
        console.error('加载热门政策出错:', error)
        this.$message.error('加载热门政策出错：' + error.message)
      } finally {
        this.loading = false
      }
    },

    // 加载最新动态数据
    async loadLatestNews() {
      try {
        this.loading = true
        const response = await getLatestNews(this.stationId || '0')
        if (response.code === 200) {
          this.latestNews = response.data || []
        } else {
          console.error('加载最新动态失败:', response.msg)
          this.$message.error('加载最新动态失败：' + response.msg)
        }
      } catch (error) {
        console.error('加载最新动态出错:', error)
        this.$message.error('加载最新动态出错：' + error.message)
      } finally {
        this.loading = false
      }
    },

    // 加载所有数据
    async loadData() {
      await Promise.all([
        this.loadHotPolicies(),
        this.loadLatestNews()
      ])
    },

    // 根据政策类型值获取样式类
    getPolicyTypeClass(typeValue) {
      if (!typeValue || !this.dict.type.rd_policy_type) return 'normal'

      const typeItem = this.dict.type.rd_policy_type.find(item => item.value == typeValue)
      if (!typeItem) return 'normal'

      // 根据字典标签映射到CSS类名
      const labelToClass = {
        '重要政策': 'important',
        '民生政策': 'normal',
        '法规条例': 'regulation',
        '发展规划': 'planning'
      }

      return labelToClass[typeItem.label] || 'normal'
    },

    // 获取图片完整URL
    getImageUrl(imagePath) {
      if (!imagePath) return ''

      // 如果是完整URL，直接返回
      if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
        return imagePath
      }

      // 如果是相对路径，拼接基础URL
      return process.env.VUE_APP_BASE_API + imagePath
    }
  },
  mounted() {
    this.$nextTick(() => {
      // 禁用右键菜单
      document.oncontextmenu = function() {
        return false;
      };
    });

    // 获取联络站Id
    this.stationId = this.$route.query.stationId
    console.log('联络站ID:', this.stationId)

    // 加载数据
    this.loadData()

    document.addEventListener('keydown', this.handleKeydown)
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.handleKeydown)
  }
}
</script>

<style lang="scss" scoped>
.policy-publicity {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  margin: 0;
  padding: 0;
}

.content {
  flex: 1;
  padding: 80px 100px;
  max-width: 2400px;
  margin: 0 auto;
  overflow-y: auto;

  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.page-header {
  text-align: center;
  margin-bottom: 120px;

  .page-title {
    font-size: 96px;
    color: #d71718;
    margin: 0;
    font-weight: 500;
    letter-spacing: 8px;
    font-family: 'AL-BL' !important;
  }

  .page-subtitle {
    font-size: 42px;
    color: #666;
    margin: 40px 0 0;
    letter-spacing: 4px;
    font-family: 'AL-L' !important;
  }
}

section {
  margin-bottom: 140px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
}

.section-title {
  font-size: 64px;
  color: #333;
  margin: 0;
  font-weight: 700;
  position: relative;
  padding-left: 32px;
  font-family: 'AL-BL' !important;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 80px;
    background: linear-gradient(135deg, #d71718, #b41616);
    border-radius: 6px;
  }
}

.more-link {
  color: #d71718;
  font-size: 30px;
  font-weight: 600;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 8px;
}

// 政策分类
.policy-categories {
  .categories-grid {
    margin-top: 40px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 50px;

    .category-card {
      background: #fff;
      border-radius: 24px;
      padding: 50px;
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      border: 2px solid transparent;

      &:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        border-color: rgba(215, 23, 24, 0.2);
      }

      .category-icon {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: rgba(215, 23, 24, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #d71718;
        font-size: 64px;
        margin-right: 40px;
        transition: all 0.3s;
      }

      &:hover .category-icon {
        background: rgba(215, 23, 24, 0.15);
        transform: scale(1.05);
      }

      .category-info {
        flex: 1;

        h3 {
          font-size: 42px;
          color: #333;
          margin: 0 0 20px;
          font-weight: 700;
          font-family: 'AL-BL' !important;
        }

        p {
          font-size: 28px;
          color: #666;
          margin: 0 0 20px;
          line-height: 1.5;
        }

        .policy-count {
          font-size: 24px;
          color: #d71718;
          font-weight: 600;
        }
      }

      .category-arrow {
        color: #d71718;
        font-size: 36px;
        opacity: 0.5;
        transition: all 0.3s;
      }

      &:hover .category-arrow {
        opacity: 1;
        transform: translateX(8px);
      }
    }
  }
}

// 热门政策
.hot-policies {
  font-family: 'AL-L';
  .policies-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 50px;

    .policy-card {
      background: #fff;
      border-radius: 20px;
      padding: 50px;
      box-shadow: 0 10px 35px rgba(0, 0, 0, 0.06);
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid transparent;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 50px rgba(0, 0, 0, 0.12);
        border-color: rgba(215, 23, 24, 0.1);
      }

      .policy-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;

        .policy-tag {
          padding: 12px 24px;
          border-radius: 25px;
          font-size: 24px;
          font-weight: 600;

          &.important {
            background: linear-gradient(135deg, #d71718, #b41616);
            color: #fff;
          }

          &.normal {
            background: rgba(52, 152, 219, 0.1);
            color: #3498db;
          }

          &.regulation {
            background: rgba(155, 89, 182, 0.1);
            color: #9b59b6;
          }

          &.planning {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
          }
        }

        .policy-status {
          padding: 12px 24px;
          border-radius: 25px;
          font-size: 24px;
          font-weight: 600;

          &.active {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
          }

          &.draft {
            background: rgba(241, 196, 15, 0.1);
            color: #f1c40f;
          }
        }
      }

      .policy-title {
        font-size: 36px;
        color: #333;
        margin: 0 0 25px;
        font-weight: 500;
        line-height: 1.4;
        font-family: 'AL-BL' !important;
      }

      .policy-summary {
        font-size: 28px;
        color: #666;
        margin: 0 0 30px;
        line-height: 1.6;
      }

      .policy-meta {
        display: flex;
        gap: 50px;

        span {
          font-size: 24px;
          color: #999;
          display: flex;
          align-items: center;
          gap: 8px;

          i {
            font-size: 24px;
          }
        }
      }
    }
  }
}

// 最新动态
.latest-news {
  font-family: 'AL-L';
  .news-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 50px;

    .news-card {
      background: #fff;
      border-radius: 20px;
      overflow: hidden;
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.06);
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid transparent;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
        border-color: rgba(215, 23, 24, 0.1);
      }

      .news-image {
        position: relative;
        height: 400px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
        }

        &:hover img {
          transform: scale(1.05);
        }

        .news-date {
          position: absolute;
          top: 20px;
          right: 20px;
          background: rgba(215, 23, 24, 0.9);
          color: white;
          padding: 12px 16px;
          border-radius: 12px;
          text-align: center;
          backdrop-filter: blur(10px);

          .date-day {
            display: block;
            font-size: 28px;
            font-weight: 700;
            line-height: 1;
            font-family: 'AL-BL' !important;
          }

          .date-month {
            display: block;
            font-size: 16px;
            margin-top: 4px;
            opacity: 0.9;
          }
        }
      }

      .news-body {
        padding: 30px;

        .news-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;

          .news-source {
            color: #d71718;
            font-size: 20px;
            font-weight: 600;
            background: rgba(215, 23, 24, 0.1);
            padding: 6px 12px;
            border-radius: 12px;
          }

          .news-time {
            color: #999;
            font-size: 18px;
          }
        }

        .news-title {
          font-size: 36px !important;
          color: #333;
          margin: 0 0 15px;
          font-weight: 500;
          line-height: 1.4;
          font-family: 'AL-BL' !important;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .news-content {
          font-size: 28px !important;
          color: #666;
          margin: 0;
          line-height: 1.6;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      }
    }
  }
}

// 加载状态样式
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 0;

  .loading-spinner {
    width: 80px;
    height: 80px;
    border: 8px solid rgba(215, 23, 24, 0.1);
    border-left: 8px solid #d71718;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    margin-top: 30px;
    font-size: 32px;
    color: #666;
    font-family: 'AL-L' !important;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120px 0;
  color: #999;

  i {
    font-size: 120px;
    margin-bottom: 30px;
    opacity: 0.5;
  }

  p {
    font-size: 36px;
    margin: 0;
    font-family: 'AL-L' !important;
  }
}

.back-button {
  position: fixed;
  bottom: 40px;
  right: 40px;
  width: 80px;
  height: 80px;
  background: rgba(215, 23, 24, 0.9);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(215, 23, 24, 0.3);
  backdrop-filter: blur(10px);
  z-index: 1000;

  &:hover {
    background: rgba(215, 23, 24, 1);
    transform: scale(1.1);
    box-shadow: 0 8px 30px rgba(215, 23, 24, 0.5);
  }

  .back-icon {
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 8px 12px 8px 0;
    border-color: transparent #fff transparent transparent;
    margin-bottom: 4px;
  }

  span {
    color: #fff;
    font-size: 12px;
    font-weight: 500;
    font-family: 'AL-R';
  }
}

/* 4K分辨率适配 */
@media (min-width: 3000px) {
  .back-button {
    width: 120px;
    height: 120px;
    bottom: 60px;
    right: 60px;

    .back-icon {
      border-width: 12px 18px 12px 0;
      margin-bottom: 6px;
    }

    span {
      font-size: 24px;
    }
  }
}
</style>
