package com.renda.core.service;

import com.renda.common.core.domain.TreeSelect;
import com.renda.common.core.domain.entity.RdStation;
import com.renda.common.core.domain.entity.SysDept;

import java.util.List;

/**
 * 站点管理 服务层
 *
 * <AUTHOR>
 */
public interface IRdStationService
{
    /**
     * 查询站点管理数据
     *
     * @param station 站点信息
     * @return 站点信息集合
     */
    public List<RdStation> selectStationList(RdStation station);

    /**
     * 查询站点树结构信息
     *
     * @param station 站点信息
     * @return 站点树信息集合
     */
    public List<TreeSelect> selectStationTreeList(RdStation station);

    /**
     * 查询站点树结构信息（附各站点人大代表）
     *
     * @param station 站点信息
     * @return 站点树信息集合（附各站点人大代表）
     */
    public List<TreeSelect> selectStationTreeListWithDeputy(RdStation station);

    /**
     * 获取代表团树列表（附各代表团人大代表）
     *
     * @return 代表团树信息集合（附各代表团人大代表）
     */
    public List<TreeSelect> groupTreeSelectWithDeputy();

    /**
     * 构建前端所需要树结构
     *
     * @param stations 站点列表
     * @return 树结构列表
     */
    public List<RdStation> buildStationTree(List<RdStation> stations);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param stations 站点列表
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildStationTreeSelect(List<RdStation> stations);

    /**
     * 根据站点ID查询信息
     *
     * @param stationId 站点ID
     * @return 站点信息
     */
    public RdStation selectStationById(Long stationId);

    /**
     * 根据ID查询所有子站点（正常状态）
     *
     * @param stationId 站点ID
     * @return 子站点数
     */
    public int selectNormalChildrenStationById(Long stationId);

    /**
     * 是否存在站点子节点
     *
     * @param stationId 站点ID
     * @return 结果
     */
    public boolean hasChildByStationId(Long stationId);

    /**
     * 查询站点是否存在用户
     *
     * @param stationId 站点ID
     * @return 结果 true 存在 false 不存在
     */
    public boolean checkStationExistDeputy(Long stationId);

    /**
     * 校验站点名称是否唯一
     *
     * @param station 站点信息
     * @return 结果
     */
    public boolean checkStationNameUnique(RdStation station);

    /**
     * 校验站点是否有数据权限
     *
     * @param stationId 站点id
     */
    public void checkStationDataScope(Long stationId);

    /**
     * 新增保存站点信息
     *
     * @param station 站点信息
     * @return 结果
     */
    public int insertStation(RdStation station);

    /**
     * 修改保存站点信息
     *
     * @param station 站点信息
     * @return 结果
     */
    public int updateStation(RdStation station);

    /**
     * 删除站点管理信息
     *
     * @param stationId 站点ID
     * @return 结果
     */
    public int deleteStationById(Long stationId);

}
