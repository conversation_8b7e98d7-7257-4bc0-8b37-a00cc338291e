package com.renda.core.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.renda.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

@Data
public class DeputyInfoVO {

    /** 人大代表ID */
    private Long id;

    /** 姓名 */
    private String name;

    /** 民族 */
    private String nation;

    /** 单位 */
    private String company;

    /** 联系电话 */
    private String tel;

    /** 职务 */
    private String duty;

    /** 出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date birthday;

    /** 电话 */
    private String phone;

    /** 代表履历 */
    private String resume;

    /** 头像 */
    private String avatar;

    /** 二维码地址 */
    private String qrcodeUrl;

    /** 代表类型：0-代表；1-委员；2-管理； */
    private Integer type;

    /** 代表层级 */
    private String level;

    /** 所属站点 */
    private String stationName;

    /** 是否进站代表 */
    private String isStationDeputy;


}
