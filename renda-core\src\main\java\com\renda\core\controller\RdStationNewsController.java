package com.renda.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.renda.common.annotation.Log;
import com.renda.common.core.controller.BaseController;
import com.renda.common.core.domain.AjaxResult;
import com.renda.common.enums.BusinessType;
import com.renda.core.domain.RdStationNews;
import com.renda.core.service.IRdStationNewsService;
import com.renda.common.utils.poi.ExcelUtil;
import com.renda.common.core.page.TableDataInfo;

/**
 * 联络站动态Controller
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@RestController
@RequestMapping("/renda/stationnews")
public class RdStationNewsController extends BaseController
{
    @Autowired
    private IRdStationNewsService rdStationNewsService;

    /**
     * 查询联络站动态列表
     */
    @PreAuthorize("@ss.hasPermi('renda:stationnews:list')")
    @GetMapping("/list")
    public TableDataInfo list(RdStationNews rdStationNews)
    {
        startPage();
        List<RdStationNews> list = rdStationNewsService.selectRdStationNewsList(rdStationNews);
        return getDataTable(list);
    }

    /**
     * 导出联络站动态列表
     */
    @PreAuthorize("@ss.hasPermi('renda:stationnews:export')")
    @Log(title = "联络站动态", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RdStationNews rdStationNews)
    {
        List<RdStationNews> list = rdStationNewsService.selectRdStationNewsList(rdStationNews);
        ExcelUtil<RdStationNews> util = new ExcelUtil<RdStationNews>(RdStationNews.class);
        util.exportExcel(response, list, "联络站动态数据");
    }

    /**
     * 获取联络站动态详细信息
     */
    @PreAuthorize("@ss.hasPermi('renda:stationnews:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rdStationNewsService.selectRdStationNewsById(id));
    }

    /**
     * 新增联络站动态
     */
    @PreAuthorize("@ss.hasPermi('renda:stationnews:add')")
    @Log(title = "联络站动态", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RdStationNews rdStationNews)
    {
        return toAjax(rdStationNewsService.insertRdStationNews(rdStationNews));
    }

    /**
     * 修改联络站动态
     */
    @PreAuthorize("@ss.hasPermi('renda:stationnews:edit')")
    @Log(title = "联络站动态", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RdStationNews rdStationNews)
    {
        return toAjax(rdStationNewsService.updateRdStationNews(rdStationNews));
    }

    /**
     * 删除联络站动态
     */
    @PreAuthorize("@ss.hasPermi('renda:stationnews:remove')")
    @Log(title = "联络站动态", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rdStationNewsService.deleteRdStationNewsByIds(ids));
    }
}
