<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renda.core.mapper.RdActivityMapper">

    <resultMap type="RdActivity" id="RdActivityResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="activityDate"    column="activity_date"    />
        <result property="address"    column="address"    />
        <result property="publisher"    column="publisher"    />
        <result property="phone"    column="phone"    />
        <result property="content"    column="content"    />
        <result property="other"    column="other"    />
        <result property="registrationMode"    column="registration_mode"    />
        <result property="sendSms"    column="send_sms"    />
        <result property="enabled"    column="enabled"    />
        <result property="status"    column="status"    />
        <result property="total"    column="total"    />
        <result property="registeredCount"    column="registeredCount"    />
        <result property="leaveCount"    column="leaveCount"    />
        <result property="notRegisteredCount"    column="notRegisteredCount"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <collection property="registrationList" javaType="List" ofType="com.renda.core.domain.RdActivityRegistration"
                    column="id" select="selectRegistrationById"  />
    </resultMap>

    <resultMap type="RdActivityRegistration" id="RdActivityRegistrationResult">
        <result property="id"    column="id"    />
        <result property="activityId"    column="activity_id"    />
        <result property="deputyId"    column="deputy_id"    />
        <result property="registrationType"    column="registration_type"    />
        <result property="phone"    column="phone"    />
        <result property="smsSendCount"    column="sms_send_count"    />
        <result property="smsSendTime"    column="sms_send_time"    />
        <result property="smsSendStatus"    column="sms_send_status"    />
        <result property="smsMsgNo"    column="sms_msg_no"    />
        <result property="smsMsg"    column="sms_msg"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="deputyName"    column="deputyName"    />
    </resultMap>

    <sql id="selectRdActivityVo">
        select a.id, a.title, a.activity_date, a.address, a.publisher, a.phone, a.content, a.other, a.registration_mode,
               a.send_sms, a.enabled, a.status, a.create_by, a.create_time, a.update_by, a.update_time,
               (select count(1) from rd_activity_registration where activity_id = a.id) as total,
               (select count(1) from rd_activity_registration where activity_id = a.id and registration_type = '1') as registeredCount,
               (select count(1) from rd_activity_registration where activity_id = a.id and registration_type = '2') as leaveCount,
               (select count(1) from rd_activity_registration where activity_id = a.id and registration_type = '0') as notRegisteredCount
        from rd_activity a
    </sql>

    <sql id="selectRdActivityVoWithMyselfRegistration">
        select a.id, a.title, a.activity_date, a.address, a.publisher, a.phone, a.content, a.other, a.registration_mode,
               a.send_sms, a.enabled, a.status, a.create_by, a.create_time, a.update_by, a.update_time,
               (select count(1) from rd_activity_registration where activity_id = a.id) as total,
               (select count(1) from rd_activity_registration where activity_id = a.id and registration_type = '1') as registeredCount,
               (select count(1) from rd_activity_registration where activity_id = a.id and registration_type = '2') as leaveCount,
               (select count(1) from rd_activity_registration where activity_id = a.id and registration_type = '0') as notRegisteredCount
        from rd_activity a
    </sql>

    <select id="selectRegistrationById" resultMap="RdActivityRegistrationResult">
        select id as registrationId, activity_id, deputy_id, registration_type, phone, sms_send_count, sms_send_time, sms_send_status,
               sms_msg_no, sms_msg, remark, create_time,
               (select name from rd_deputy where id = r.deputy_id) as deputyName
        from rd_activity_registration r
        where activity_id = #{id}
    </select>

    <select id="selectRdActivityList" parameterType="RdActivity" resultMap="RdActivityResult">
        <include refid="selectRdActivityVo"/>
        <where>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="params.beginActivityDate != null and params.beginActivityDate != '' and params.endActivityDate != null and params.endActivityDate != ''"> and activity_date between #{params.beginActivityDate} and #{params.endActivityDate}</if>
            <if test="address != null  and address != ''"> and address like concat('%', #{address}, '%')</if>
            <if test="publisher != null  and publisher != ''"> and publisher like concat('%', #{publisher}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone like concat('%', #{phone}, '%')</if>
            <if test="content != null  and content != ''"> and content like concat('%', #{content}, '%')</if>
            <if test="other != null  and other != ''"> and other like concat('%', #{other}, '%')</if>
            <if test="enabled != null  and enabled != ''"> and enabled = #{enabled}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by a.id desc
    </select>

    <select id="selectRdActivityById" parameterType="Long" resultMap="RdActivityResult">
        <include refid="selectRdActivityVo"/>
        where a.id = #{id}
    </select>

    <select id="getActivities" parameterType="RdActivity" resultMap="RdActivityResult">
        <include refid="selectRdActivityVoWithMyselfRegistration"/>
        <where>
            status = '1'
            and (registration_mode = '0' or
                (registration_mode != '0' and (select count(1) from rd_activity_registration where activity_id = a.id and deputy_id = #{deputyId}) > 0))
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="params.beginActivityDate != null and params.beginActivityDate != '' and params.endActivityDate != null and params.endActivityDate != ''"> and activity_date between #{params.beginActivityDate} and #{params.endActivityDate}</if>
            <if test="address != null  and address != ''"> and address like concat('%', #{address}, '%')</if>
            <if test="publisher != null  and publisher != ''"> and publisher like concat('%', #{publisher}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone like concat('%', #{phone}, '%')</if>
            <if test="content != null  and content != ''"> and content like concat('%', #{content}, '%')</if>
            <if test="other != null  and other != ''"> and other like concat('%', #{other}, '%')</if>
        </where>
        order by a.id desc
        limit 0, 5
    </select>

    <select id="selectActivityList" parameterType="RdActivity" resultMap="RdActivityResult">
        <include refid="selectRdActivityVoWithMyselfRegistration"/>
        <where>
            status = '1'
            and (registration_mode = '0' or
                (registration_mode != '0' and (select count(1) from rd_activity_registration where activity_id = a.id and deputy_id = #{deputyId}) > 0))
            <if test="title != null  and title != ''">
            and ( title like concat('%', #{title}, '%')
                or address like concat('%', #{address}, '%')
                or publisher like concat('%', #{publisher}, '%')
                or phone like concat('%', #{phone}, '%')
                or content like concat('%', #{content}, '%')
                or other like concat('%', #{other}, '%') )
            </if>
        </where>
        order by a.id desc
    </select>

    <select id="getDeputyActivityList" resultMap="RdActivityResult">
        <include refid="selectRdActivityVoWithMyselfRegistration"/>
        where status = 1
            and exists (select 1 from rd_activity_registration where activity_id = a.id and deputy_id = #{userId})
        order by a.id desc
    </select>

    <insert id="insertRdActivity" parameterType="RdActivity" useGeneratedKeys="true" keyProperty="id">
        insert into rd_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="activityDate != null">activity_date,</if>
            <if test="address != null">address,</if>
            <if test="publisher != null">publisher,</if>
            <if test="phone != null">phone,</if>
            <if test="content != null">content,</if>
            <if test="other != null">other,</if>
            <if test="registrationMode != null and registrationMode != ''">registration_mode,</if>
            <if test="sendSms != null and sendSms != ''">send_sms,</if>
            <if test="enabled != null and enabled != ''">enabled,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="activityDate != null">#{activityDate},</if>
            <if test="address != null">#{address},</if>
            <if test="publisher != null">#{publisher},</if>
            <if test="phone != null">#{phone},</if>
            <if test="content != null">#{content},</if>
            <if test="other != null">#{other},</if>
            <if test="registrationMode != null and registrationMode != ''">#{registrationMode},</if>
            <if test="sendSms != null and sendSms != ''">#{sendSms},</if>
            <if test="enabled != null and enabled != ''">#{enabled},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRdActivity" parameterType="RdActivity">
        update rd_activity
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="activityDate != null">activity_date = #{activityDate},</if>
            <if test="address != null">address = #{address},</if>
            <if test="publisher != null">publisher = #{publisher},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="content != null">content = #{content},</if>
            <if test="other != null">other = #{other},</if>
            <if test="registrationMode != null and registrationMode != ''">registration_mode = #{registrationMode},</if>
            <if test="sendSms != null and sendSms != ''">send_sms = #{sendSms},</if>
            <if test="enabled != null and enabled != ''">enabled = #{enabled},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRdActivityById" parameterType="Long">
        delete from rd_activity where id = #{id}
    </delete>

    <delete id="deleteRdActivityByIds" parameterType="String">
        delete from rd_activity where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
