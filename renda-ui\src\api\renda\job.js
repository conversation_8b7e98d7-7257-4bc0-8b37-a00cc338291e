import request from '@/utils/request'

// 查询履职工作列表
export function listJob(query) {
  return request({
    url: '/renda/Job/list',
    method: 'get',
    params: query
  })
}

// 查询履职工作详细
export function getJob(id) {
  return request({
    url: '/renda/Job/' + id,
    method: 'get'
  })
}

// 查询履职工作详细
export function getJobExt(id) {
  return request({
    url: '/renda/Job/Ext/' + id,
    method: 'get'
  })
}

// 新增履职工作
export function addJob(data) {
  return request({
    url: '/renda/Job',
    method: 'post',
    data: data
  })
}

// 修改履职工作
export function updateJob(data) {
  return request({
    url: '/renda/Job',
    method: 'put',
    data: data
  })
}

// 删除履职工作
export function delJob(id) {
  return request({
    url: '/renda/Job/' + id,
    method: 'delete'
  })
}

// 获取代表履职统计信息
export function getJobStat(id) {
  return request({
    url: '/renda/Job/stat/',
    method: 'get'
  })
}
