package com.renda.core.service;

import java.util.List;
import com.renda.core.domain.RdStationStaff;

/**
 * 工作人员管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface IRdStationStaffService 
{
    /**
     * 查询工作人员管理
     * 
     * @param id 工作人员管理主键
     * @return 工作人员管理
     */
    public RdStationStaff selectRdStationStaffById(Long id);

    /**
     * 查询工作人员管理列表
     * 
     * @param rdStationStaff 工作人员管理
     * @return 工作人员管理集合
     */
    public List<RdStationStaff> selectRdStationStaffList(RdStationStaff rdStationStaff);

    /**
     * 新增工作人员管理
     * 
     * @param rdStationStaff 工作人员管理
     * @return 结果
     */
    public int insertRdStationStaff(RdStationStaff rdStationStaff);

    /**
     * 修改工作人员管理
     * 
     * @param rdStationStaff 工作人员管理
     * @return 结果
     */
    public int updateRdStationStaff(RdStationStaff rdStationStaff);

    /**
     * 批量删除工作人员管理
     * 
     * @param ids 需要删除的工作人员管理主键集合
     * @return 结果
     */
    public int deleteRdStationStaffByIds(Long[] ids);

    /**
     * 删除工作人员管理信息
     * 
     * @param id 工作人员管理主键
     * @return 结果
     */
    public int deleteRdStationStaffById(Long id);
}
