package com.renda.core.mapper;

import java.util.List;
import com.renda.core.domain.RdActivityRegistration;
import org.apache.ibatis.annotations.Param;

/**
 * 活动报名Mapper接口
 *
 * <AUTHOR>
 * @date 2023-11-14
 */
public interface RdActivityRegistrationMapper
{
    /**
     * 查询活动报名
     *
     * @param id 活动报名主键
     * @return 活动报名
     */
    public RdActivityRegistration selectRdActivityRegistrationById(Long id);

    /**
     * 查询活动报名列表
     *
     * @param rdActivityRegistration 活动报名
     * @return 活动报名集合
     */
    public List<RdActivityRegistration> selectRdActivityRegistrationList(RdActivityRegistration rdActivityRegistration);

    /**
     * 新增活动报名
     *
     * @param rdActivityRegistration 活动报名
     * @return 结果
     */
    public int insertRdActivityRegistration(RdActivityRegistration rdActivityRegistration);

    /**
     * 修改活动报名
     *
     * @param rdActivityRegistration 活动报名
     * @return 结果
     */
    public int updateRdActivityRegistration(RdActivityRegistration rdActivityRegistration);

    /**
     * 删除活动报名
     *
     * @param id 活动报名主键
     * @return 结果
     */
    public int deleteRdActivityRegistrationById(Long id);

    /**
     * 批量删除活动报名
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRdActivityRegistrationByIds(Long[] ids);

    /***
     * 根据活动id和用户id查询报名信息
     * @param activityRegistration 活动信息
     * @return
     */
    RdActivityRegistration selectRdActivityRegistrationByDeputyId(RdActivityRegistration activityRegistration);

    /***
     * 删除活动报名信息
     * @param activityId
     * @param registrationType
     */
    void deleteRdActivityRegistrationByActivityId(@Param("activityId") Long activityId, @Param("registrationType") String registrationType);

    /***
     * 根据活动id和用户id查询报名信息
     * @param activityId 活动id
     * @param deputyId 用户id
     * @return 活动报名信息
     */
    RdActivityRegistration selectRdActivityRegistrationByActivityIdAndDeputyId(@Param("activityId") Long activityId, @Param("deputyId") Long deputyId);

}
