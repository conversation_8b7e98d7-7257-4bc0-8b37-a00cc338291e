package com.renda.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.renda.common.annotation.Log;
import com.renda.common.core.controller.BaseController;
import com.renda.common.core.domain.AjaxResult;
import com.renda.common.enums.BusinessType;
import com.renda.core.domain.RdNoticeFeedbackAttachment;
import com.renda.core.service.IRdNoticeFeedbackAttachmentService;
import com.renda.common.utils.poi.ExcelUtil;
import com.renda.common.core.page.TableDataInfo;

/**
 * 工作通知反馈意见附件Controller
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
@RestController
@RequestMapping("/renda/noticefeedbackattachment")
public class RdNoticeFeedbackAttachmentController extends BaseController
{
    @Autowired
    private IRdNoticeFeedbackAttachmentService rdNoticeFeedbackAttachmentService;

    /**
     * 查询工作通知反馈意见附件列表
     */
    @PreAuthorize("@ss.hasPermi('renda:noticefeedbackattachment:list')")
    @GetMapping("/list")
    public TableDataInfo list(RdNoticeFeedbackAttachment rdNoticeFeedbackAttachment)
    {
        startPage();
        List<RdNoticeFeedbackAttachment> list = rdNoticeFeedbackAttachmentService.selectRdNoticeFeedbackAttachmentList(rdNoticeFeedbackAttachment);
        return getDataTable(list);
    }

    /**
     * 导出工作通知反馈意见附件列表
     */
    @PreAuthorize("@ss.hasPermi('renda:noticefeedbackattachment:export')")
    @Log(title = "工作通知反馈意见附件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RdNoticeFeedbackAttachment rdNoticeFeedbackAttachment)
    {
        List<RdNoticeFeedbackAttachment> list = rdNoticeFeedbackAttachmentService.selectRdNoticeFeedbackAttachmentList(rdNoticeFeedbackAttachment);
        ExcelUtil<RdNoticeFeedbackAttachment> util = new ExcelUtil<RdNoticeFeedbackAttachment>(RdNoticeFeedbackAttachment.class);
        util.exportExcel(response, list, "工作通知反馈意见附件数据");
    }

    /**
     * 获取工作通知反馈意见附件详细信息
     */
    @PreAuthorize("@ss.hasPermi('renda:noticefeedbackattachment:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rdNoticeFeedbackAttachmentService.selectRdNoticeFeedbackAttachmentById(id));
    }

    /**
     * 新增工作通知反馈意见附件
     */
    @PreAuthorize("@ss.hasPermi('renda:noticefeedbackattachment:add')")
    @Log(title = "工作通知反馈意见附件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RdNoticeFeedbackAttachment rdNoticeFeedbackAttachment)
    {
        return toAjax(rdNoticeFeedbackAttachmentService.insertRdNoticeFeedbackAttachment(rdNoticeFeedbackAttachment));
    }

    /**
     * 修改工作通知反馈意见附件
     */
    @PreAuthorize("@ss.hasPermi('renda:noticefeedbackattachment:edit')")
    @Log(title = "工作通知反馈意见附件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RdNoticeFeedbackAttachment rdNoticeFeedbackAttachment)
    {
        return toAjax(rdNoticeFeedbackAttachmentService.updateRdNoticeFeedbackAttachment(rdNoticeFeedbackAttachment));
    }

    /**
     * 删除工作通知反馈意见附件
     */
    @PreAuthorize("@ss.hasPermi('renda:noticefeedbackattachment:remove')")
    @Log(title = "工作通知反馈意见附件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rdNoticeFeedbackAttachmentService.deleteRdNoticeFeedbackAttachmentByIds(ids));
    }
}
