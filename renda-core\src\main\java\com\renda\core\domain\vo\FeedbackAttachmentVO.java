package com.renda.core.domain.vo;

import com.renda.common.annotation.Excel;
import com.renda.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 人大代反馈意见附件对象 rd_feedback_attachment
 *
 * <AUTHOR>
 * @date 2023-11-04
 */
public class FeedbackAttachmentVO
{

    /** 文件类型：1-图片视频；2-文件 */
    private Integer fileType;

    /** 文件名 */
    private String fileName;

    /** 文件URL */
    private String fileUrl;

    public void setFileType(Integer fileType)
    {
        this.fileType = fileType;
    }
    public Integer getFileType()
    {
        return fileType;
    }

    public void setFileUrl(String fileUrl)
    {
        this.fileUrl = fileUrl;
    }
    public String getFileUrl()
    {
        return fileUrl;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("fileType", getFileType())
            .append("fileUrl", getFileUrl())
            .toString();
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
}
