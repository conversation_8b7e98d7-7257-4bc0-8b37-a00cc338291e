package com.renda.core.service;

import java.util.List;
import com.renda.core.domain.RdFeedbackAttachment;

/**
 * 人大代反馈意见附件Service接口
 * 
 * <AUTHOR>
 * @date 2023-11-04
 */
public interface IRdFeedbackAttachmentService 
{
    /**
     * 查询人大代反馈意见附件
     * 
     * @param id 人大代反馈意见附件主键
     * @return 人大代反馈意见附件
     */
    public RdFeedbackAttachment selectRdFeedbackAttachmentById(Long id);

    /**
     * 查询人大代反馈意见附件列表
     * 
     * @param rdFeedbackAttachment 人大代反馈意见附件
     * @return 人大代反馈意见附件集合
     */
    public List<RdFeedbackAttachment> selectRdFeedbackAttachmentList(RdFeedbackAttachment rdFeedbackAttachment);

    /**
     * 新增人大代反馈意见附件
     * 
     * @param rdFeedbackAttachment 人大代反馈意见附件
     * @return 结果
     */
    public int insertRdFeedbackAttachment(RdFeedbackAttachment rdFeedbackAttachment);

    /**
     * 修改人大代反馈意见附件
     * 
     * @param rdFeedbackAttachment 人大代反馈意见附件
     * @return 结果
     */
    public int updateRdFeedbackAttachment(RdFeedbackAttachment rdFeedbackAttachment);

    /**
     * 批量删除人大代反馈意见附件
     * 
     * @param ids 需要删除的人大代反馈意见附件主键集合
     * @return 结果
     */
    public int deleteRdFeedbackAttachmentByIds(Long[] ids);

    /**
     * 删除人大代反馈意见附件信息
     * 
     * @param id 人大代反馈意见附件主键
     * @return 结果
     */
    public int deleteRdFeedbackAttachmentById(Long id);
}
