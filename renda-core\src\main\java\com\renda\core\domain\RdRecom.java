package com.renda.core.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.renda.common.annotation.Excel;
import com.renda.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 代表建议对象 rd_recom
 *
 * <AUTHOR>
 * @date 2023-11-23
 */
public class RdRecom extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 建议标题 */
    @Excel(name = "建议标题")
    private String title;

    /** 建议类型：0-大会建议；1-闭会建议 */
    @Excel(name = "建议类型：0-大会建议；1-闭会建议")
    private String recomType;

    /** 届 */
    @Excel(name = "届")
    private String session;

    /** 次 */
    @Excel(name = "次")
    private String times;

    /** 主办 */
    private String host;

    /** 协办 */
    private String cohost;

    /** 建议正文 */
    private String recomFileUrl;

    /** 建议代表 */
    private List<RdRecomDeputy> deputyList;

    /** 建议附件 */
    private List<RdRecomAttachment> attachmentList;

    /** 建议代表 */
    private Long deputyId;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getTitle()
    {
        return title;
    }
    public void setRecomType(String recomType)
    {
        this.recomType = recomType;
    }

    public String getRecomType()
    {
        return recomType;
    }
    public void setSession(String session)
    {
        this.session = session;
    }

    public String getSession()
    {
        return session;
    }
    public void setTimes(String times)
    {
        this.times = times;
    }

    public String getTimes()
    {
        return times;
    }
    public void setRecomFileUrl(String recomFileUrl)
    {
        this.recomFileUrl = recomFileUrl;
    }

    public String getRecomFileUrl()
    {
        return recomFileUrl;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("title", getTitle())
            .append("recomType", getRecomType())
            .append("session", getSession())
            .append("times", getTimes())
            .append("recomFileUrl", getRecomFileUrl())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }

    public List<RdRecomDeputy> getDeputyList() {
        return deputyList;
    }

    public void setDeputyList(List<RdRecomDeputy> deputyList) {
        this.deputyList = deputyList;
    }

    public List<RdRecomAttachment> getAttachmentList() {
        return attachmentList;
    }

    public void setAttachmentList(List<RdRecomAttachment> attachmentList) {
        this.attachmentList = attachmentList;
    }

    public Long getDeputyId() {
        return deputyId;
    }

    public void setDeputyId(Long deputyId) {
        this.deputyId = deputyId;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getCohost() {
        return cohost;
    }

    public void setCohost(String cohost) {
        this.cohost = cohost;
    }
}
