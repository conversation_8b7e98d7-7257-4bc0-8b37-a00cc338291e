package com.renda.core.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.renda.common.annotation.Excel;
import com.renda.common.core.domain.BaseEntity;

/**
 * 工作通知反馈意见附件对象 rd_notice_feedback_attachment
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
@Data
public class RdNoticeFeedbackAttachment extends BaseEntity
{

    /** $column.columnComment */
    private Long id;

    /** 反馈ID */
    @Excel(name = "反馈ID")
    private Long feedbackId;

    /** 文件类型：1-图片视频；2-文件 */
    @Excel(name = "文件类型：1-图片视频；2-文件")
    private Integer fileType;

    /** 文件名 */
    @Excel(name = "文件名")
    private String fileName;

    /** 文件URL */
    @Excel(name = "文件URL")
    private String fileUrl;

}
