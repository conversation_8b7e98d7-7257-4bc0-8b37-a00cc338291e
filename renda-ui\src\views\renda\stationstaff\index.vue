<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="联络站" prop="stationId">
        <treeselect
          v-model="queryParams.stationId"
          :options="stationOptions"
          :show-count="true"
          placeholder="请选择联络站"
          :searchable="true"
          :clearable="true"
          style="width: 200px;"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工作单位" prop="unit">
        <el-input
          v-model="queryParams.unit"
          placeholder="请输入工作单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="职务" prop="position">
        <el-input
          v-model="queryParams.position"
          placeholder="请输入职务"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系电话" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入手机号进行搜索"
          clearable
          maxlength="11"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['renda:stationstaff:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['renda:stationstaff:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['renda:stationstaff:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['renda:stationstaff:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="stationstaffList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键ID" align="center" prop="id" />
      <el-table-column label="联络站" align="center" prop="stationName" />
      <el-table-column label="姓名" align="center" prop="name" />
      <el-table-column label="工作单位" align="center" prop="unit" />
      <el-table-column label="职务" align="center" prop="position" />
      <el-table-column label="联系电话" align="center" prop="phone" />
      <el-table-column label="头像" align="center" prop="avatar" width="80">
        <template slot-scope="scope">
          <el-image
            :src="scope.row.avatar ? baseURL + scope.row.avatar : defaultAvatar"
            fit="contain"
            style="width: 50px; height: 50px;"
          />
        </template>
      </el-table-column>
      <el-table-column label="显示顺序" align="center" prop="orderNum" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['renda:stationstaff:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['renda:stationstaff:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改工作人员管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row align="middle" justify="center">
          <el-col :span="16">
            <el-row>
              <el-form-item label="联络站" prop="stationId">
                <treeselect
                  v-model="form.stationId"
                  :options="stationOptions"
                  :show-count="true"
                  placeholder="请选择联络站"
                  style="width: 100%;"
                />
              </el-form-item>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="姓名" prop="name">
                  <el-input v-model="form.name" placeholder="请输入姓名" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话" prop="phone">
                  <el-input v-model="form.phone" placeholder="请输入11位手机号" maxlength="11" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="16">
                <el-form-item label="工作单位" prop="unit">
                  <el-input v-model="form.unit" placeholder="请输入工作单位" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="职务" prop="position">
                  <el-input v-model="form.position" placeholder="请输入职务" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="显示顺序" prop="orderNum">
                  <el-input-number
                    v-model="form.orderNum"
                    :min="0"
                    :max="9999"
                    controls-position="right"
                    placeholder="请输入显示顺序"
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="状态" prop="status">
                  <el-radio-group v-model="form.status">
                    <el-radio
                      v-for="dict in dict.type.sys_normal_disable"
                      :key="dict.value"
                      :label="dict.value"
                    >{{dict.label}}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="avatar" style="text-align: center;">
              <div>
                <div class="user-info-head" @click="editCropper()" style="margin: 0 auto;">
                  <el-image
                    class="avatar"
                    :src="form.avatar ? transURL(form.avatar) : defaultAvatar"
                    fit="cover"
                  ></el-image>
                </div>
                <div style="margin-top: 10px; color: #999; font-size: 12px;">点击上传头像</div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 头像裁剪上传对话框 -->
    <el-dialog :title="title" :visible.sync="avatarSelectorOpen" width="800px" append-to-body @opened="modalOpened" @close="closeDialog">
      <el-row>
        <el-col :xs="24" :md="12" :style="{height: '350px'}">
          <vue-cropper
            ref="cropper"
            :img="options.img"
            :output-size="options.size"
            :output-type="options.outputType"
            :info="true"
            :full="options.full"
            :fixed="options.fixed"
            :fixedNumber="options.fixedNumber"
            :can-move="options.canMove"
            :can-move-box="options.canMoveBox"
            :fixed-box="options.fixedBox"
            :original="options.original"
            :auto-crop="options.autoCrop"
            :auto-crop-width="options.autoCropWidth"
            :auto-crop-height="options.autoCropHeight"
            :center-box="options.centerBox"
            :high="options.high"
            mode="contain"
            :max-img-size="options.max"
            @realTime="realTime"
            v-if="visible"
          />
        </el-col>
        <el-col :xs="24" :md="12" :style="{height: '350px'}">
          <div style="width: 350px; height: 350px">
            <div class="show-preview" :style="{'width': previews.w + 'px', 'height': previews.h + 'px', 'overflow': 'hidden'}">
              <div :style="previews.div">
                <img :src="previews.url" :style="previews.img">
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <br />
      <el-row>
        <el-col :lg="2" :sm="3" :xs="3">
          <el-upload action="#" :http-request="requestUpload" :show-file-list="false" :before-upload="beforeUpload">
            <el-button size="small">
              选择
              <i class="el-icon-upload el-icon--right"></i>
            </el-button>
          </el-upload>
        </el-col>
        <el-col :lg="{span: 1, offset: 2}" :sm="2" :xs="2">
          <el-button icon="el-icon-plus" size="small" @click="changeScale(1)"></el-button>
        </el-col>
        <el-col :lg="{span: 1, offset: 1}" :sm="2" :xs="2">
          <el-button icon="el-icon-minus" size="small" @click="changeScale(-1)"></el-button>
        </el-col>
        <el-col :lg="{span: 1, offset: 1}" :sm="2" :xs="2">
          <el-button icon="el-icon-refresh-left" size="small" @click="rotateLeft()"></el-button>
        </el-col>
        <el-col :lg="{span: 1, offset: 1}" :sm="2" :xs="2">
          <el-button icon="el-icon-refresh-right" size="small" @click="rotateRight()"></el-button>
        </el-col>
        <el-col :lg="{span: 6, offset: 5}" :sm="2" :xs="2">
          <el-button type="primary" size="small" @click="confirmAvatar()">确 定</el-button>
          <el-button type="primary" size="small" @click="cancelAvatar()">取 消</el-button>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import { listStationstaff, getStationstaff, delStationstaff, addStationstaff, updateStationstaff } from "@/api/renda/stationstaff";
import { debounce } from '@/utils'
import { VueCropper } from 'vue-cropper'
import Treeselect from '@riophae/vue-treeselect'
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { stationTreeSelect } from '@/api/renda/station'

export default {
  name: "Stationstaff",
  components: { VueCropper, Treeselect },
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工作人员管理表格数据
      stationstaffList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        stationId: null,
        name: null,
        unit: null,
        position: null,
        phone: null,
        avatar: null,
        orderNum: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        stationId: [
          { required: true, message: "联络站不能为空", trigger: "blur" }
        ],
        name: [
          { required: true, message: "姓名不能为空", trigger: "blur" }
        ],
        unit: [
          { required: true, message: "工作单位不能为空", trigger: "blur" }
        ],
        position: [
          { required: true, message: "职务不能为空", trigger: "blur" }
        ],
        phone: [
          { required: true, message: "联系电话不能为空", trigger: "blur" },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: "请输入正确的11位手机号码",
            trigger: "blur"
          }
        ],
      },
      defaultAvatar: require("@/assets/images/avatar.png"), // 默认头像
      baseURL: process.env.VUE_APP_BASE_API, // 图片上传地址头
      avatarSelectorOpen: false, // 是否显示弹出层
      avatarChanged: false, // 头像是否改变
      visible: false, // 是否显示cropper
      options: {
        img: '', //裁剪图片的地址
        size: 1, //导出图片的质量
        outputType:"png", // 默认生成截图为PNG格式
        full: false, // 是否输出原图比例的截图
        fixed: true, // 固定截图框比例 不允许改变
        fixedNumber: [1, 1.3333], // 截图框的宽高比例  1:1
        canMove: true, // 图片是否可以移动
        canMoveBox: true, // 截图框能否拖动
        fixedBox: false, // 固定截图框大小 不允许改变
        original: false, // 上传图片按照原始比例渲染
        autoCrop: true, // 是否默认生成截图框
        autoCropWidth: 200, // 默认生成截图框宽度
        autoCropHeight: 200,  // 默认生成截图框高度
        centerBox: true, // 截图框是否被限制在图片里面
        high: true, // 是否按照设备的dpr 输出等比例图片
        max: 99999, // 上传的最大图片数量
      },
      previews: {},
      resizeHandler: null,
      stationOptions: undefined, // 站点树选项
    };
  },
  created() {
    this.getList();
    this.getStationTree();
  },
  methods: {
    /** 查询工作人员管理列表 */
    getList() {
      this.loading = true;
      listStationstaff(this.queryParams).then(response => {
        this.stationstaffList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        stationId: null,
        name: null,
        unit: null,
        position: null,
        phone: null,
        avatar: null,
        orderNum: 0,
        status: "0",
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加工作人员管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getStationstaff(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改工作人员管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateStationstaff(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addStationstaff(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除工作人员管理编号为"' + ids + '"的数据项？').then(function() {
        return delStationstaff(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('renda/stationstaff/export', {
        ...this.queryParams
      }, `stationstaff_${new Date().getTime()}.xlsx`)
    },

    // 头像管理
    // 编辑头像
    editCropper() {
      this.options.img = this.form.avatar ? this.baseURL + this.form.avatar : this.defaultAvatar;
      this.avatarSelectorOpen = true;
    },
    // 打开弹出层结束时的回调
    modalOpened() {
      this.visible = true;
      if (!this.resizeHandler) {
        this.resizeHandler = debounce(() => {
          this.refresh()
        }, 100)
      }
      window.addEventListener("resize", this.resizeHandler)
    },
    // 刷新组件
    refresh() {
      this.$refs.cropper.refresh();
    },
    // 覆盖默认的上传行为
    requestUpload() {
    },
    // 向左旋转
    rotateLeft() {
      this.$refs.cropper.rotateLeft();
    },
    // 向右旋转
    rotateRight() {
      this.$refs.cropper.rotateRight();
    },
    // 图片缩放
    changeScale(num) {
      num = num || 1;
      this.$refs.cropper.changeScale(num);
    },
    // 上传预处理
    beforeUpload(file) {
      if (file.type.indexOf("image/") == -1) {
        this.$modal.msgError("文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。");
      } else {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
          this.options.img = reader.result;
        };
      }
    },
    // 确定
    confirmAvatar() {
      this.$refs.cropper.getCropData(data => {
        this.form.avatar = data;
        this.avatarSelectorOpen = false;
      });
    },
    // 取消
    cancelAvatar() {
      this.visible = false;
      this.avatarSelectorOpen = false;
    },
    // 实时预览
    realTime(data) {
      this.previews = data;
    },
    // 关闭窗口
    closeDialog() {
      this.visible = false;
      window.removeEventListener("resize", this.resizeHandler)
    },
    transURL(avatar) {
      if (avatar.startsWith("data:image")) {
        return avatar
      } else {
        return this.baseURL + avatar
      }
    },
    /** 查询站点下拉树结构 */
    getStationTree() {
      stationTreeSelect().then(response => {
        this.stationOptions = response.data;
      });
    },
  }
};
</script>

<style lang="scss" scoped>
.avatar {
  width: 120px;
  height: 160px;
  border-radius: 8px;
  border: 2px solid #e4e7ed;
  transition: border-color 0.3s;
}

.show-preview {
  margin-left: 10px;
}

.user-info-head {
  position: relative;
  display: inline-block;
  height: 160px;
  width: 120px;
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
}

.user-info-head:hover .avatar {
  border-color: #409eff;
}

.user-info-head:hover:after {
  content: '点击上传';
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  color: #fff;
  text-align: center;
  background: rgba(0, 0, 0, 0.6);
  font-size: 14px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  cursor: pointer;
  line-height: 160px;
  border-radius: 8px;
}

// 表单整体布局优化
.el-form {
  .el-form-item {
    margin-bottom: 18px;
  }

  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

// 头像上传区域美化
.user-info-head {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  &:hover {
    box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.3);
  }
}
</style>
