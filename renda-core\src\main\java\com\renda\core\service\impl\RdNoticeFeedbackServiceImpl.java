package com.renda.core.service.impl;

import java.util.List;
import com.renda.common.utils.DateUtils;
import com.renda.common.utils.SecurityUtils;
import com.renda.core.domain.*;
import com.renda.core.domain.vo.FeedbackInfo2VO;
import com.renda.core.service.IRdDeputyService;
import com.renda.core.service.IRdNoticeFeedbackAttachmentService;
import com.renda.system.domain.SysNotice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.renda.core.mapper.RdNoticeFeedbackMapper;
import com.renda.core.service.IRdNoticeFeedbackService;

/**
 * 工作通知反馈Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
@Service
public class RdNoticeFeedbackServiceImpl implements IRdNoticeFeedbackService
{
    @Autowired
    private RdNoticeFeedbackMapper rdNoticeFeedbackMapper;

    @Autowired
    private IRdNoticeFeedbackAttachmentService noticeFeedbackAttachmentService;

    /**
     * 查询工作通知反馈
     *
     * @param id 工作通知反馈主键
     * @return 工作通知反馈
     */
    @Override
    public RdNoticeFeedback selectRdNoticeFeedbackById(Long id)
    {
        return rdNoticeFeedbackMapper.selectRdNoticeFeedbackById(id);
    }

    /**
     * 查询工作通知反馈列表
     *
     * @param rdNoticeFeedback 工作通知反馈
     * @return 工作通知反馈
     */
    @Override
    public List<RdNoticeFeedback> selectRdNoticeFeedbackList(RdNoticeFeedback rdNoticeFeedback)
    {
        return rdNoticeFeedbackMapper.selectRdNoticeFeedbackList(rdNoticeFeedback);
    }

    /**
     * 新增工作通知反馈
     *
     * @param rdNoticeFeedback 工作通知反馈
     * @return 结果
     */
    @Override
    public int insertRdNoticeFeedback(RdNoticeFeedback rdNoticeFeedback)
    {
        rdNoticeFeedback.setCreateTime(DateUtils.getNowDate());
        return rdNoticeFeedbackMapper.insertRdNoticeFeedback(rdNoticeFeedback);
    }

    /**
     * 修改工作通知反馈
     *
     * @param rdNoticeFeedback 工作通知反馈
     * @return 结果
     */
    @Override
    public int updateRdNoticeFeedback(RdNoticeFeedback rdNoticeFeedback)
    {
        return rdNoticeFeedbackMapper.updateRdNoticeFeedback(rdNoticeFeedback);
    }

    /**
     * 批量删除工作通知反馈
     *
     * @param ids 需要删除的工作通知反馈主键
     * @return 结果
     */
    @Override
    public int deleteRdNoticeFeedbackByIds(Long[] ids)
    {
        return rdNoticeFeedbackMapper.deleteRdNoticeFeedbackByIds(ids);
    }

    /**
     * 删除工作通知反馈信息
     *
     * @param id 工作通知反馈主键
     * @return 结果
     */
    @Override
    public int deleteRdNoticeFeedbackById(Long id)
    {
        return rdNoticeFeedbackMapper.deleteRdNoticeFeedbackById(id);
    }

    /***
     * 提交工作通知反馈接口
     * @param feedbackInfo2VO 反馈信息
     * @return 提交结果
     */
    @Override
    public void saveNoticeFeedback(FeedbackInfo2VO feedbackInfo2VO) {
        // 保存反馈信息
        RdNoticeFeedback feedback = new RdNoticeFeedback();
        feedback.setNoticeId(feedbackInfo2VO.getNoticeId());
        feedback.setDeputyId(SecurityUtils.getLoginUser().getUserId());
        feedback.setContent(feedbackInfo2VO.getContent());
        feedback.setCreateTime(DateUtils.getNowDate());
        rdNoticeFeedbackMapper.insertRdNoticeFeedback(feedback);

        // 保存附件图片
        feedbackInfo2VO.getImgList().forEach(img->{
            RdNoticeFeedbackAttachment attachment = new RdNoticeFeedbackAttachment();
            attachment.setFeedbackId(feedback.getId());
            attachment.setFileType(1);
            attachment.setFileUrl(img);
            noticeFeedbackAttachmentService.insertRdNoticeFeedbackAttachment(attachment);
        });

        // 保存附件文件
        feedbackInfo2VO.getFileList().forEach(file->{
            RdNoticeFeedbackAttachment attachment = new RdNoticeFeedbackAttachment();
            attachment.setFeedbackId(feedback.getId());
            attachment.setFileType(2);
            attachment.setFileName(file.getFileName());
            attachment.setFileUrl(file.getFileUrl());
            noticeFeedbackAttachmentService.insertRdNoticeFeedbackAttachment(attachment);
        });
    }

    /***
     * 获取工作通知反馈接口
     * @param notice 工作通知信息
     * @return 反馈信息
     */
    @Override
    public List<RdNoticeFeedback> getNoticeFeedback(SysNotice notice) {
        return rdNoticeFeedbackMapper.getNoticeFeedback(notice);
    }
}
