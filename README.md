一、部署注意事项
1. 服务器安装docker，下载kkfileview镜像 
   docker pull keking/kkfileview
   docker容器端口为8012，kkfile服务端口为8012

2. 配置nginx反向代理，将8888映射到8012端口，并且配置nginx的https证书
   server {
     listen       9000 ssl;
     server_name  rd.juruifeng.cn;
     ssl_certificate rd.juruifeng.cn_bundle.crt;
     ssl_certificate_key rd.juruifeng.cn.key;
     ssl_session_timeout 5m;
     ssl_protocols TLSv1.2 TLSv1.3;
     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
     ssl_prefer_server_ciphers on;
     index index.html;
     root  /root/renda/dist;
     charset utf-8;
     location / {
       root   /root/renda/dist/;
       try_files $uri $uri/ /index.html;
       index  index.html index.htm;
     }

     location /prod-api/ {
       proxy_set_header Host $http_host;
       proxy_set_header X-Real-IP $remote_addr;
       proxy_set_header REMOTE-HOST $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_pass https://************:9443/;
     }
   }

   server {
     listen       8888 ssl;
     server_name  rd.juruifeng.cn;
     ssl_certificate rd.juruifeng.cn_bundle.crt;
     ssl_certificate_key rd.juruifeng.cn.key;
     ssl_session_timeout 5m;
     ssl_protocols TLSv1.2 TLSv1.3;
     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
     ssl_prefer_server_ciphers on;
     location / {
       proxy_pass http://127.0.0.1:8012;
     }
   }

3. kkFileView容器启动参数
   docker run -it -d -p 8012:8012 -e KK_BASE_URL="https://rd.juruifeng.cn:8888" -e KK_OFFICE_PREVIEW_TYPE="pdf" -e KK_OFFICE_PREVIEW_SWITCH_DISABLED="true" keking/kkfileview --name kkviewer
