<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renda.core.mapper.RdStationPolicyMapper">

    <resultMap type="RdStationPolicy" id="RdStationPolicyResult">
        <result property="id"    column="id"    />
        <result property="stationId"    column="station_id"    />
        <result property="title"    column="title"    />
        <result property="summary"    column="summary"    />
        <result property="content"    column="content"    />
        <result property="type"    column="type"    />
        <result property="department"    column="department"    />
        <result property="publishTime"    column="publish_time"    />
        <result property="implementTime"    column="implement_time"    />
        <result property="validPeriod"    column="valid_period"    />
        <result property="isTop"    column="is_top"    />
        <result property="status"    column="status"    />
        <result property="viewCount"    column="view_count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectRdStationPolicyVo">
        select id, station_id, title, summary, content, type, department, publish_time, implement_time, valid_period, is_top, status, view_count, create_by, create_time, update_by, update_time from rd_station_policy
    </sql>

    <select id="selectRdStationPolicyList" parameterType="RdStationPolicy" resultMap="RdStationPolicyResult">
        <include refid="selectRdStationPolicyVo"/>
        <where>
            <if test="stationId != null "> and station_id = #{stationId}</if>
            <if test="title != null  and title != ''">
                and (title like concat('%', #{title}, '%')
                    or summary like concat('%', #{title}, '%')
                    or content like concat('%', #{title}, '%'))
            </if>
            <if test="type != null "> and type = #{type}</if>
            <if test="department != null  and department != ''"> and department like concat('%', #{department}, '%')</if>
            <if test="publishTime != null "> and publish_time = #{publishTime}</if>
            <if test="implementTime != null "> and implement_time = #{implementTime}</if>
            <if test="validPeriod != null  and validPeriod != ''"> and valid_period = #{validPeriod}</if>
            <if test="isTop != null  and isTop != ''"> and is_top = #{isTop}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="viewCount != null "> and view_count = #{viewCount}</if>
        </where>
    </select>

    <select id="selectRdStationPolicyById" parameterType="Long" resultMap="RdStationPolicyResult">
        <include refid="selectRdStationPolicyVo"/>
        where id = #{id}
    </select>

    <insert id="insertRdStationPolicy" parameterType="RdStationPolicy">
        insert into rd_station_policy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="stationId != null">station_id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="summary != null and summary != ''">summary,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="type != null">type,</if>
            <if test="department != null and department != ''">department,</if>
            <if test="publishTime != null">publish_time,</if>
            <if test="implementTime != null">implement_time,</if>
            <if test="validPeriod != null">valid_period,</if>
            <if test="isTop != null and isTop != ''">is_top,</if>
            <if test="status != null">status,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="stationId != null">#{stationId},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="summary != null and summary != ''">#{summary},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="type != null">#{type},</if>
            <if test="department != null and department != ''">#{department},</if>
            <if test="publishTime != null">#{publishTime},</if>
            <if test="implementTime != null">#{implementTime},</if>
            <if test="validPeriod != null">#{validPeriod},</if>
            <if test="isTop != null and isTop != ''">#{isTop},</if>
            <if test="status != null">#{status},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRdStationPolicy" parameterType="RdStationPolicy">
        update rd_station_policy
        <trim prefix="SET" suffixOverrides=",">
            <if test="stationId != null">station_id = #{stationId},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="summary != null and summary != ''">summary = #{summary},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="type != null">type = #{type},</if>
            <if test="department != null and department != ''">department = #{department},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            <if test="implementTime != null">implement_time = #{implementTime},</if>
            <if test="validPeriod != null">valid_period = #{validPeriod},</if>
            <if test="isTop != null and isTop != ''">is_top = #{isTop},</if>
            <if test="status != null">status = #{status},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRdStationPolicyById" parameterType="Long">
        delete from rd_station_policy where id = #{id}
    </delete>

    <delete id="deleteRdStationPolicyByIds" parameterType="String">
        delete from rd_station_policy where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
