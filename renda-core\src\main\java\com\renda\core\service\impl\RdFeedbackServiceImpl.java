package com.renda.core.service.impl;

import java.util.List;
import com.renda.common.utils.DateUtils;
import com.renda.common.utils.SecurityUtils;
import com.renda.core.domain.*;
import com.renda.core.domain.vo.FeedbackInfo2VO;
import com.renda.core.domain.vo.FeedbackInfoVO;
import com.renda.core.service.IRdDeputyService;
import com.renda.core.service.IRdFeedbackAttachmentService;
import com.renda.core.service.IRdMassService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.renda.core.mapper.RdFeedbackMapper;
import com.renda.core.service.IRdFeedbackService;

/**
 * 人大代反馈意见Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-04
 */
@Service
public class RdFeedbackServiceImpl implements IRdFeedbackService
{
    @Autowired
    private RdFeedbackMapper rdFeedbackMapper;

    @Autowired
    private IRdMassService massService;

    @Autowired
    private IRdFeedbackAttachmentService feedbackAttachmentService;

    @Autowired
    private IRdDeputyService deputyService;

    /**
     * 查询人大代反馈意见
     *
     * @param id 人大代反馈意见主键
     * @return 人大代反馈意见
     */
    @Override
    public RdFeedback selectRdFeedbackById(Long id)
    {
        return rdFeedbackMapper.selectRdFeedbackById(id);
    }

    /**
     * 查询人大代反馈意见列表
     *
     * @param rdFeedback 人大代反馈意见
     * @return 人大代反馈意见
     */
    @Override
    public List<RdFeedback> selectRdFeedbackList(RdFeedback rdFeedback)
    {
        return rdFeedbackMapper.selectRdFeedbackList(rdFeedback);
    }

    /**
     * 新增人大代反馈意见
     *
     * @param rdFeedback 人大代反馈意见
     * @return 结果
     */
    @Override
    public int insertRdFeedback(RdFeedback rdFeedback)
    {
        rdFeedback.setCreateTime(DateUtils.getNowDate());
        return rdFeedbackMapper.insertRdFeedback(rdFeedback);
    }

    /**
     * 修改人大代反馈意见
     *
     * @param rdFeedback 人大代反馈意见
     * @return 结果
     */
    @Override
    public int updateRdFeedback(RdFeedback rdFeedback)
    {
        return rdFeedbackMapper.updateRdFeedback(rdFeedback);
    }

    /**
     * 批量删除人大代反馈意见
     *
     * @param ids 需要删除的人大代反馈意见主键
     * @return 结果
     */
    @Override
    public int deleteRdFeedbackByIds(Long[] ids)
    {
        return rdFeedbackMapper.deleteRdFeedbackByIds(ids);
    }

    /**
     * 删除人大代反馈意见信息
     *
     * @param id 人大代反馈意见主键
     * @return 结果
     */
    @Override
    public int deleteRdFeedbackById(Long id)
    {
        return rdFeedbackMapper.deleteRdFeedbackById(id);
    }

    /***
     * 获取建议回复列表
     * @param adviceId
     * @return
     */
    @Override
    public List<FeedbackInfoVO> selectFeedbackListWithAttachs(Long adviceId) {
        return rdFeedbackMapper.selectFeedbackListWithAttachs(adviceId);
    }

    /***
     * 提交反馈接口
     * @param feedbackInfo2VO 意见建议
     * @return
     */
    @Override
    public void saveMassFeedback(FeedbackInfo2VO feedbackInfo2VO, Integer userType) {
        // 保存反馈信息
        RdFeedback feedback = new RdFeedback();
        feedback.setAdviceId(feedbackInfo2VO.getAdviceId());
        feedback.setUserType(userType);
        if (userType == 1) {
            // 获取群众信息
            RdMass mass = massService.selectRdMassById(SecurityUtils.getLoginUser().getUserId());
            feedback.setUserId(mass.getId());
            feedback.setName(mass.getName());
            feedback.setAvatar(mass.getAvatar());
        } else {
            // 获取人大代表信息
            RdDeputy deputy = deputyService.selectRdDeputyById(SecurityUtils.getLoginUser().getUserId());
            feedback.setUserId(deputy.getId());
            feedback.setName(deputy.getName());
            feedback.setAvatar(deputy.getAvatar());
        }
        feedback.setContent(feedbackInfo2VO.getContent());
        feedback.setCreateTime(DateUtils.getNowDate());
        rdFeedbackMapper.insertRdFeedback(feedback);

        // 保存附件图片
        feedbackInfo2VO.getImgList().forEach(img->{
            RdFeedbackAttachment attachment = new RdFeedbackAttachment();
            attachment.setFeedbackId(feedback.getId());
            attachment.setFileType(1);
            attachment.setFileUrl(img);
            feedbackAttachmentService.insertRdFeedbackAttachment(attachment);
        });

        // 保存附件文件
        feedbackInfo2VO.getFileList().forEach(file->{
            RdFeedbackAttachment attachment = new RdFeedbackAttachment();
            attachment.setFeedbackId(feedback.getId());
            attachment.setFileType(2);
            attachment.setFileName(file.getFileName());
            attachment.setFileUrl(file.getFileUrl());
            feedbackAttachmentService.insertRdFeedbackAttachment(attachment);
        });
    }

}
