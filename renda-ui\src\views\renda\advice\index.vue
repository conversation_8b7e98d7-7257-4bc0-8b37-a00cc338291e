<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="电话" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['renda:advice:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['renda:advice:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="adviceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="代表单位" align="left" prop="deputyCompany" />
      <el-table-column label="代表" align="left" prop="deputyName">
        <template slot-scope="scope">
          <span>{{scope.row.deputyName}}({{scope.row.deputyDuty}},{{scope.row.deputyPhone}})</span>
        </template>
      </el-table-column>
      <el-table-column label="标题" align="left" prop="title" />
      <el-table-column label="群众姓名" align="center" prop="name" />
      <el-table-column label="群众电话" align="center" prop="phone" />
      <el-table-column label="建议类别" align="center" prop="category">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.rd_advice_category" :value="scope.row.category"/>
        </template>
      </el-table-column>
      <el-table-column label="处理状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.rd_advice_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="服务评分" align="center" prop="serviceRating">
        <template slot-scope="scope">
          <span v-if="scope.row.serviceRating">{{scope.row.serviceRating}}分</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="提交时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['renda:advice:list']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['renda:advice:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['renda:advice:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 建议详情对话框 -->
    <el-dialog title="群众建议详情" :visible.sync="showAdviceDetail" width="1000px" append-to-body>
      <el-form ref="form" :model="adviceForm" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="建议标题" prop="title">
              <el-input v-model="adviceForm.title" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="建议时间" prop="createTime">
              <el-date-picker clearable
                              readonly
                              v-model="adviceForm.createTime"
                              type="datetime"
                              value-format="yyyy-MM-dd HH:mm:ss"
                              placeholder="请选择活动时间">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="建议类别" prop="category">
              <dict-tag :options="dict.type.rd_advice_category" :value="adviceForm.category"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="处理状态" prop="status">
              <dict-tag :options="dict.type.rd_advice_status" :value="adviceForm.status"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="服务评分" prop="serviceRating">
              <span v-if="adviceForm.serviceRating">{{adviceForm.serviceRating}}分</span>
              <span v-else>-</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="建议内容" prop="content">
              <el-input v-model="adviceForm.content" type="textarea" :rows="10" readonly />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="adviceForm && adviceForm.attachmentList && adviceForm.attachmentList.length > 0">
          <el-col :span="24">
            <el-form-item label="附件" prop="attachments">
              <div v-for="(item, index) of adviceForm.attachmentList" :key="index" >
                <el-link v-if="item.fileType === 2" type="primary" icon="el-icon-document" :href="item.fileUrl" target="_blank">{{item.fileName}}</el-link>
              </div>
              <div class="img-container" >
                <el-image v-for="(item, index) of adviceForm.attachmentList"
                          :key="index"
                          v-if="item.fileType === 1"
                          class="img-item"
                          :src="item.fileUrl"
                          :preview-src-list="getPreviewImgList(adviceForm.attachmentList, index)"></el-image>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="item">
              <div class="label">答复</div>
              <div class="content">
                <el-steps direction="vertical" :active="3">
                  <el-step v-for="(item, index) of adviceForm.feedbackList" :key="index">
                    <div slot="title" class="step-label">
                      {{item.name}}({{item.createTime}})
                      <el-button
                        size="mini"
                        type="text"
                        icon="el-icon-edit"
                        @click="handleEditFeedback(item)"
                        v-hasPermi="['renda:advice:edit']"
                        style="margin-left: 10px;"
                      >编辑</el-button>
                    </div>
                    <div slot="description" class="step-desc">
                      <div class="feedback-content">{{item.content}}</div>
                      <div v-for="(fileItem, fileIndex) of item.attachmentList" :key="fileIndex" >
                        <el-link v-if="fileItem.fileType === 2" type="primary" icon="el-icon-document" :href="fileItem.fileUrl" target="_blank">{{fileItem.fileName}}</el-link>
                      </div>
                      <div class="img-container" >
                        <el-image v-for="(imgItem, imgIndex) of item.attachmentList"
                                  :key="imgIndex"
                                  v-if="imgItem.fileType === 1"
                                  class="img-item"
                                  :src="imgItem.fileUrl"
                                  :preview-src-list="getPreviewImgList(item.attachmentList, imgIndex)"></el-image>
                      </div>
                    </div>
                  </el-step>
                </el-steps>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          icon="el-icon-edit"
          @click="handleUpdate(adviceForm)"
          v-hasPermi="['renda:advice:edit']"
        >编辑建议</el-button>
        <el-button @click="showAdviceDetail = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 修改建议对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="editForm" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="建议标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入建议标题" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="建议内容" prop="content">
              <el-input v-model="form.content" type="textarea" :rows="6" placeholder="请输入建议内容" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="建议时间" prop="createTime">
              <el-date-picker
                v-model="form.createTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择建议时间"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="建议类别" prop="category">
              <el-select v-model="form.category" placeholder="请选择建议类别" clearable style="width: 100%">
                <el-option
                  v-for="dict in dict.type.rd_advice_category"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="处理状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择处理状态" clearable style="width: 100%">
                <el-option
                  v-for="dict in dict.type.rd_advice_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务评分" prop="serviceRating">
              <el-rate
                v-model="form.serviceRating"
                :max="5"
                :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
                :texts="['非常差', '差', '一般', '好', '非常好']"
                show-text>
              </el-rate>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 修改反馈对话框 -->
    <el-dialog title="修改反馈" :visible.sync="feedbackEditOpen" width="600px" append-to-body>
      <el-form ref="feedbackEditForm" :model="feedbackForm" :rules="feedbackRules" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="反馈内容" prop="content">
              <el-input v-model="feedbackForm.content" type="textarea" :rows="6" placeholder="请输入反馈内容" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="反馈时间" prop="createTime">
              <el-date-picker
                v-model="feedbackForm.createTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择反馈时间"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务评分" prop="serviceRating">
              <el-rate
                v-model="feedbackForm.serviceRating"
                :max="5"
                :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
                :texts="['非常差', '差', '一般', '好', '非常好']"
                show-text>
              </el-rate>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submitFeedbackForm">确 定</el-button>
        <el-button @click="cancelFeedback">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listAdvice, getAdvice, delAdvice, updateAdvice } from '@/api/renda/advice'
import { getFeedback, updateFeedback } from '@/api/renda/feedback'

export default {
  name: "Advice",
  dicts: ['rd_advice_category', 'rd_advice_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 群众建议表格数据
      adviceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        content: null,
        name: null,
        phone: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          { required: true, message: "建议标题不能为空", trigger: "blur" },
          { min: 1, max: 200, message: "标题长度应在1到200个字符之间", trigger: "blur" },
          { validator: this.validateTitle, trigger: "blur" }
        ],
        content: [
          { required: true, message: "建议内容不能为空", trigger: "blur" },
          { min: 1, max: 2000, message: "内容长度应在1到2000个字符之间", trigger: "blur" },
          { validator: this.validateContent, trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "建议时间不能为空", trigger: "change" },
          { validator: this.validateCreateTime, trigger: "change" }
        ],
        category: [
          { required: true, message: "建议类别不能为空", trigger: "change" }
        ],
        status: [
          { required: true, message: "处理状态不能为空", trigger: "change" }
        ],
      },
      showAdviceDetail: false, // 显示群众建议详情对话框
      adviceForm: {}, // 群众建议表单
      baseURL: process.env.VUE_APP_BASE_API, // 图片上传地址头
      srcList: [], // 图片预览列表
      // 反馈编辑相关
      feedbackEditOpen: false, // 显示反馈编辑对话框
      feedbackForm: {}, // 反馈表单
      feedbackRules: {
        content: [
          { required: true, message: "反馈内容不能为空", trigger: "blur" },
          { min: 1, max: 2000, message: "内容长度应在1到2000个字符之间", trigger: "blur" },
          { validator: this.validateFeedbackContent, trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "反馈时间不能为空", trigger: "change" },
          { validator: this.validateFeedbackTime, trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询群众建议列表 */
    getList() {
      this.loading = true;
      listAdvice(this.queryParams).then(response => {
        this.adviceList = response.rows;
        this.total = response.total;
        this.loading = false;
        console.log(this.adviceList)
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        title: null,
        content: null,
        createTime: null,
        category: null,
        status: null,
        serviceRating: null,
      };
      this.resetForm("editForm");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getAdvice(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改群众建议";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["editForm"].validate(valid => {
        if (valid) {
          this.loading = true;
          updateAdvice(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          }).catch(error => {
            console.error('更新建议失败:', error);
            this.$modal.msgError("修改失败，请稍后重试");
          }).finally(() => {
            this.loading = false;
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('确定删除本条建议？').then(function() {
        return delAdvice(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('renda/advice/export', {
        ...this.queryParams
      }, `advice_${new Date().getTime()}.xlsx`)
    },
    /** 查看详情 */
    handleDetail(row) {
      getAdvice(row.id).then(response => {
        this.adviceForm = response.data;
        this.srcList = [];
        this.adviceForm.attachmentList.forEach(item => {
          item.fileUrl = this.baseURL + item.fileUrl
          if (item.fileType === 1) {
            this.srcList.push(item.fileUrl)
          }
        });
        this.adviceForm.feedbackList.forEach(fbitem => {
          fbitem.attachmentList.forEach(fbitemImg =>{
            fbitemImg.fileUrl = this.baseURL + fbitemImg.fileUrl
          })
        })
        this.adviceForm.attachmentList.forEach(item => {
          if (item.fileType === 2) {
            // 文件
            // 获取文件扩展名
            const ext = item.fileUrl.substring(item.fileUrl.lastIndexOf('.') + 1).toLowerCase();
            if (ext === 'pdf') {
              item.icon = require('@/assets/images/screen/filetype/pdf.png');
            } else if (ext === 'doc' || ext === 'docx') {
              item.icon = require('@/assets/images/screen/filetype/doc.png');
            } else if (ext === 'xls' || ext === 'xlsx') {
              item.icon = require('@/assets/images/screen/filetype/xls.png');
            } else if (ext === 'ppt' || ext === 'pptx') {
              item.icon = require('@/assets/images/screen/filetype/ppt.png');
            } else {
              item.icon = require('@/assets/images/screen/filetype/unknow.png');
            }
          }
        });
        this.adviceForm.feedbackList.forEach(item => {
          item.attachmentList.forEach(feedbackItem => {
            if (feedbackItem.fileType === 2) {
              // 文件
              // 获取文件扩展名
              const ext = feedbackItem.fileUrl.substring(feedbackItem.fileUrl.lastIndexOf('.') + 1).toLowerCase();
              if (ext === 'pdf') {
                feedbackItem.icon = require('@/assets/images/screen/filetype/pdf.png');
              } else if (ext === 'doc' || ext === 'docx') {
                feedbackItem.icon = require('@/assets/images/screen/filetype/doc.png');
              } else if (ext === 'xls' || ext === 'xlsx') {
                feedbackItem.icon = require('@/assets/images/screen/filetype/xls.png');
              } else if (ext === 'ppt' || ext === 'pptx') {
                feedbackItem.icon = require('@/assets/images/screen/filetype/ppt.png');
              } else {
                feedbackItem.icon = require('@/assets/images/screen/filetype/unknow.png');
              }
            }
          });
        });
        this.showAdviceDetail = true;
      });
    },
    // 大图预览，实现点击当前图片显示当前图片大图，可以随机切换到其他图片进行展示
    getPreviewImgList:function(attachmentList, index) {
      let arr = []
      attachmentList.forEach(item => {
        if (item.fileType == 1) {
          arr.push(item.fileUrl)
        }
      })
      return arr;
    },
    /** 编辑反馈按钮操作 */
    handleEditFeedback(feedback) {
      this.resetFeedback();
      this.loading = true;
      getFeedback(feedback.id).then(response => {
        this.feedbackForm = response.data;
        this.feedbackEditOpen = true;
      }).catch(error => {
        console.error('获取反馈详情失败:', error);
        this.$modal.msgError("获取反馈详情失败，请稍后重试");
      }).finally(() => {
        this.loading = false;
      });
    },
    // 反馈表单重置
    resetFeedback() {
      this.feedbackForm = {
        id: null,
        content: null,
        createTime: null,
        serviceRating: null,
      };
      this.resetForm("feedbackEditForm");
    },
    // 取消反馈编辑
    cancelFeedback() {
      this.feedbackEditOpen = false;
      this.resetFeedback();
    },
    /** 提交反馈编辑表单 */
    submitFeedbackForm() {
      this.$refs["feedbackEditForm"].validate(valid => {
        if (valid) {
          this.loading = true;
          updateFeedback(this.feedbackForm).then(response => {
            this.$modal.msgSuccess("反馈修改成功");
            this.feedbackEditOpen = false;
            // 重新加载建议详情以刷新反馈列表
            if (this.showAdviceDetail && this.adviceForm.id) {
              this.handleDetail({id: this.adviceForm.id});
            }
          }).catch(error => {
            console.error('更新反馈失败:', error);
            this.$modal.msgError("反馈修改失败，请稍后重试");
          }).finally(() => {
            this.loading = false;
          });
        }
      });
    },
    // 自定义验证方法
    validateTitle(rule, value, callback) {
      if (value && value.trim().length === 0) {
        callback(new Error('建议标题不能为空白字符'));
      } else {
        callback();
      }
    },
    validateContent(rule, value, callback) {
      if (value && value.trim().length === 0) {
        callback(new Error('建议内容不能为空白字符'));
      } else {
        callback();
      }
    },
    validateCreateTime(rule, value, callback) {
      if (value) {
        const now = new Date();
        const selectedTime = new Date(value);
        if (selectedTime > now) {
          callback(new Error('建议时间不能晚于当前时间'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    },
    validateFeedbackContent(rule, value, callback) {
      if (value && value.trim().length === 0) {
        callback(new Error('反馈内容不能为空白字符'));
      } else {
        callback();
      }
    },
    validateFeedbackTime(rule, value, callback) {
      if (value) {
        const now = new Date();
        const selectedTime = new Date(value);
        if (selectedTime > now) {
          callback(new Error('反馈时间不能晚于当前时间'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    },
  }
};
</script>

<style lang="scss" scoped>
  .img-container {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
  }
  .img-item {
    width: 200px;
    height: 150px;
    margin: 12px;
  }
  .item {
    display: flex;
    flex-direction: row;
    .label {
      width: 80px;
      line-height: 36px;
      text-align: right;
      font-size: 14px;
      color: #606266;
      padding: 0 12px 0 0;
      font-weight: 700;
    }
    .content {
      flex: 1;
      margin-top: 6px;
    }
  }
  .step-label {
    font-size: 14px;
    color: #606266;
  }
  .step-desc {
    font-size: 14px;
    color: #606266;
  }
  .feedback-content {
    margin: 10px 0;
  }
</style>
