package com.renda.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.renda.system.domain.SysNotice;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.renda.common.annotation.Log;
import com.renda.common.core.controller.BaseController;
import com.renda.common.core.domain.AjaxResult;
import com.renda.common.enums.BusinessType;
import com.renda.core.domain.RdNoticeFeedback;
import com.renda.core.service.IRdNoticeFeedbackService;
import com.renda.common.utils.poi.ExcelUtil;
import com.renda.common.core.page.TableDataInfo;

/**
 * 工作通知反馈Controller
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
@RestController
@RequestMapping("/renda/noticefeedback")
public class RdNoticeFeedbackController extends BaseController
{
    @Autowired
    private IRdNoticeFeedbackService rdNoticeFeedbackService;

    /**
     * 查询工作通知反馈列表
     */
    @PreAuthorize("@ss.hasPermi('system:notice:list')")
    @GetMapping("/list")
    public TableDataInfo list(RdNoticeFeedback rdNoticeFeedback)
    {
        startPage();
        List<RdNoticeFeedback> list = rdNoticeFeedbackService.selectRdNoticeFeedbackList(rdNoticeFeedback);
        return getDataTable(list);
    }

    /**
     * 导出工作通知反馈列表
     */
    @PreAuthorize("@ss.hasPermi('system:notice:export')")
    @Log(title = "工作通知反馈", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RdNoticeFeedback rdNoticeFeedback)
    {
        List<RdNoticeFeedback> list = rdNoticeFeedbackService.selectRdNoticeFeedbackList(rdNoticeFeedback);
        ExcelUtil<RdNoticeFeedback> util = new ExcelUtil<RdNoticeFeedback>(RdNoticeFeedback.class);
        util.exportExcel(response, list, "工作通知反馈数据");
    }

    /**
     * 获取工作通知反馈详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:notice:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rdNoticeFeedbackService.selectRdNoticeFeedbackById(id));
    }

    /**
     * 新增工作通知反馈
     */
    @PreAuthorize("@ss.hasPermi('system:notice:add')")
    @Log(title = "工作通知反馈", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RdNoticeFeedback rdNoticeFeedback)
    {
        return toAjax(rdNoticeFeedbackService.insertRdNoticeFeedback(rdNoticeFeedback));
    }

    /**
     * 修改工作通知反馈
     */
    @PreAuthorize("@ss.hasPermi('system:notice:edit')")
    @Log(title = "工作通知反馈", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RdNoticeFeedback rdNoticeFeedback)
    {
        return toAjax(rdNoticeFeedbackService.updateRdNoticeFeedback(rdNoticeFeedback));
    }

    /**
     * 删除工作通知反馈
     */
    @PreAuthorize("@ss.hasPermi('system:notice:remove')")
    @Log(title = "工作通知反馈", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rdNoticeFeedbackService.deleteRdNoticeFeedbackByIds(ids));
    }

    /**
     * 查询工作通知反馈列表
     */
    @PreAuthorize("@ss.hasPermi('system:notice:list')")
    @PostMapping("/getNoticeFeedback")
    public AjaxResult getNoticeFeedback(@RequestBody SysNotice notice)
    {
        List<RdNoticeFeedback> list = rdNoticeFeedbackService.getNoticeFeedback(notice);
        return AjaxResult.success(list);
    }

}
