import request from '@/utils/request'

// 查询联络站政策列表
export function listStationpolicy(query) {
  return request({
    url: '/renda/stationpolicy/list',
    method: 'get',
    params: query
  })
}

// 查询联络站政策详细
export function getStationpolicy(id) {
  return request({
    url: '/renda/stationpolicy/' + id,
    method: 'get'
  })
}

// 新增联络站政策
export function addStationpolicy(data) {
  return request({
    url: '/renda/stationpolicy',
    method: 'post',
    data: data
  })
}

// 修改联络站政策
export function updateStationpolicy(data) {
  return request({
    url: '/renda/stationpolicy',
    method: 'put',
    data: data
  })
}

// 删除联络站政策
export function delStationpolicy(id) {
  return request({
    url: '/renda/stationpolicy/' + id,
    method: 'delete'
  })
}
