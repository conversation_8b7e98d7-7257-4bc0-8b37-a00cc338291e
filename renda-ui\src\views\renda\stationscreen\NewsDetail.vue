<template>
  <div class="news-detail">
    <app-header :showBackBtn="true" />

    <main class="content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p class="loading-text">正在加载新闻详情...</p>
      </div>

      <!-- 新闻头部信息 -->
      <section v-else class="news-header">
        <div class="news-meta">
          <div class="news-tags">
            <span class="news-category">{{ newsInfo.category }}</span>
          </div>
        </div>

        <h1 class="news-title">{{ newsInfo.title }}</h1>

        <div class="news-info">
          <div class="info-item">
            <span class="label">发布时间：</span>
            <span class="value">{{ newsInfo.publishTime }}</span>
          </div>
          <div class="info-item">
            <span class="label">发布单位：</span>
            <span class="value">{{ newsInfo.publisher }}</span>
          </div>
          <div class="info-item">
            <span class="label">阅读量：</span>
            <span class="value">{{ newsInfo.readCount }}次</span>
          </div>
        </div>
      </section>

      <!-- 新闻内容 -->
      <section v-if="!loading" class="news-content">
        <div class="content-body">
          <div class="news-text">
            <div class="section-content" v-html="newsContent"></div>
          </div>
        </div>
      </section>
    </main>

    <!-- 返回顶部按钮 -->
    <div class="back-to-top" v-show="showBackToTop" @click="scrollToTop">
      <i class="el-icon-top"></i>
      <span>返回顶部</span>
    </div>

    <!-- 返回按钮 -->
    <div class="back-button" @click="goBack">
      <span>返回</span>
    </div>
  </div>
</template>

<script>
import AppHeader from '@/components/StationScreen/AppHeader.vue'
import { getNewsDetail } from '@/api/renda/stationscreen'

export default {
  name: 'NewsDetail',
  components: {
    AppHeader
  },
  dicts: ['rd_news_type'],
  data() {
    return {
      loading: false,
      showBackToTop: false,
      newsInfo: {
        id: null,
        title: '',
        category: '',
        publishTime: '',
        publisher: '',
        readCount: 0,
        image: '',
        imageCaption: ''
      },
      newsContent: '',
    }
  },
  mounted() {
    // 根据路由参数获取新闻详情
    const newsId = this.$route.query.id || this.$route.params.id
    if (newsId) {
      this.fetchNewsDetail(newsId)
    } else {
      this.$message.error('未找到新闻ID')
      this.goBack()
    }

    this.$nextTick(() => {
      // 禁用右键菜单
      document.oncontextmenu = function() {
        return false;
      };
    });

    document.addEventListener('keydown', this.handleKeydown)

    // 添加滚动监听
    const content = document.querySelector('.content')
    if (content) {
      content.addEventListener('scroll', this.handleScroll)
    }
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.handleKeydown)

    // 移除滚动监听
    const content = document.querySelector('.content')
    if (content) {
      content.removeEventListener('scroll', this.handleScroll)
    }
  },
  methods: {
    // 获取新闻详情
    async fetchNewsDetail(newsId) {
      try {
        this.loading = true
        const response = await getNewsDetail(newsId)

        if (response.code === 200 && response.data) {
          this.processNewsData(response.data)
        } else {
          this.$message.error(response.msg || '获取新闻详情失败')
          this.goBack()
        }
      } catch (error) {
        console.error('获取新闻详情失败:', error)
        this.$message.error('获取新闻详情失败')
        this.goBack()
      } finally {
        this.loading = false
      }
    },

    // 处理新闻数据
    processNewsData(data) {
      // 处理基本信息
      this.newsInfo = {
        id: data.id,
        title: data.title || '',
        category: this.getNewsTypeLabel(data.type) || '动态资讯',
        publishTime: this.formatDateTime(data.publishTime),
        publisher: data.publisher || '系统发布',
        readCount: data.viewCount || 0,
        image: data.coverUrl || '',
        imageCaption: data.title || ''
      }

      // 直接使用内容
      this.newsContent = data.content || '<p>暂无详细内容</p>'
    },

    // 格式化日期时间
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return ''

      try {
        const date = new Date(dateTimeStr)
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')

        return `${year}年${month}月${day}日`
      } catch (error) {
        console.error('日期格式化失败:', error)
        return dateTimeStr
      }
    },

    // 获取新闻类型标签
    getNewsTypeLabel(type) {
      const typeMap = {
        1: '政务动态',
        2: '工作动态',
        3: '通知公告',
        4: '媒体报道',
        5: '学习资料'
      }
      return typeMap[type] || '动态资讯'
    },

    // 处理键盘事件
    handleKeydown(event) {
      // ESC键返回新闻列表
      if (event.key === 'Escape') {
        this.goBack()
      }
    },

    goBack() {
      this.$router.back()
    },

    // 返回顶部
    scrollToTop() {
      const content = document.querySelector('.content')
      if (content) {
        content.scrollTo({
          top: 0,
          behavior: 'smooth'
        })
      }
    },

    // 监听滚动事件
    handleScroll() {
      const content = document.querySelector('.content')
      if (content) {
        this.showBackToTop = content.scrollTop > 500
      }
    }
    }
  }
</script>

<style lang="scss" scoped>
.news-detail {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  font-family: 'AL-R' !important;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  margin: 0;
  padding: 0;
}

.content {
  flex: 1;
  padding: 80px 120px;
  max-width: 1800px;
  margin: 0 auto;
  overflow-y: auto;

  /* 隐藏滚动条 */
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

// 新闻头部
.news-header {
  background: #fff;
  border-radius: 24px;
  padding: 60px;
  margin-bottom: 60px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);

  .news-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;

    .news-tags {
      display: flex;
      gap: 20px;

      .news-category {
        padding: 12px 24px;
        border-radius: 25px;
        font-size: 24px;
        font-weight: 600;
        background: linear-gradient(135deg, #d71718, #b41616);
        color: #fff;
      }
    }

    .news-actions {
      display: flex;
      gap: 20px;

      :deep(.el-button) {
        font-size: 20px;
        padding: 15px 30px;
        border-radius: 25px;

        &:first-child {
          background: #d71718;
          border-color: #d71718;
          color: #fff;
        }
      }
    }
  }

  .news-title {
    font-size: 64px;
    color: #333;
    margin: 0 0 40px;
    font-weight: 500;
    line-height: 1.3;
    font-family: 'AL-BL' !important;
    text-align: center;
  }

  .news-info {
    display: flex;
    gap: 60px;
    justify-content: center;

    .info-item {
      display: flex;
      align-items: center;

      .label {
        font-size: 28px;
        color: #666;
        margin-right: 15px;
      }

      .value {
        font-size: 28px;
        color: #333;
        font-weight: 600;
      }
    }
  }
}

// 新闻内容
.news-content {
  background: #fff;
  border-radius: 24px;
  padding: 60px;
  margin-bottom: 60px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);

  .content-body {
    .news-image {
      margin-bottom: 50px;
      text-align: center;

      img {
        width: 100%;
        max-width: 800px;
        border-radius: 16px;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
      }

      .image-caption {
        margin-top: 20px;
        font-size: 20px;
        color: #999;
        font-style: italic;
      }
    }

    .news-text {
      .section-content {
        font-size: 30px;
        line-height: 1.8;
        color: #555;

        :deep(p) {
          margin: 0 0 20px;
          text-indent: 2em;

          &:last-child {
            margin-bottom: 0;
          }
        }

        // 新闻内容中的图片样式
        :deep(img) {
          display: block !important;
          max-width: 90% !important;
          width: auto !important;
          height: auto !important;
          max-height: 800px !important;
          margin: 30px auto !important;
          border-radius: 12px !important;
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1) !important;
          transition: all 0.3s ease !important;

          &:hover {
            transform: scale(1.02) !important;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
          }
        }

        // 包含图片的段落样式
        :deep(p) {
          img {
            display: block !important;
            max-width: 90% !important;
            width: auto !important;
            height: auto !important;
            max-height: 800px !important;
            margin: 30px auto !important;
            border-radius: 12px !important;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1) !important;
          }
        }

        // 直接在div中的图片
        :deep(div img) {
          display: block !important;
          max-width: 90% !important;
          width: auto !important;
          height: auto !important;
          max-height: 800px !important;
          margin: 30px auto !important;
          border-radius: 12px !important;
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1) !important;
        }
      }
    }
  }
}

// 加载状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: #666;

  .loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #d71718;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
  }

  .loading-text {
    font-size: 24px;
    margin: 0;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 返回顶部按钮
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 160px;
  height: 60px;
  border-radius: 30px;
  background: linear-gradient(135deg, #d71718, #b41616);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  box-shadow: 0 8px 25px rgba(215, 23, 24, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1000;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(215, 23, 24, 0.4);
  }

  i {
    font-size: 24px;
  }

  span {
    font-size: 16px;
    font-weight: 500;
    font-family: 'AL-L';
  }
}

// 返回按钮
.back-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 80px;
  height: 80px;
  background: rgba(215, 23, 24, 0.9);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(215, 23, 24, 0.3);
  backdrop-filter: blur(10px);
  z-index: 1000;

  &:hover {
    background: rgba(215, 23, 24, 1);
    transform: scale(1.1);
    box-shadow: 0 8px 30px rgba(215, 23, 24, 0.5);
  }

  .back-icon {
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 8px 12px 8px 0;
    border-color: transparent #fff transparent transparent;
    margin-bottom: 4px;
  }

  span {
    color: #fff;
    font-size: 12px;
    font-weight: 500;
    font-family: 'AL-R';
  }
}

/* 4K分辨率适配 */
@media (min-width: 3000px) {
  .back-to-top {
    bottom: 60px;
    right: 900px;
    width: 220px;
    height: 80px;
    border-radius: 40px;
    gap: 15px;

    i {
      font-size: 32px;
    }

    span {
      font-size: 24px;
    }
  }

  .back-button {
    bottom:  60px;
    right: 60px;
    width: 120px;
    height: 120px;

    .back-icon {
      border-width: 12px 18px 12px 0;
      margin-bottom: 6px;
    }

    span {
      font-size: 24px;
    }
  }

  .news-content {
    .content-body {
      .news-text {
        .section-content {
          // 4K分辨率下图片样式调整
          :deep(img) {
            max-height: 800px;
            margin: 50px auto;
            border-radius: 16px;
          }
        }
      }
    }
  }
}


</style>

<style lang="scss">
// 全局样式确保新闻内容中的图片正确显示
.news-detail .news-content .section-content {
  img {
    display: block !important;
    max-width: 90% !important;
    width: auto !important;
    height: auto !important;
    max-height: 800px !important;
    margin: 30px auto !important;
    border-radius: 12px !important;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;

    &:hover {
      transform: scale(1.02) !important;
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
    }
  }

  p img, div img {
    display: block !important;
    max-width: 90% !important;
    width: auto !important;
    height: auto !important;
    max-height: 800px !important;
    margin: 30px auto !important;
    border-radius: 12px !important;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1) !important;
  }
}
</style>
