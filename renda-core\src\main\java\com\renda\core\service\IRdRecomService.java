package com.renda.core.service;

import java.util.List;
import com.renda.core.domain.RdRecom;

/**
 * 代表建议Service接口
 *
 * <AUTHOR>
 * @date 2023-11-23
 */
public interface IRdRecomService
{
    /**
     * 查询代表建议
     *
     * @param id 代表建议主键
     * @return 代表建议
     */
    public RdRecom selectRdRecomById(Long id);

    /**
     * 查询代表建议列表
     *
     * @param rdRecom 代表建议
     * @return 代表建议集合
     */
    public List<RdRecom> selectRdRecomList(RdRecom rdRecom);

    /**
     * 新增代表建议
     *
     * @param rdRecom 代表建议
     * @return 结果
     */
    public int insertRdRecom(RdRecom rdRecom);

    /**
     * 修改代表建议
     *
     * @param rdRecom 代表建议
     * @return 结果
     */
    public int updateRdRecom(RdRecom rdRecom);

    /**
     * 批量删除代表建议
     *
     * @param ids 需要删除的代表建议主键集合
     * @return 结果
     */
    public int deleteRdRecomByIds(Long[] ids);

    /**
     * 删除代表建议信息
     *
     * @param id 代表建议主键
     * @return 结果
     */
    public int deleteRdRecomById(Long id);

    /***
     * 查询主办协办列表
     * @param keyword 关键字
     * @return 主办协办列表
     */
    List<String> listHost(String keyword);

}
