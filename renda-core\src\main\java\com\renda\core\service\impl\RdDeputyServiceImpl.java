package com.renda.core.service.impl;

import java.io.File;
import java.util.Date;
import java.util.List;

import com.renda.common.annotation.DataScope;
import com.renda.common.config.RendaConfig;
import com.renda.common.utils.DateUtils;
import com.renda.common.utils.SecurityUtils;
import com.renda.common.utils.StringUtils;
import com.renda.common.utils.file.FileUploadUtils;
import com.renda.core.domain.vo.DeputyInfoVO;
import com.renda.core.domain.vo.DeputyStatVO;
import com.renda.core.domain.vo.DeputyStatisticsInfoVO;
import com.renda.core.service.IRdAdviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.renda.core.mapper.RdDeputyMapper;
import com.renda.core.domain.RdDeputy;
import com.renda.core.service.IRdDeputyService;
import cn.hutool.extra.qrcode.QrCodeUtil;

/**
 * 人大代表管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-10-23
 */
@Service
public class RdDeputyServiceImpl implements IRdDeputyService
{
    @Autowired
    private RdDeputyMapper rdDeputyMapper;

    /**
     * 验证联络站职务人数限制
     *
     * @param stationId 联络站ID
     * @param stationDuty 站内职务
     * @param excludeId 排除的代表ID（修改时使用，新增时传null）
     */
    private void validateStationDutyLimit(Long stationId, String stationDuty, Long excludeId) {
        // 如果联络站ID为空或站内职务为空/空字符串，则不进行限制检查
        if (stationId == null || StringUtils.isEmpty(stationDuty) || "".equals(stationDuty.trim())) {
            return;
        }

        // 查询当前联络站该职务的人数
        int currentCount = rdDeputyMapper.countByStationIdAndStationDuty(stationId, stationDuty, excludeId);

        // 定义职务人数限制
        int maxCount = 0;
        String dutyName = "";

        switch (stationDuty.trim()) {
            case "站长": // 站长
                maxCount = 1;
                dutyName = "站长";
                break;
            case "副站长": // 副站长
                maxCount = 3;
                dutyName = "副站长";
                break;
            case "联络员": // 联络员
                maxCount = 6;
                dutyName = "联络员";
                break;
            default:
                return; // 未知职务，不限制
        }

        if (currentCount >= maxCount) {
            throw new RuntimeException("联络站" + dutyName + "人数已达上限（" + maxCount + "人），无法继续添加");
        }
    }

    /** 人大代表二维码基础参数 */
    @Value("${qrcode.width}")
    private int qrcodeWidth;
    @Value("${qrcode.height}")
    private int qrcodeHeight;
    @Value("${renda.profile}")
    private String qrcodePath;
    @Value("${qrcode.url}")
    private String qrcodeUrl;

    /**
     * 查询人大代表管理
     *
     * @param id 人大代表管理主键
     * @return 人大代表管理
     */
    @Override
    public RdDeputy selectRdDeputyById(Long id)
    {
        return rdDeputyMapper.selectRdDeputyById(id);
    }

    /**
     * 查询人大代表管理列表
     *
     * @param rdDeputy 人大代表管理
     * @return 人大代表管理
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<RdDeputy> selectRdDeputyList(RdDeputy rdDeputy)
    {
        return rdDeputyMapper.selectRdDeputyList(rdDeputy);
    }

    /**
     * 查询人大代表管理列表
     *
     * @param rdDeputy 人大代表管理
     * @return 人大代表管理
     */
    @Override
    public List<RdDeputy> selectRdDeputyList2(RdDeputy rdDeputy)
    {
        return rdDeputyMapper.selectRdDeputyList2(rdDeputy);
    }

    /***
     * 根据部门查询人大代表列表
     * @param id 部门id
     * @return
     */
    @Override
    public List<RdDeputy> selectRdDeputyListByDeptId(Long id) {
        return rdDeputyMapper.selectRdDeputyListByDeptId(id);
    }

    /**
     * 新增人大代表管理
     *
     * @param rdDeputy 人大代表管理
     * @return 结果
     */
    @Override
    public int insertRdDeputy(RdDeputy rdDeputy)
    {
        // 如果代表手机号已存在，则不允许新增
        RdDeputy query = new RdDeputy();
        query.setPhone(rdDeputy.getPhone());
        List<RdDeputy> list = rdDeputyMapper.selectRdDeputyList(query);
        if (list != null && list.size() > 0) {
            throw new RuntimeException("此手机号已在系统登记，无法再次登记");
        }

        // 验证联络站职务人数限制
        validateStationDutyLimit(rdDeputy.getStationId(), rdDeputy.getStationDuty(), null);

        // 如果rdDeputy的Avatar是data:image开头的，说明是base64编码的图片，需要先保存图片，再把图片路径赋值给rdDeputy的Avatar
        if (StringUtils.isNotEmpty(rdDeputy.getAvatar()) && rdDeputy.getAvatar().startsWith("data:image")) {
            // 获取Base64数据
            String base64 = rdDeputy.getAvatar().split(",")[1];
            String avatar = FileUploadUtils.uploadBase64Image(RendaConfig.getProfile()+"/deputy", base64);
            if (StringUtils.isEmpty(avatar)) {
                throw new RuntimeException("图片上传失败");
            }
            rdDeputy.setAvatar(avatar);
        }
        String username = SecurityUtils.getLoginUser().getUsername();
        Date date = DateUtils.getNowDate();
        rdDeputy.setCreateBy(username);
        rdDeputy.setCreateTime(date);
        rdDeputy.setUpdateBy(username);
        rdDeputy.setUpdateTime(date);
        rdDeputyMapper.insertRdDeputy(rdDeputy);

        /** 生成代表二维码 */
        // 二维码文件保存路径
        String fileName = "qrcode-"+rdDeputy.getGroupId()+"-"+rdDeputy.getId()+"-"+rdDeputy.getName()+".png";
        File qrCodeFile = new File(qrcodePath + "/qrcode/" + fileName);
        // 二维码内容
        String text = qrcodeUrl + rdDeputy.getId().toString();
        // 生成二维码
        QrCodeUtil.generate(text, qrcodeWidth, qrcodeHeight, qrCodeFile);

        // 回写二维码Url
        String qrcodeUrl = "/profile/qrcode/" + fileName;
        rdDeputy.setQrcodeUrl(qrcodeUrl);
        return rdDeputyMapper.updateRdDeputy(rdDeputy);
    }

    /**
     * 修改人大代表管理
     *
     * @param rdDeputy 人大代表管理
     * @return 结果
     */
    @Override
    public int updateRdDeputy(RdDeputy rdDeputy)
    {
        // 只有当手机号不为空时，才检查手机号是否重复
        if (StringUtils.isNotEmpty(rdDeputy.getPhone())) {
            RdDeputy query = new RdDeputy();
            query.setPhone(rdDeputy.getPhone());
            List<RdDeputy> list = rdDeputyMapper.selectRdDeputyList(query);
            if (list != null && list.size() > 0) {
                if (!list.get(0).getId().equals(rdDeputy.getId())) {
                    throw new RuntimeException("此手机号已在系统登记，无法再次登记");
                }
            }
        }

        // 只有当站点ID和站内职务都不为空时，才验证联络站职务人数限制
        if (rdDeputy.getStationId() != null && StringUtils.isNotEmpty(rdDeputy.getStationDuty())) {
            validateStationDutyLimit(rdDeputy.getStationId(), rdDeputy.getStationDuty(), rdDeputy.getId());
        }

        // 如果rdDeputy的Avatar是data:image开头的，说明是base64编码的图片，需要先保存图片，再把图片路径赋值给rdDeputy的Avatar
        if (StringUtils.isNotEmpty(rdDeputy.getAvatar()) && rdDeputy.getAvatar().startsWith("data:image")) {
            // 获取Base64数据
            String base64 = rdDeputy.getAvatar().split(",")[1];
            String avatar = FileUploadUtils.uploadBase64Image(RendaConfig.getProfile()+"/deputy", base64);
            if (StringUtils.isEmpty(avatar)) {
                throw new RuntimeException("图片上传失败");
            }
            rdDeputy.setAvatar(avatar);
        }
        String username = SecurityUtils.getLoginUser().getUsername();
        Date date = DateUtils.getNowDate();
        rdDeputy.setUpdateBy(username);
        rdDeputy.setUpdateTime(date);
        return rdDeputyMapper.updateRdDeputy(rdDeputy);
    }

    /**
     * 批量删除人大代表管理
     *
     * @param ids 需要删除的人大代表管理主键
     * @return 结果
     */
    @Override
    public int deleteRdDeputyByIds(Long[] ids)
    {
        return rdDeputyMapper.deleteRdDeputyByIds(ids);
    }

    /**
     * 删除人大代表管理信息
     *
     * @param id 人大代表管理主键
     * @return 结果
     */
    @Override
    public int deleteRdDeputyById(Long id)
    {
        return rdDeputyMapper.deleteRdDeputyById(id);
    }

    /**
     * 查询人大代表管理（小程序端展示）
     *
     * @param id 人大代表管理主键
     * @return 人大代表管理
     */
    @Override
    public DeputyInfoVO selectRdDeputyInfoById(Long id) {
        return rdDeputyMapper.selectRdDeputyInfoById(id);
    }

    /***
     * 根据手机号获取人大代表信息
     * @param phone
     * @return 人大代表信息
     */
    @Override
    public RdDeputy selectDeputyByPhone(String phone) {
        return rdDeputyMapper.selectDeputyByPhone(phone);
    }

    /***
     * 获取代表统计信息接口
     * @return
     */
    @Override
    public DeputyStatisticsInfoVO getDeputyStatistics() {
        Long userId = SecurityUtils.getLoginUser().getUserId();
        return rdDeputyMapper.getDeputyStatistics(userId);
    }

    /**
     * 获取人大代表履职统计信息
     */
    @Override
    public List<DeputyStatVO> getDeputyStat(Long id) {
        return rdDeputyMapper.getDeputyStat(id);
    }

    /**
     * 批量生成代表二维码
     */
    @Override
    public int genDeputyQrcode(Long[] ids) {

        // 循环代表ids，生成二维码
        for (Long id : ids) {
            RdDeputy rdDeputy = rdDeputyMapper.selectRdDeputyById(id);
            if (rdDeputy == null) {
                throw new RuntimeException("未找到该代表信息");
            }

            // 先删除原来的二维码
            if (StringUtils.isNotEmpty(rdDeputy.getQrcodeUrl())) {
                // 提取文件名
                String QrcodeUrl = rdDeputy.getQrcodeUrl();
                String fileName = QrcodeUrl.substring(QrcodeUrl.lastIndexOf("/") + 1);
                File qrCodeFile = new File(qrcodePath + "/qrcode/" + fileName);
                if (qrCodeFile.exists()) {
                    qrCodeFile.delete();
                }
            }

            /** 生成代表二维码 */
            // 二维码文件保存路径
            String fileName = "qrcode-"+rdDeputy.getGroupId()+"-"+rdDeputy.getId()+"-"+rdDeputy.getName()+".png";
            File qrCodeFile = new File(qrcodePath + "/qrcode/" + fileName);
            // 二维码内容
            String text = qrcodeUrl + rdDeputy.getId().toString();
            // 生成二维码
            QrCodeUtil.generate(text, qrcodeWidth, qrcodeHeight, qrCodeFile);

            // 回写二维码Url
            String qrcodeUrl = "/profile/qrcode/" + fileName;
            rdDeputy.setQrcodeUrl(qrcodeUrl);
            rdDeputyMapper.updateRdDeputy(rdDeputy);
        }

        return 1;

    }

}
