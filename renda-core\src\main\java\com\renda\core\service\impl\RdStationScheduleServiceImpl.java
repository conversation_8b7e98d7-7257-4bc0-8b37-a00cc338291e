package com.renda.core.service.impl;

import java.util.List;
import java.util.Date;
import com.renda.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.renda.core.mapper.RdStationScheduleMapper;
import com.renda.core.domain.RdStationSchedule;
import com.renda.core.service.IRdStationScheduleService;

/**
 * 排班表管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Service
public class RdStationScheduleServiceImpl implements IRdStationScheduleService
{
    @Autowired
    private RdStationScheduleMapper rdStationScheduleMapper;

    /**
     * 查询排班表管理
     *
     * @param id 排班表管理主键
     * @return 排班表管理
     */
    @Override
    public RdStationSchedule selectRdStationScheduleById(Long id)
    {
        return rdStationScheduleMapper.selectRdStationScheduleById(id);
    }

    /**
     * 查询排班表管理列表
     *
     * @param rdStationSchedule 排班表管理
     * @return 排班表管理
     */
    @Override
    public List<RdStationSchedule> selectRdStationScheduleList(RdStationSchedule rdStationSchedule)
    {
        return rdStationScheduleMapper.selectRdStationScheduleList(rdStationSchedule);
    }

    /**
     * 检查指定联络站和日期是否已存在排班记录
     *
     * @param stationId 联络站ID
     * @param scheduleDate 排班日期
     * @param excludeId 排除的记录ID（修改时使用）
     * @return 是否存在重复记录
     */
    @Override
    public boolean checkStationScheduleExists(Long stationId, Date scheduleDate, Long excludeId)
    {
        return rdStationScheduleMapper.checkStationScheduleExists(stationId, scheduleDate, excludeId) > 0;
    }

    /**
     * 新增排班表管理
     *
     * @param rdStationSchedule 排班表管理
     * @return 结果
     */
    @Override
    public int insertRdStationSchedule(RdStationSchedule rdStationSchedule)
    {
        // 检查是否已存在相同联络站相同日期的排班记录
        if (checkStationScheduleExists(rdStationSchedule.getStationId(), rdStationSchedule.getScheduleDate(), null))
        {
            throw new RuntimeException("该联络站在指定日期已存在排班记录，每个联络站每天只能有一条排班信息");
        }
        rdStationSchedule.setCreateTime(DateUtils.getNowDate());
        return rdStationScheduleMapper.insertRdStationSchedule(rdStationSchedule);
    }

    /**
     * 修改排班表管理
     *
     * @param rdStationSchedule 排班表管理
     * @return 结果
     */
    @Override
    public int updateRdStationSchedule(RdStationSchedule rdStationSchedule)
    {
        // 检查是否已存在相同联络站相同日期的排班记录（排除当前记录）
        if (checkStationScheduleExists(rdStationSchedule.getStationId(), rdStationSchedule.getScheduleDate(), rdStationSchedule.getId()))
        {
            throw new RuntimeException("该联络站在指定日期已存在排班记录，每个联络站每天只能有一条排班信息");
        }
        rdStationSchedule.setUpdateTime(DateUtils.getNowDate());
        return rdStationScheduleMapper.updateRdStationSchedule(rdStationSchedule);
    }

    /**
     * 批量删除排班表管理
     *
     * @param ids 需要删除的排班表管理主键
     * @return 结果
     */
    @Override
    public int deleteRdStationScheduleByIds(Long[] ids)
    {
        return rdStationScheduleMapper.deleteRdStationScheduleByIds(ids);
    }

    /**
     * 删除排班表管理信息
     *
     * @param id 排班表管理主键
     * @return 结果
     */
    @Override
    public int deleteRdStationScheduleById(Long id)
    {
        return rdStationScheduleMapper.deleteRdStationScheduleById(id);
    }
}
