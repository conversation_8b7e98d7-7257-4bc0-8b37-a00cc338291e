package com.renda.core.service;

import java.util.List;
import com.renda.core.domain.RdActivity;
import com.renda.core.domain.RdActivityRegistration;

/**
 * 活动Service接口
 *
 * <AUTHOR>
 * @date 2023-11-13
 */
public interface IRdActivityService
{
    /**
     * 查询活动
     *
     * @param id 活动主键
     * @return 活动
     */
    RdActivity selectRdActivityById(Long id);

    /**
     * 查询活动列表
     *
     * @param rdActivity 活动
     * @return 活动集合
     */
    List<RdActivity> selectRdActivityList(RdActivity rdActivity);

    /**
     * 新增活动
     *
     * @param rdActivity 活动
     * @return 结果
     */
    int insertRdActivity(RdActivity rdActivity);

    /**
     * 修改活动
     *
     * @param rdActivity 活动
     * @return 结果
     */
    int updateRdActivity(RdActivity rdActivity);

    /**
     * 批量删除活动
     *
     * @param ids 需要删除的活动主键集合
     * @return 结果
     */
    int deleteRdActivityByIds(Long[] ids);

    /**
     * 删除活动信息
     *
     * @param id 活动主键
     * @return 结果
     */
    int deleteRdActivityById(Long id);

    /***
     * 获取活动信息接口-前5条
     * @param activity 活动信息
     * @return 活动信息列表-前5条
     */
    List<RdActivity> getActivities(RdActivity activity);

    /***
     * 获取活动信息接口
     * @return 活动信息列表
     */
    List<RdActivity> selectActivityList(RdActivity activity);

    /***
     * 活动报名请假接口
     * @param activityRegistration 活动报名请假信息
     * @return
     */
    void registerActivity(RdActivityRegistration activityRegistration);

    /***
     * 获取代表活动接口
     * @return
     */
    List<RdActivity> getDeputyActivityList(Long userId);

    /**
     * 单发短信
     */
    int sendSms(RdActivityRegistration rdActivityRegistration);

    /**
     * 群发短信
     */
    int batchSendSms(RdActivity rdActivity);

}
