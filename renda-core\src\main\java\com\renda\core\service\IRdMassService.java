package com.renda.core.service;

import java.util.List;
import com.renda.core.domain.RdMass;
import com.renda.core.domain.vo.MassAdviceInfoVO;
import com.renda.core.domain.vo.MassStatisticsInfoVO;

/**
 * 群众管理Service接口
 *
 * <AUTHOR>
 * @date 2023-10-30
 */
public interface IRdMassService
{
    /**
     * 查询群众管理
     *
     * @param id 群众管理主键
     * @return 群众管理
     */
    public RdMass selectRdMassById(Long id);

    /**
     * 查询群众管理列表
     *
     * @param rdMass 群众管理
     * @return 群众管理集合
     */
    public List<RdMass> selectRdMassList(RdMass rdMass);

    /**
     * 新增群众管理
     *
     * @param rdMass 群众管理
     * @return 结果
     */
    public int insertRdMass(RdMass rdMass);

    /**
     * 修改群众管理
     *
     * @param rdMass 群众管理
     * @return 结果
     */
    public int updateRdMass(RdMass rdMass);

    /**
     * 批量删除群众管理
     *
     * @param ids 需要删除的群众管理主键集合
     * @return 结果
     */
    public int deleteRdMassByIds(Long[] ids);

    /**
     * 删除群众管理信息
     *
     * @param id 群众管理主键
     * @return 结果
     */
    public int deleteRdMassById(Long id);

    /***
     * 获取用户统计信息接口
     * @return
     */
    MassStatisticsInfoVO getMassStatistics(Long userId);

    /***
     * 获取用户建议列表接口
     * @return
     */
    List<MassAdviceInfoVO> getMassAdviceList(Long userId);

}
