package com.renda.core.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.renda.core.mapper.RdAdviceAttachmentMapper;
import com.renda.core.domain.RdAdviceAttachment;
import com.renda.core.service.IRdAdviceAttachmentService;

/**
 * 群众建议附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-03
 */
@Service
public class RdAdviceAttachmentServiceImpl implements IRdAdviceAttachmentService
{
    @Autowired
    private RdAdviceAttachmentMapper rdAdviceAttachmentMapper;

    /**
     * 查询群众建议附件
     *
     * @param id 群众建议附件主键
     * @return 群众建议附件
     */
    @Override
    public RdAdviceAttachment selectRdAdviceAttachmentById(Long id)
    {
        return rdAdviceAttachmentMapper.selectRdAdviceAttachmentById(id);
    }

    /**
     * 查询群众建议附件列表
     *
     * @param rdAdviceAttachment 群众建议附件
     * @return 群众建议附件
     */
    @Override
    public List<RdAdviceAttachment> selectRdAdviceAttachmentList(RdAdviceAttachment rdAdviceAttachment)
    {
        return rdAdviceAttachmentMapper.selectRdAdviceAttachmentList(rdAdviceAttachment);
    }

    /**
     * 新增群众建议附件
     *
     * @param rdAdviceAttachment 群众建议附件
     * @return 结果
     */
    @Override
    public int insertRdAdviceAttachment(RdAdviceAttachment rdAdviceAttachment)
    {
        return rdAdviceAttachmentMapper.insertRdAdviceAttachment(rdAdviceAttachment);
    }

    /**
     * 修改群众建议附件
     *
     * @param rdAdviceAttachment 群众建议附件
     * @return 结果
     */
    @Override
    public int updateRdAdviceAttachment(RdAdviceAttachment rdAdviceAttachment)
    {
        return rdAdviceAttachmentMapper.updateRdAdviceAttachment(rdAdviceAttachment);
    }

    /**
     * 批量删除群众建议附件
     *
     * @param ids 需要删除的群众建议附件主键
     * @return 结果
     */
    @Override
    public int deleteRdAdviceAttachmentByIds(Long[] ids)
    {
        return rdAdviceAttachmentMapper.deleteRdAdviceAttachmentByIds(ids);
    }

    /**
     * 删除群众建议附件信息
     *
     * @param id 群众建议附件主键
     * @return 结果
     */
    @Override
    public int deleteRdAdviceAttachmentById(Long id)
    {
        return rdAdviceAttachmentMapper.deleteRdAdviceAttachmentById(id);
    }
}
