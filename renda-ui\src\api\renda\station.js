import request from '@/utils/request'

// 查询站点列表
export function listStation(query) {
  return request({
    url: '/renda/station/list',
    method: 'get',
    params: query
  })
}

// 查询站点列表（排除节点）
export function listStationExcludeChild(stationId) {
  return request({
    url: '/renda/station/list/exclude/' + stationId,
    method: 'get'
  })
}

// 查询站点详细
export function getStation(stationId) {
  return request({
    url: '/renda/station/' + stationId,
    method: 'get'
  })
}

// 新增站点
export function addStation(data) {
  return request({
    url: '/renda/station',
    method: 'post',
    data: data
  })
}

// 修改站点
export function updateStation(data) {
  return request({
    url: '/renda/station',
    method: 'put',
    data: data
  })
}

// 删除站点
export function delStation(stationId) {
  return request({
    url: '/renda/station/' + stationId,
    method: 'delete'
  })
}

// 查询站点下拉树结构
export function stationTreeSelect() {
  return request({
    url: '/renda/station/stationTree',
    method: 'get'
  })
}

// 查询站点下拉树结构
export function stationTreeSelectWithDeputy() {
  return request({
    url: '/renda/station/stationTreeWithDeputy',
    method: 'get'
  })
}

// 查询代表团下拉树结构
export function groupTreeSelectWithDeputy() {
  return request({
    url: '/renda/station/groupTreeSelectWithDeputy',
    method: 'get'
  })
}

