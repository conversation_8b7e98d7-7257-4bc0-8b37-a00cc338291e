import request from '@/utils/request'

// 查询人大代反馈意见列表
export function listFeedback(query) {
  return request({
    url: '/renda/feedback/list',
    method: 'get',
    params: query
  })
}

// 查询人大代反馈意见详细
export function getFeedback(id) {
  return request({
    url: '/renda/feedback/' + id,
    method: 'get'
  })
}

// 新增人大代反馈意见
export function addFeedback(data) {
  return request({
    url: '/renda/feedback',
    method: 'post',
    data: data
  })
}

// 修改人大代反馈意见
export function updateFeedback(data) {
  return request({
    url: '/renda/feedback',
    method: 'put',
    data: data
  })
}

// 删除人大代反馈意见
export function delFeedback(id) {
  return request({
    url: '/renda/feedback/' + id,
    method: 'delete'
  })
}

// 导出人大代反馈意见
export function exportFeedback(query) {
  return request({
    url: '/renda/feedback/export',
    method: 'post',
    params: query
  })
}
