package com.renda.core.service;

import java.util.List;
import com.renda.core.domain.RdActivityRegistration;

/**
 * 活动报名Service接口
 *
 * <AUTHOR>
 * @date 2023-11-14
 */
public interface IRdActivityRegistrationService
{
    /**
     * 查询活动报名
     *
     * @param id 活动报名主键
     * @return 活动报名
     */
    public RdActivityRegistration selectRdActivityRegistrationById(Long id);

    /**
     * 查询活动报名列表
     *
     * @param rdActivityRegistration 活动报名
     * @return 活动报名集合
     */
    public List<RdActivityRegistration> selectRdActivityRegistrationList(RdActivityRegistration rdActivityRegistration);

    /**
     * 新增活动报名
     *
     * @param rdActivityRegistration 活动报名
     * @return 结果
     */
    public int insertRdActivityRegistration(RdActivityRegistration rdActivityRegistration);

    /**
     * 修改活动报名
     *
     * @param rdActivityRegistration 活动报名
     * @return 结果
     */
    public int updateRdActivityRegistration(RdActivityRegistration rdActivityRegistration);

    /**
     * 批量删除活动报名
     *
     * @param ids 需要删除的活动报名主键集合
     * @return 结果
     */
    public int deleteRdActivityRegistrationByIds(Long[] ids);

    /**
     * 删除活动报名信息
     *
     * @param id 活动报名主键
     * @return 结果
     */
    public int deleteRdActivityRegistrationById(Long id);



    /***
     * 根据活动id和用户id查询报名信息
     * @param activityRegistration 活动信息
     * @return
     */
    RdActivityRegistration selectRdActivityRegistrationByDeputyId(RdActivityRegistration activityRegistration);

    /**
     * 删除活动报名信息
     * @param activityId
     * @param registrationType
     */
    void deleteRdActivityRegistrationByActivityId(Long activityId, String registrationType);

    /***
     * 根据活动id和用户id查询报名信息
     * @param activityId 活动id
     * @param deputyId 用户id
     * @return 活动报名信息
     */
    RdActivityRegistration selectRdActivityRegistrationByActivityIdAndDeputyId(Long activityId, Long deputyId);
}
