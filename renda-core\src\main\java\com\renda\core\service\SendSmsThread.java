package com.renda.core.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.renda.common.utils.DateUtils;
import com.renda.core.domain.RdActivity;
import com.renda.core.domain.RdActivityRegistration;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SendSmsThread extends Thread {

    private IRdActivityRegistrationService activityRegistrationService;
    private RdActivity activity;
    private String url;
    private String apikey;
    private String secret;
    private String signId;
    private String templateId;

    public SendSmsThread(String url, String apikey, String secret, String signId, String templateId,
                         IRdActivityRegistrationService activityRegistrationService, RdActivity activity) {
        this.url = url;
        this.apikey = apikey;
        this.secret = secret;
        this.signId = signId;
        this.templateId = templateId;
        this.activity = activity;
        this.activityRegistrationService = activityRegistrationService;
    }

    @Override
    public void run() {
        try {
            // 获取报名信息
            RdActivityRegistration activityRegistration = new RdActivityRegistration();
            activityRegistration.setActivityId(activity.getId());
            List<RdActivityRegistration> activityRegistrations = activityRegistrationService.selectRdActivityRegistrationList(activityRegistration);

            // 初始化短信平台配置
            HttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(url);
            httpPost.addHeader("Content-Type","application/json");
            Map<String,Object> map = new HashMap<>();
            map.put("apikey", apikey);
            map.put("secret", secret);
            map.put("sign_id", signId);
            map.put("template_id", templateId);
            map.put("content", "");

            activityRegistrations.forEach(registration -> {

                // 发送短信
                map.put("mobile",registration.getPhone());
                String json = JSON.toJSONString(map);
                httpPost.setEntity(new StringEntity(json,"UTF-8"));
                HttpResponse response = null;
                try {
                    response = httpClient.execute(httpPost);
                } catch (IOException e) {
                    e.printStackTrace();
                    throw new RuntimeException("短信平台连接异常");
                }
                HttpEntity entity = response.getEntity();
                String ret_json = null;
                try {
                    ret_json = EntityUtils.toString(entity);
                } catch (IOException e) {
                    e.printStackTrace();
                    throw new RuntimeException("短信平台返回数据异常");
                }
                JSONObject result = JSON.parseObject(ret_json);

                // 更新数据库发送状态
                registration.setSmsSendCount(registration.getSmsSendCount() + 1);
                registration.setSmsSendStatus(result.getInteger("code"));
                registration.setSmsMsgNo(result.getString("msg_no"));
                registration.setSmsMsg(result.getString("msg"));
                registration.setSmsSendTime(DateUtils.getNowDate());
                activityRegistrationService.updateRdActivityRegistration(registration);

                // 受短信平台限制，每分钟发送条数不得多于200条，故等待300ms
                try {
                    sleep(300);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }

            });
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("发送短信异常");
        }
    }

}
