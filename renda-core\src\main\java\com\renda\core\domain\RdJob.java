package com.renda.core.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.renda.common.annotation.Excel;
import com.renda.common.core.domain.BaseEntity;

import java.util.Date;
import java.util.List;

/**
 * 履职工作对象 rd_job
 *
 * <AUTHOR>
 * @date 2023-11-12
 */
public class RdJob extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 人大代表姓名 */
    @Excel(name = "代表姓名")
    private String deputyName;

    /** 履职类型 */
    @Excel(name = "履职类型")
    private String jobName;

    /** 组织单位 */
    @Excel(name = "组织单位")
    private String organizer;

    /** $column.columnComment */
    private Long id;

    /** 人民代表ID */
    private Long deputyId;

    /** 履职工作类型 */
    private String jobType;

    /** 起始时间 */
    @Excel(name = "起始时间", width = 20, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date beginDate;

    /** 截止时间 */
    @Excel(name = "截止时间", width = 20, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 工作内容 */
    @Excel(name = "工作内容")
    private String content;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "提交时间", width = 20, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 附件 */
    private List<RdJobAttachment> attachments;

    /** 人大代表头像 */
    private String deputyAvatar;

    public void setId(Long id)
    {
        this.id = id;
    }
    public Long getId()
    {
        return id;
    }
    public void setDeputyId(Long deputyId)
    {
        this.deputyId = deputyId;
    }
    public Long getDeputyId()
    {
        return deputyId;
    }
    public String getJobType() {
        return jobType;
    }
    public void setJobType(String jobType) {
        this.jobType = jobType;
    }
    public String getOrganizer() {
        return organizer;
    }
    public void setOrganizer(String organizer) {
        this.organizer = organizer;
    }
    public Date getBeginDate() {
        return beginDate;
    }
    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }
    public Date getEndDate() {
        return endDate;
    }
    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }
    public void setTitle(String title)
    {
        this.title = title;
    }
    public String getTitle()
    {
        return title;
    }
    public void setContent(String content)
    {
        this.content = content;
    }
    public String getContent()
    {
        return content;
    }
    public String getDeputyName() {
        return deputyName;
    }
    public void setDeputyName(String deputyName) {
        this.deputyName = deputyName;
    }
    public String getDeputyAvatar() {
        return deputyAvatar;
    }
    public void setDeputyAvatar(String deputyAvatar) {
        this.deputyAvatar = deputyAvatar;
    }
    public List<RdJobAttachment> getAttachments() {
        return attachments;
    }
    public void setAttachments(List<RdJobAttachment> attachments) {
        this.attachments = attachments;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deputyId", getDeputyId())
            .append("deputyName", getDeputyName())
            .append("deputyAvatar", getDeputyAvatar())
            .append("jobType", getJobType())
            .append("organizer", getOrganizer())
            .append("beginDate", getBeginDate())
            .append("endDate", getEndDate())
            .append("title", getTitle())
            .append("content", getContent())
            .append("createTime", getCreateTime())
            .toString();
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    @Override
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
