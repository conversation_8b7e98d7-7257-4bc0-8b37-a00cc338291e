package com.renda.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.renda.common.annotation.Log;
import com.renda.common.core.controller.BaseController;
import com.renda.common.core.domain.AjaxResult;
import com.renda.common.enums.BusinessType;
import com.renda.core.domain.RdMass;
import com.renda.core.service.IRdMassService;
import com.renda.common.utils.poi.ExcelUtil;
import com.renda.common.core.page.TableDataInfo;

/**
 * 群众管理Controller
 *
 * <AUTHOR>
 * @date 2023-10-30
 */
@RestController
@RequestMapping("/renda/mass")
public class RdMassController extends BaseController
{
    @Autowired
    private IRdMassService rdMassService;

    /**
     * 查询群众管理列表
     */
    @PreAuthorize("@ss.hasPermi('renda:mass:list')")
    @GetMapping("/list")
    public TableDataInfo list(RdMass rdMass)
    {
        startPage();
        List<RdMass> list = rdMassService.selectRdMassList(rdMass);
        return getDataTable(list);
    }

    /**
     * 导出群众管理列表
     */
    @PreAuthorize("@ss.hasPermi('renda:mass:export')")
    @Log(title = "群众管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RdMass rdMass)
    {
        List<RdMass> list = rdMassService.selectRdMassList(rdMass);
        ExcelUtil<RdMass> util = new ExcelUtil<RdMass>(RdMass.class);
        util.exportExcel(response, list, "群众管理数据");
    }

    /**
     * 获取群众管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('renda:mass:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rdMassService.selectRdMassById(id));
    }

    /**
     * 新增群众管理
     */
    @PreAuthorize("@ss.hasPermi('renda:mass:add')")
    @Log(title = "群众管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RdMass rdMass)
    {
        return toAjax(rdMassService.insertRdMass(rdMass));
    }

    /**
     * 修改群众管理
     */
    @PreAuthorize("@ss.hasPermi('renda:mass:edit')")
    @Log(title = "群众管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RdMass rdMass)
    {
        return toAjax(rdMassService.updateRdMass(rdMass));
    }

    /**
     * 删除群众管理
     */
    @PreAuthorize("@ss.hasPermi('renda:mass:remove')")
    @Log(title = "群众管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rdMassService.deleteRdMassByIds(ids));
    }
}
