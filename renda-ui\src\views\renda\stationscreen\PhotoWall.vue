<template>
  <div class="photo-wall">
    <app-header :showBackBtn="true" />

    <main class="content">
      <div class="background-elements">
        <div class="circle circle-1"></div>
        <div class="circle circle-2"></div>
        <div class="circle circle-3"></div>
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner">
          <i class="el-icon-loading"></i>
          <span>正在加载排班数据...</span>
        </div>
      </div>

      <div class="page-header">
        <h1>人大代表联络站</h1>
        <p class="page-subtitle">{{ currentDate }} · 值班信息</p>
      </div>

      <div class="main-section">
        <!-- 今日进站代表 -->
        <div class="today-section">
          <div class="section-header">
            <div class="header-icon">
              <i class="el-icon-user-solid"></i>
            </div>
            <div class="header-text">
              <h2>今日进站代表</h2>
              <span class="header-subtitle">Jin Ri Jin Zhan Dai Biao</span>
            </div>
          </div>

          <div class="representative-card" @click="goToDetail(todayRepresentative.id)">
            <div class="card-photo">
              <img
                :src="getImageUrl(todayRepresentative.avatar)"
                alt="代表照片"
                class="photo"
                @error="handleImageError"
              />
              <div class="photo-overlay">
                <span class="view-detail">查看详情</span>
              </div>
            </div>
            <div class="card-info">
              <h3 class="rep-name">{{ todayRepresentative.name }}</h3>
              <div class="rep-details">
                <div class="detail-item">
                  <span class="detail-label">单位：</span>
                  <span class="detail-value">{{ todayRepresentative.unit }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">职务：</span>
                  <span class="detail-value">{{ todayRepresentative.position }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">电话：</span>
                  <span class="detail-value">{{ todayRepresentative.phone }}</span>
                </div>
                <div class="detail-item duty">
                  <span class="detail-label">职责：</span>
                  <span class="detail-value">倾听群众心声、收集民情民意，当好政策宣传员、诉求传递者，推动民生问题解决与基层治理优化。</span>
                </div>
              </div>
            </div>
            <div class="card-qr">
              <div class="qr-container">
                <img
                  :src="getQRCodeUrl(todayRepresentative.qrcodeUrl)"
                  alt="二维码"
                  class="qr-code"
                  @error="handleQRCodeError"
                />
                <p class="qr-text">扫码提建议</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 今日工作人员 -->
        <div class="today-section">
          <div class="section-header">
            <div class="header-icon">
              <i class="el-icon-s-custom"></i>
            </div>
            <div class="header-text">
              <h2>今日工作人员</h2>
              <span class="header-subtitle">Jin Ri Gong Zuo Ren Yuan</span>
            </div>
          </div>

          <div class="staff-card">
            <div class="card-photo">
              <img
                :src="getImageUrl(todayStaff.avatar)"
                alt="工作人员照片"
                class="photo"
                @error="handleImageError"
              />
            </div>
            <div class="card-info">
              <h3 class="staff-name">{{ todayStaff.name }}</h3>
              <div class="staff-details">
                <div class="detail-item">
                  <span class="detail-label">单位：</span>
                  <span class="detail-value">{{ todayStaff.unit }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">职务：</span>
                  <span class="detail-value">{{ todayStaff.position }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">电话：</span>
                  <span class="detail-value">{{ todayStaff.phone }}</span>
                </div>
                <div class="detail-item duty">
                  <span class="detail-label">职责：</span>
                  <span class="detail-value">协助人大代表与群众沟通，引导群众有序表达诉求；联络对接相关部门，跟进问题办理进度，及时向人大代表和群众反馈情况，保障信息传递畅通。</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 排班表 -->
      <div class="schedule-section">
        <div class="section-header">
          <div class="header-icon">
            <i class="el-icon-date"></i>
          </div>
          <div class="header-text">
            <h2>排班表</h2>
            <span class="header-subtitle">Pai Ban Biao</span>
          </div>
        </div>

        <div class="schedule-container" ref="scheduleContainer">
          <div class="schedule-grid">
            <div class="schedule-header">
              <div class="header-cell">日期</div>
              <div class="header-cell">星期</div>
              <div class="header-cell">值班代表</div>
              <div class="header-cell">工作人员</div>
              <div class="header-cell">状态</div>
            </div>

            <!-- 有数据时显示排班表 -->
            <div
              v-for="(day, index) in weeklySchedule"
              :key="index"
              class="schedule-row"
              :class="{ 'today-row': day.isToday }"
              :ref="day.isToday ? 'todayRow' : null"
            >
              <div class="schedule-cell date-cell">
                <span class="date-number">{{ day.date }}</span>
                <span class="month-text">{{ day.month }}</span>
              </div>
              <div class="schedule-cell">{{ day.weekday }}</div>
              <div class="schedule-cell representative-cell">
                <div class="person-info">
                  <span class="person-name clickable" @click="goToDetail(day.representativeId)">{{ day.representative }}</span>
                  <span class="person-unit">{{ day.repUnit }}</span>
                </div>
              </div>
              <div class="schedule-cell staff-cell">
                <div class="person-info">
                  <span class="person-name">{{ day.staff }}</span>
                  <span class="person-unit">{{ day.staffUnit }}</span>
                </div>
              </div>
              <div class="schedule-cell status-cell">
                <span class="status-badge" :class="day.statusClass">{{ day.status }}</span>
              </div>
            </div>

            <!-- 无数据时的提示 -->
            <div v-if="!loading && weeklySchedule.length === 0" class="no-data-row">
              <div class="no-data-text">
                <i class="el-icon-warning"></i>
                <span>暂无排班数据</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 右下角返回按钮 -->
    <div class="back-button" @click="goBack">
      <span>返回</span>
    </div>


  </div>
</template>

<script>
import { mapState } from 'vuex'
import AppHeader from '@/components/StationScreen/AppHeader.vue'
import { getScheduleData } from '@/api/renda/stationscreen'

export default {
  name: 'PhotoWall',
  components: {
    AppHeader
  },
  data() {
    return {
      stationId: null,
      loading: false,
      refreshTimer: null, // 防抖定时器
      lastRefreshTime: 0, // 上次刷新时间
      isInitialized: false, // 标记是否已经初始化
      todayRepresentative: {
        id: null,
        name: '暂无安排',
        unit: '',
        position: '',
        phone: '',
        avatar: '',
        qrcodeUrl: ''
      },
      todayStaff: {
        id: null,
        name: '暂无安排',
        unit: '',
        position: '',
        phone: '',
        avatar: ''
      },
      weeklySchedule: [],
      todayIndex: 0 // 今日在排班表中的索引
    }
  },
  computed: {
    ...mapState(['representatives']),
    currentDate() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const date = String(now.getDate()).padStart(2, '0')
      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      const weekday = weekdays[now.getDay()]
      return `${year}年${month}月${date}日 ${weekday}`
    }
  },
  methods: {
    // 处理键盘事件
    handleKeydown(event) {
      // ESC键返回首页
      if (event.key === 'Escape') {
        this.goToHome()
      }
      // T键滚动到今天
      if (event.key === 'T' || event.key === 't') {
        this.scrollToToday()
      }
    },

    // 处理页面可见性变化
    handleVisibilityChange() {
      if (!document.hidden) {
        this.refreshData()
      }
    },

    // 处理窗口获得焦点
    handleWindowFocus() {
      this.refreshData()
    },

    // 统一的数据刷新方法（带防抖）
    refreshData(force = false) {
      console.log('refreshData 被调用，force:', force, 'isInitialized:', this.isInitialized)
      
      const now = Date.now()
      const timeSinceLastRefresh = now - this.lastRefreshTime

      // 如果距离上次刷新少于3秒且非强制刷新，则防抖
      if (!force && timeSinceLastRefresh < 3000) {
        console.log('防抖中，延迟刷新')

        // 清除之前的定时器
        if (this.refreshTimer) {
          clearTimeout(this.refreshTimer)
        }

        // 设置新的定时器
        this.refreshTimer = setTimeout(() => {
          this.doRefresh()
        }, 3000 - timeSinceLastRefresh)

        return
      }

      this.doRefresh()
    },

    // 执行实际的数据刷新
    doRefresh() {
      console.log('doRefresh 执行数据刷新')
      this.lastRefreshTime = Date.now()

      // 清除定时器
      if (this.refreshTimer) {
        clearTimeout(this.refreshTimer)
        this.refreshTimer = null
      }

      this.getScheduleData()
    },

    goToHome() {
      this.$router.push({ path: "StationScreenHome"});
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    goToDetail(id) {
      if (id) {
        this.$router.push({
        name: 'RepresentativeDetail',
        query: {
          deputyId: id
        }
      })
      }
    },

    // 获取排班数据
    async getScheduleData() {
      if (!this.stationId) {
        console.warn('联络站ID为空，无法获取排班数据')
        return
      }

      try {
        this.loading = true
        
        // 计算日期范围：今天前15天到后15天
        const today = new Date()
        const startDate = new Date(today)
        startDate.setDate(today.getDate() - 15)
        const endDate = new Date(today)
        endDate.setDate(today.getDate() + 15)
        
        // 格式化日期参数
        const formatDate = (date) => {
          const year = date.getFullYear()
          const month = String(date.getMonth() + 1).padStart(2, '0')
          const day = String(date.getDate()).padStart(2, '0')
          return `${year}-${month}-${day}`
        }
        
        const startDateStr = formatDate(startDate)
        const endDateStr = formatDate(endDate)
        
        const response = await getScheduleData(this.stationId, startDateStr, endDateStr)

        if (response.code === 200) {
          const data = response.data

          // 更新排班表数据
          this.weeklySchedule = data.weeklySchedule || []
          
          console.log('获取到的排班数据:', this.weeklySchedule)
          console.log('排班数据长度:', this.weeklySchedule.length)
          
          // 找到今天的索引位置 - 使用后端返回的isToday字段
          this.todayIndex = this.weeklySchedule.findIndex(item => item.isToday === true)
          
          // 如果没找到今天的数据，默认设置为第15个位置（中间位置）
          if (this.todayIndex === -1) {
            this.todayIndex = 15
            console.warn('未找到今天的排班数据，使用默认位置')
            console.log('查找今天数据的条件: isToday === true')
            console.log('排班数据示例:', this.weeklySchedule.slice(0, 3))
          } else {
            console.log('找到今天的排班数据，索引:', this.todayIndex)
            console.log('今天的数据:', this.weeklySchedule[this.todayIndex])
          }

          // 更新今日值班信息
          if (data.todayInfo) {
            if (data.todayInfo.todayRepresentative) {
              this.todayRepresentative = {
                id: data.todayInfo.todayRepresentative.id,
                name: data.todayInfo.todayRepresentative.name || '暂无安排',
                unit: data.todayInfo.todayRepresentative.unit || '',
                position: data.todayInfo.todayRepresentative.position || '',
                phone: data.todayInfo.todayRepresentative.phone || '',
                avatar: data.todayInfo.todayRepresentative.avatar || '',
                qrcodeUrl: data.todayInfo.todayRepresentative.qrcodeUrl || ''
              }
            }

            if (data.todayInfo.todayStaff) {
              this.todayStaff = {
                id: data.todayInfo.todayStaff.id,
                name: data.todayInfo.todayStaff.name || '暂无安排',
                unit: data.todayInfo.todayStaff.unit || '',
                position: data.todayInfo.todayStaff.position || '',
                phone: data.todayInfo.todayStaff.phone || '',
                avatar: data.todayInfo.todayStaff.avatar || ''
              }
            }
          }

          // 数据加载完成后，滚动到今天的位置
          this.$nextTick(() => {
            this.scrollToToday()
          })

        } else {
          console.error('获取排班数据失败:', response.msg)
          this.$message.error('获取排班数据失败：' + response.msg)
        }
      } catch (error) {
        console.error('获取排班数据异常:', error)
        this.$message.error('获取排班数据异常')
      } finally {
        this.loading = false
      }
    },

    // 滚动到今天的位置
    scrollToToday() {
      const scheduleContainer = this.$refs.scheduleContainer
      const todayRows = this.$refs.todayRow
      
      if (scheduleContainer && todayRows && todayRows.length > 0) {
        // todayRow在v-for中是数组，取第一个元素
        const todayRow = todayRows[0]
        // 计算滚动位置（留一些边距）
        const scrollTop = todayRow.offsetTop - scheduleContainer.offsetTop + 10
        
        // 平滑滚动到目标位置
        scheduleContainer.scrollTo({
          top: scrollTop,
          behavior: 'smooth'
        })
        
        console.log('滚动到今天的位置，scrollTop:', scrollTop)
      } else {
        console.warn('无法滚动到今天的位置:', {
          hasContainer: !!scheduleContainer,
          hasTodayRows: !!todayRows,
          todayRowsLength: todayRows ? todayRows.length : 0
        })
      }
    },

    // 获取图片URL
    getImageUrl(avatar) {
      // 如果有头像路径，返回完整URL
      if (avatar) {
        // 如果是完整URL，直接返回
        if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
          return avatar
        }
        // 如果是相对路径，拼接基础URL
        return process.env.VUE_APP_BASE_API + avatar
      }
      // 没有头像时使用默认图片
      return require('@/assets/deputy/someone.jpg')
    },

    // 获取二维码URL
    getQRCodeUrl(qrcodeUrl) {
      // 如果有二维码路径，返回完整URL
      if (qrcodeUrl) {
        // 如果是完整URL，直接返回
        if (qrcodeUrl.startsWith('http://') || qrcodeUrl.startsWith('https://')) {
          return qrcodeUrl
        }
        // 如果是相对路径，拼接基础URL
        return process.env.VUE_APP_BASE_API + qrcodeUrl
      }
      // 没有二维码时使用默认图片
      return require('@/assets/deputy/qrcode.png')
    },

    // 处理图片加载错误
    handleImageError(event) {
      console.warn('图片加载失败，使用默认图片')
      event.target.src = require('@/assets/deputy/someone.jpg')
    },

    // 处理二维码加载错误
    handleQRCodeError(event) {
      console.warn('二维码加载失败，使用默认二维码')
      event.target.src = require('@/assets/deputy/qrcode.png')
    }
  },
  mounted() {
    this.$nextTick(() => {
      // 禁用右键菜单
      document.oncontextmenu = function() {
        return false;
      };
    });

    // 获取联络站Id
    this.stationId = this.$route.query.stationId || '0'
    console.log('联络站ID:', this.stationId)

    // 首次加载数据（强制刷新）
    this.refreshData(true)
    this.isInitialized = true

    // 添加事件监听器
    document.addEventListener('keydown', this.handleKeydown)
    document.addEventListener('visibilitychange', this.handleVisibilityChange)
    window.addEventListener('focus', this.handleWindowFocus)
  },

  // keep-alive组件激活时
  activated() {
    // 只在已经初始化过且确实从其他页面返回时才刷新
    if (this.isInitialized) {
      this.refreshData()
    }
  },

  // 路由进入前
  beforeRouteEnter(to, from, next) {
    next(vm => {
      // 更新联络站ID，数据刷新由mounted钩子处理
      vm.stationId = to.query.stationId || '0'
    })
  },

  // 路由更新时
  beforeRouteUpdate(to, from, next) {
    this.stationId = to.query.stationId || '1'
    this.refreshData()
    next()
  },

  beforeDestroy() {
    // 清理定时器
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer)
      this.refreshTimer = null
    }

    // 重置初始化标记
    this.isInitialized = false

    // 移除事件监听器
    document.removeEventListener('keydown', this.handleKeydown)
    document.removeEventListener('visibilitychange', this.handleVisibilityChange)
    window.removeEventListener('focus', this.handleWindowFocus)
  }
}
</script>

<style lang="scss" scoped>
.photo-wall {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f8f8f8, #efefef);
  overflow: hidden;
  font-family: 'AL-R' !important;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  margin: 0;
  padding: 0;

  /* 隐藏滚动条 */
  &::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.content {
  flex: 1;
  padding: 80px 100px;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;

  /* 隐藏滚动条 */
  &::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;
  scrollbar-width: none;

  .loading-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    text-align: center;

    .loading-spinner {
      background: rgba(255, 255, 255, 0.95);
      padding: 30px 40px;
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);

      i {
        font-size: 32px;
        color: #d71718;
        margin-bottom: 15px;
      }

      span {
        display: block;
        font-size: 18px;
        color: #666;
        font-weight: 500;
      }
    }
  }

  .background-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 0;
    pointer-events: none;

    .circle {
      position: absolute;
      border-radius: 50%;
      opacity: 0.06;

      &.circle-1 {
        width: 600px;
        height: 600px;
        background: #d71718;
        top: -200px;
        left: -200px;
      }

      &.circle-2 {
        width: 500px;
        height: 500px;
        background: #d71718;
        bottom: 20%;
        right: 15%;
      }

      &.circle-3 {
        width: 300px;
        height: 300px;
        background: #d71718;
        bottom: 35%;
        left: 5%;
      }
    }

    .shape {
      position: absolute;
      opacity: 0.04;
      background: #d71718;

      &.shape-1 {
        width: 500px;
        height: 500px;
        transform: rotate(45deg);
        top: 15%;
        right: -300px;
      }

      &.shape-2 {
        width: 400px;
        height: 400px;
        transform: rotate(30deg);
        bottom: -50px;
        left: 30%;
      }
    }
  }

  .page-header {
    text-align: center;
    margin-bottom: 40px;
    position: relative;
    z-index: 1;

    h1 {
      color: #d71718;
      font-size: 64px;
      font-weight: 700;
      margin: 0 0 15px;
      letter-spacing: 2px;
      font-family: 'AL-BL' !important;
    }

    .page-subtitle {
      color: #666;
      font-size: 28px;
      margin: 0;
      letter-spacing: 1px;
      font-family: 'AL-L' !important;
    }
  }

  .main-section {
    display: flex;
    gap: 40px;
    margin-bottom: 40px;
    position: relative;
    z-index: 1;

    .today-section {
      flex: 1;

      .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 25px;

        .header-icon {
          width: 60px;
          height: 60px;
          background: linear-gradient(135deg, #d71718, #a01012);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20px;
          box-shadow: 0 8px 25px rgba(215, 23, 24, 0.3);

          i {
            font-size: 28px;
            color: #fff;
          }
        }

        .header-text {
          h2 {
            color: #d71718;
            font-size: 32px;
            margin: 0;
            font-weight: 600;
            font-family: 'AL-B' !important;
          }

          .header-subtitle {
            color: #666;
            font-size: 16px;
            font-style: italic;
          }
        }
      }

      .representative-card,
      .staff-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 24px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        display: flex;
        align-items: center;
        gap: 30px;
        transition: all 0.3s ease;
        border-left: 8px solid #d71718;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
        }

        .card-photo {
          position: relative;
          flex-shrink: 0;

          .photo {
            width: 180px;
            height: 240px;
            object-fit: cover;
            border-radius: 16px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            background: linear-gradient(135deg, #f5f5f5, #e9e9e9);
            display: flex;
            align-items: center;
            justify-content: center;

            // 图片加载失败时的占位符
            &::before {
              content: '';
              position: absolute;
              width: 60px;
              height: 60px;
              background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ccc"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>') center/cover;
              opacity: 0;
              transition: opacity 0.3s ease;
            }

            &[src=""], &:not([src]) {
              &::before {
                opacity: 1;
              }
            }
          }

          .photo-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(215, 23, 24, 0.8);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;

            .view-detail {
              color: #fff;
              font-size: 16px;
              font-weight: 600;
            }
          }
        }

        &.representative-card .card-photo:hover .photo-overlay {
          opacity: 1;
        }

        .card-info {
          flex: 1;

          .rep-name,
          .staff-name {
            color: #d71718;
            font-size: 28px;
            font-weight: 700;
            margin: 0 0 20px;
            font-family: 'AL-B' !important;
            text-align: left;
          }

          .rep-details,
          .staff-details {
            .detail-item {
              display: flex;
              align-items: center;
              margin-bottom: 12px;

              .detail-label {
                font-size: 18px;
                color: #666;
                font-weight: 500;
                min-width: 80px;
              }

              .detail-value {
                font-size: 18px;
                color: #333;
                font-weight: 600;
              }
            }
            .duty{
                align-items: start;
                .detail-value {
                  text-align: left;
                }
              }
          }
        }

        .card-qr {
          flex-shrink: 0;

          .qr-container {
            text-align: center;

            .qr-code {
              width: 120px;
              height: 120px;
              border-radius: 12px;
              box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
              margin-bottom: 10px;
              margin-right: 60px;
              background: linear-gradient(135deg, #f5f5f5, #e9e9e9);
              object-fit: cover;

              // 二维码加载失败时的占位符
              &::before {
                content: '';
                position: absolute;
                width: 40px;
                height: 40px;
                background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ccc"><path d="M3 11h8V3H3v8zm2-6h4v4H5V5zM3 21h8v-8H3v8zm2-6h4v4H5v-4zM13 3v8h8V3h-8zm6 6h-4V5h4v4zM19 13h2v2h-2zM13 13h2v2h-2zM15 15h2v2h-2zM13 17h2v2h-2zM15 19h2v2h-2zM17 17h2v2h-2zM17 13h2v2h-2zM19 15h2v2h-2z"/></svg>') center/cover;
                opacity: 0;
                transition: opacity 0.3s ease;
              }

              &[src=""], &:not([src]) {
                &::before {
                  opacity: 1;
                }
              }
            }

            .qr-text {
              color: #666;
              font-size: 14px;
              margin: 0;
              font-weight: 500;
              margin-right: 60px;
            }
          }
        }
      }

      .representative-card {
        cursor: pointer;
      }
    }
  }

  .schedule-section {
    position: relative;
    z-index: 1;

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 25px;

      .header-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #d71718, #a01012);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
        box-shadow: 0 8px 25px rgba(215, 23, 24, 0.3);

        i {
          font-size: 28px;
          color: #fff;
        }
      }

      .header-text {
        h2 {
          color: #d71718;
          font-size: 32px;
          margin: 0;
          font-weight: 600;
          font-family: 'AL-B' !important;
        }

        .header-subtitle {
          color: #666;
          font-size: 16px;
          font-style: italic;
        }
      }
    }

    .schedule-container {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 24px;
      padding: 30px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
      max-height: 600px;
      overflow-y: auto;
      position: relative;

      /* 滚动条样式 */
      &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #d71718, #a01012);
        border-radius: 4px;
        transition: background 0.3s ease;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #a01012, #d71718);
      }

              .schedule-grid {
          .schedule-header {
            display: grid;
            grid-template-columns: 120px 100px 1fr 1fr 120px;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px 0;
            border-bottom: 3px solid #d71718;
            background: rgba(255, 255, 255, 0.98);
            position: sticky;
            top: -50px;
            z-index: 10;

            .header-cell {
              margin-left: 50px;
              font-size: 18px;
              font-weight: 700;
              color: #d71718;
              text-align: left;
              font-family: 'AL-B' !important;
            }
          }

          .no-data-row {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 40px 0;

            .no-data-text {
              display: flex;
              flex-direction: column;
              align-items: center;
              color: #999;

              i {
                font-size: 48px;
                margin-bottom: 15px;
                color: #ccc;
              }

              span {
                font-size: 18px;
                font-weight: 500;
              }
            }
          }

          .schedule-row {
            display: grid;
            grid-template-columns: 120px 100px 1fr 1fr 120px;
            gap: 20px;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;

            &:hover {
              background: rgba(215, 23, 24, 0.02);
              border-radius: 12px;
            }

            &.today-row {
              background: linear-gradient(135deg, rgba(215, 23, 24, 0.1), rgba(215, 23, 24, 0.05));
              border-radius: 12px;
              font-weight: 600;
            }

            &:last-child {
              border-bottom: none;
            }

            .schedule-cell {
            text-align: center;
            font-size: 16px;

            &.date-cell {
              display: flex;
              flex-direction: column;
              align-items: center;
              margin-left: -30px;

              .date-number {
                font-size: 24px;
                font-weight: 700;
                color: #d71718;
                line-height: 1;
              }

              .month-text {
                font-size: 12px;
                color: #666;
                margin-top: 2px;
              }
            }

            &.representative-cell,
            &.staff-cell {
              .person-info {
                text-align: left;
                margin-left: 50px;

                .person-name {
                  display: block;
                  font-weight: 600;
                  color: #333;
                  margin-bottom: 4px;

                  &.clickable {
                    cursor: pointer;
                    transition: color 0.3s ease;

                    &:hover {
                      color: #d71718;
                      text-decoration: underline;
                    }
                  }
                }

                .person-unit {
                  display: block;
                  font-size: 14px;
                  color: #666;
                }
              }
            }

            &.status-cell {
              .status-badge {
                padding: 6px 12px;
                border-radius: 20px;
                font-size: 14px;
                font-weight: 600;

                &.completed {
                  background: linear-gradient(135deg, #28a745, #20c997);
                  color: #fff;
                }

                &.active {
                  background: linear-gradient(135deg, #d71718, #a01012);
                  color: #fff;
                  animation: pulse 2s infinite;
                }

                &.pending {
                  background: linear-gradient(135deg, #ffc107, #ffb300);
                  color: #fff;
                }
              }
            }
          }
        }
      }
    }
  }
}

.back-button {
  position: fixed;
  bottom: 40px;
  right: 40px;
  width: 80px;
  height: 80px;
  background: rgba(215, 23, 24, 0.9);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(215, 23, 24, 0.3);
  backdrop-filter: blur(10px);
  z-index: 1000;

  &:hover {
    background: rgba(215, 23, 24, 1);
    transform: scale(1.1);
    box-shadow: 0 8px 30px rgba(215, 23, 24, 0.5);
  }

  .back-icon {
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 8px 12px 8px 0;
    border-color: transparent #fff transparent transparent;
    margin-bottom: 4px;
  }

  span {
    color: #fff;
    font-size: 12px;
    font-weight: 500;
    font-family: 'AL-R';
  }
}



/* 4K分辨率适配 */
@media (min-width: 3000px) {
  .photo-wall {
    .back-button {
      width: 120px;
      height: 120px;
      bottom: 60px;
      right: 60px;

      .back-icon {
        border-width: 12px 18px 12px 0;
        margin-bottom: 6px;
      }

      span {
        font-size: 24px;
      }
    }
  }



  .content {
    padding: 80px 400px;

    .loading-container {
      .loading-spinner {
        padding: 50px 60px;
        border-radius: 30px;

        i {
          font-size: 48px;
          margin-bottom: 25px;
        }

        span {
          font-size: 28px;
        }
      }
    }

    .page-header {
      margin-bottom: 60px;

      h1 {
        font-size: 96px;
        font-weight: 500;
        margin: 0 0 25px;
        letter-spacing: 3px;
      }

      .page-subtitle {
        font-size: 42px;
        letter-spacing: 2px;
      }
    }

    .main-section {
      gap: 60px;
      margin-bottom: 60px;

      .today-section {
        .section-header {
          margin-bottom: 35px;

          .header-icon {
            width: 80px;
            height: 80px;
            margin-right: 30px;

            i {
              font-size: 40px;
            }
          }

          .header-text {
            h2 {
              font-size: 48px;
            }

            .header-subtitle {
              font-size: 24px;
            }
          }
        }

        .representative-card,
        .staff-card {
          border-radius: 32px;
          padding: 40px;
          gap: 40px;
          border-left-width: 12px;

          .card-photo {
            .photo {
              width: 240px;
              height: 320px;
              border-radius: 24px;

              &::before {
                width: 80px;
                height: 80px;
              }
            }

            .photo-overlay {
              border-radius: 24px;

              .view-detail {
                font-size: 24px;
              }
            }
          }

          .card-info {
            .rep-name,
            .staff-name {
              font-size: 48px;
              margin: 0 20px 30px;
              text-align: left;
            }

            .rep-details,
            .staff-details {
              .detail-item {
                margin-bottom: 18px;

                .detail-label {
                  font-size: 26px;
                  min-width: 120px;
                }

                .detail-value {
                  font-size: 26px;
                }
              }
            }
          }

          .card-qr {
            .qr-container {
              .qr-code {
                width: 180px;
                height: 180px;
                border-radius: 18px;
                margin-bottom: 15px;
                margin-right: 60px;

                &::before {
                  width: 60px;
                  height: 60px;
                }
              }

              .qr-text {
                font-size: 20px;
              }
            }
          }
        }
      }
    }

    .schedule-section {
      .section-header {
        margin-bottom: 35px;

        .header-icon {
          width: 80px;
          height: 80px;
          margin-right: 30px;

          i {
            font-size: 40px;
          }
        }

        .header-text {
          h2 {
            font-size: 48px;
          }

          .header-subtitle {
            font-size: 24px;
          }
        }
      }

      .schedule-container {
        border-radius: 32px;
        padding: 50px;
        max-height: 820px;
        overflow-y: auto;
        position: relative;

        /* 4K分辨率滚动条样式 */
        &::-webkit-scrollbar {
          width: 12px;
        }

        &::-webkit-scrollbar-track {
          background: rgba(0, 0, 0, 0.1);
          border-radius: 6px;
        }

        &::-webkit-scrollbar-thumb {
          background: linear-gradient(135deg, #d71718, #a01012);
          border-radius: 6px;
          transition: background 0.3s ease;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(135deg, #a01012, #d71718);
        }

        .schedule-grid {
          .schedule-header {
            grid-template-columns: 180px 140px 1fr 1fr 180px;
            gap: 30px;
            margin-bottom: 30px;
            padding: 25px 0;
            border-bottom-width: 4px;
            background: rgba(255, 255, 255, 0.98);
            position: sticky;
            top: -50px;
            z-index: 10;

            .header-cell {
              font-size: 28px;
            }
          }

          .no-data-row {
            padding: 60px 0;

            .no-data-text {
              i {
                font-size: 72px;
                margin-bottom: 25px;
              }

              span {
                font-size: 28px;
              }
            }
          }

          .schedule-row {
            grid-template-columns: 180px 140px 1fr 1fr 180px;
            gap: 30px;
            padding: 15px 0;

            &:last-child {
              border-bottom: none;
            }

            .schedule-cell {
              font-size: 24px;

              &.date-cell {
                .date-number {
                  font-size: 36px;
                }

                .month-text {
                  font-size: 18px;
                  margin-top: 4px;
                }
              }

              &.representative-cell,
              &.staff-cell {
                .person-info {
                  .person-name {
                    margin-bottom: 8px;

                    &.clickable {
                      cursor: pointer;
                      transition: color 0.3s ease;

                      &:hover {
                        color: #d71718;
                        text-decoration: underline;
                      }
                    }
                  }

                  .person-unit {
                    font-size: 20px;
                  }
                }
              }

              &.status-cell {
                .status-badge {
                  padding: 12px 20px;
                  font-size: 20px;
                }
              }
            }
          }
        }
      }
    }
  }
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}


</style>
