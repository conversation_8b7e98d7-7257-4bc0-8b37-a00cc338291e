package com.renda.core.controller;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import com.renda.common.config.WxMaConfiguration;
import com.renda.common.core.controller.BaseController;
import com.renda.common.core.domain.AjaxResult;
import com.renda.common.core.domain.entity.SysDept;
import com.renda.common.core.domain.entity.SysDictData;
import com.renda.common.core.domain.entity.SysUser;
import com.renda.common.core.domain.model.LoginUser;
import com.renda.common.core.page.TableDataInfo;
import com.renda.common.utils.SecurityUtils;
import com.renda.common.utils.bean.BeanUtils;
import com.renda.core.domain.*;
import com.renda.core.domain.vo.*;
import com.renda.core.mapper.RdDeputyMapper;
import com.renda.core.service.*;
import com.renda.framework.web.service.TokenService;
import com.renda.system.domain.SysNotice;
import com.renda.system.service.ISysConfigService;
import com.renda.system.service.ISysDeptService;
import com.renda.system.service.ISysDictDataService;
import com.renda.system.service.ISysNoticeService;
import me.chanjar.weixin.common.error.WxErrorException;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 小程序人大端Controller
 *
 * <AUTHOR>
 * @date 2023-10-23
 */
@RestController
@RequestMapping("/renda/api/deputy")
public class RdApiDeputyController extends BaseController
{

    /**
     * 哈密市人大代表智慧管理平台小程序-人大代表端
     */
    private static final String appid = "wx9278f25e7086ef82"; // 正式

    @Autowired
    private TokenService tokenService;

    @Autowired
    private RdDeputyMapper rdDeputyMapper;

    @Autowired
    private ISysNoticeService noticeService;

    @Autowired
    private IRdDeputyService deputyService;

    @Autowired
    private IRdAdviceService adviceService;

    @Autowired
    private IRdFeedbackService feedbackService;

    @Autowired
    private IRdJobService JobService;

    @Autowired
    private IRdActivityService activityService;

    @Autowired
    private IRdActivityRegistrationService activityRegistrationService;

    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private IRdRecomService recomService;

    @Autowired
    private IRdRecomDeputyService recomDeputyService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private IRdAdviceReportService adviceReportService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private IRdNoticeFeedbackService noticeFeedbackService;

    @Autowired
    private IRdFileService fileService;

    /***
     * 登录接口
     * @param loginInfo 登录信息
     * @return 登录结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginInfo loginInfo) throws WxErrorException {

        final WxMaService wxService = WxMaConfiguration.getMaService(appid);

        // 用phoneCode换取手机号
        WxMaPhoneNumberInfo phoneNoInfo = wxService.getUserService().getPhoneNoInfo(loginInfo.getPhoneCode());
        String phone = phoneNoInfo.getPhoneNumber();

        // 用loginCode换取openid
        WxMaJscode2SessionResult sessionInfo = wxService.getUserService().getSessionInfo(loginInfo.getLoginCode());
        String openid = sessionInfo.getOpenid();

        // 根据手机号获取人大代表信息
        RdDeputy deputy = rdDeputyMapper.selectDeputyByPhone(phone);
        if (ObjectUtils.isEmpty(deputy)) {
            return AjaxResult.error("本手机号未开通");
        } else {
            deputy.setOpenid(openid);
            deputy.setUpdateTime(new Date());
            rdDeputyMapper.updateRdDeputy(deputy);
        }

        // 获取token
        SysUser user = new SysUser();
        user.setUserName("deputy_" + phone);
        user.setPhonenumber(phone);
        user.setUserId(deputy.getId());
        LoginUser loginUser = new LoginUser();
        loginUser.setUserId(deputy.getId());
        loginUser.setUser(user);
        String token = tokenService.createTokenWx(loginUser);

        // 返回结果
        AjaxResult result = AjaxResult.success();
        result.put("userInfo", deputy);
        result.put("token", token);

        return result;

    }

    /***
     * 获取人大动态接口-前5条
     * @return 人大动态信息
     */
    @PostMapping("/getNews")
    public AjaxResult getNews(@RequestBody SysNotice notice) {
        List<SysNotice> news = noticeService.getNews(notice);
        return AjaxResult.success(news);
    }

    /***
     * 获取人大动态接口-所有
     * @return 人大动态信息
     */
    @PostMapping("/getAllNews")
    public AjaxResult getAllNews(@RequestBody SysNotice notice) {
        List<SysNotice> news = noticeService.getAllNews(notice);
        return AjaxResult.success(news);
    }

    /***
     * 获取人大动态接口-所有
     * @return 人大动态信息
     */
    @PostMapping("/getNewsDetail")
    public AjaxResult getNewsDetail(@RequestBody SysNotice notice) {
        SysNotice news = noticeService.selectNoticeById(notice.getNoticeId());
        return AjaxResult.success(news);
    }

    /***
     * 获取意见建议接口-所有
     * @param req 搜索关键字
     * @return 意见建议列表
     */
    @PostMapping("/getAllAdvice")
    public AjaxResult getAllAdvice(@RequestBody Map<String, String> req) {
        // 获取当前登录人大代表信息
        Long userId = SecurityUtils.getLoginUser().getUserId();
        RdDeputy deputy = deputyService.selectRdDeputyById(userId);

        RdAdvice advice = new RdAdvice();
        advice.setTitle(req.get("keyword"));
        advice.setDeputyId(userId);
//        if (deputy.getType() == "2") {
//            // 查询所有人大代表收到的建议意见
//            advice.setDeputyId(null);
//        } else {
//            // 查询当前人大代表收到的建议意见
//            advice.setDeputyId(userId);
//        }
        return AjaxResult.success(adviceService.selectAdviceExtList(advice));
    }

    /***
     * 获取建议意见详情接口
     * @return 建议意见详情
     */
    @PostMapping("/getAdviceDetail")
    public AjaxResult getAdviceDetail(@RequestBody AdviceInfoVO adviceInfoVO) {

        // 获取建议详情
        AdviceInfoVO adviceInfo = adviceService.selectAdviceInfoById(adviceInfoVO.getAdviceId());
        if (ObjectUtils.isEmpty(adviceInfo)) {
            return AjaxResult.error("未找到该建议");
        }

        // 获取人大代表信息
        RdDeputy deputy = deputyService.selectRdDeputyById(adviceInfo.getDeputyId());
        if (ObjectUtils.isEmpty(deputy)) {
            return AjaxResult.error("未找到人大代表信息");
        }
        DeputyInfoVO deputyInfo = new DeputyInfoVO();
        BeanUtils.copyBeanProp(deputyInfo, deputy);

        // 获取建议回复列表
        List<FeedbackInfoVO> feedbackList = feedbackService.selectFeedbackListWithAttachs(adviceInfoVO.getAdviceId());

        AjaxResult result = AjaxResult.success();
        result.put("deputyInfo", deputyInfo);
        result.put("adviceInfo", adviceInfo);
        result.put("feedbackList", feedbackList);
        return result;

    }

    /***
     * 提交反馈接口
     * @param feedbackInfo2VO 意见建议
     * @return 提交结果
     */
    @PostMapping("/saveDeputyFeedback")
    public AjaxResult saveDeputyFeedback(@RequestBody FeedbackInfo2VO feedbackInfo2VO) {
        // 人大代表
        feedbackService.saveMassFeedback(feedbackInfo2VO, 2);
        return AjaxResult.success();
    }

    /***
     * 获取履职工作接口
     * @return 建议意见详情
     */
    @PostMapping("/getAllJob")
    public AjaxResult getAllJob(@RequestBody Map<String, String> req) {
        // 获取当前登录人大代表信息
        Long userId = SecurityUtils.getLoginUser().getUserId();
        RdDeputy deputy = deputyService.selectRdDeputyById(userId);

        RdJob Job = new RdJob();
        String keyword = req.get("keyword");
        Job.setTitle(keyword); // 同时like匹配3个字段
        if (deputy.getType() == "2") {
            // 查询所有人大代表收到的建议意见
            Job.setDeputyId(null);
        } else {
            // 查询当前人大代表收到的建议意见
            Job.setDeputyId(userId);
        }
        return AjaxResult.success(JobService.selectRdJobList(Job));
    }

    /***
     *  获取履职工作详情接口
     * @return 履职工作详情
     */
    @PostMapping("/getJobDetail")
    public AjaxResult getJobDetail(@RequestBody RdJob Job) {
        // 获取履职工作详情
        return AjaxResult.success(JobService.selectJobExtById(Job.getId()));
    }

    /***
     *  发布履职工作接口
     * @return 建议意见详情
     */
    @PostMapping("/saveJob")
    public AjaxResult saveJob(@RequestBody JobInfoVO Job) {
        JobService.saveJob(Job);
        return AjaxResult.success();
    }

    /***
     *  删除履职工作接口
     * @return 建议意见详情
     */
    @PostMapping("/deleteJob")
    public AjaxResult deleteJob(@RequestBody RdJob Job) {
        JobService.deleteJobById(Job.getId());
        return AjaxResult.success();
    }

    /***
     * 获取活动信息接口
     * @return 活动信息列表
     */
    @PostMapping("/getAllActivity")
    public AjaxResult getAllActivity(@RequestBody RdActivity activity) {
        Long userId = SecurityUtils.getLoginUser().getUserId();
        activity.setDeputyId(userId);
        return AjaxResult.success(activityService.selectActivityList(activity));
    }

    /***
     * 获取活动详情接口
     * @return 活动详情
     */
    @PostMapping("/getActivityDetail")
    public AjaxResult getActivityDetail(@RequestBody RdActivity activity) {

        // 获取活动信息
        activity = activityService.selectRdActivityById(activity.getId());

        // 获取当前登录代表报名信息
        Long userId = SecurityUtils.getLoginUser().getUserId();
        RdActivityRegistration activityRegistration = new RdActivityRegistration();
        activityRegistration.setActivityId(activity.getId());
        activityRegistration.setDeputyId(userId);
        activityRegistration = activityRegistrationService.selectRdActivityRegistrationByDeputyId(activityRegistration);

        AjaxResult result = AjaxResult.success();
        result.put("activityInfo", activity);
        result.put("activityRegistrationInfo", activityRegistration);
        return result;
    }

    /***
     * 获取活动信息接口-前5条
     * @return 活动信息列表
     */
    @PostMapping("/getActivities")
    public AjaxResult getActivities(@RequestBody RdActivity activity) {
        Long userId = SecurityUtils.getLoginUser().getUserId();
        activity.setDeputyId(userId);
        return AjaxResult.success(activityService.getActivities(activity));
    }

    /***
     * 活动报名请假接口
     * @param activityRegistration 活动报名请假信息
     * @return
     */
    @PostMapping("/registerActivity")
    public AjaxResult registerActivity(@RequestBody RdActivityRegistration activityRegistration) {
        activityService.registerActivity(activityRegistration);
        return AjaxResult.success();
    }

    /***
     * 获取代表统计信息接口
     * @return
     */
    @PostMapping("/getDeputyStatistics")
    public AjaxResult getDeputyStatistics() {
        return AjaxResult.success(deputyService.getDeputyStatistics());
    }

    /***
     * 获取代表活动接口
     * @return
     */
    @PostMapping("/getDeputyActivityList")
    public AjaxResult getDeputyActivityList() {
        Long userId = SecurityUtils.getLoginUser().getUserId();
        return AjaxResult.success(activityService.getDeputyActivityList(userId));
    }

    /***
     * 获取后台数据字典接口
     * @return 后台数据字典信息
     */
    @PostMapping("/loadDicts")
    public AjaxResult loadDicts(@RequestBody SysDictData sysDictData) {
        return AjaxResult.success(dictDataService.selectSmallDictDataList(sysDictData));
    }

    /***
     * 获取后台数据字典接口
     * @return 后台数据字典信息
     */
    @PostMapping("/getAllRecom")
    public AjaxResult getAllRecom(@RequestBody RdRecom recom) {
        // 获取当前登录人大代表信息
        Long userId = SecurityUtils.getLoginUser().getUserId();
        recom.setDeputyId(userId);
        List<RdRecom> list = recomService.selectRdRecomList(recom);
        return AjaxResult.success(list);
    }

    /***
     * 保存代表建议反馈接口
     */
    @PostMapping("/saveRecomReply")
    public AjaxResult saveRecomReply(@RequestBody RdRecomDeputy recomDeputy) {
        // 获取当前登录人大代表信息
        Long userId = SecurityUtils.getLoginUser().getUserId();
        if (recomDeputy.getDeputyId().intValue() != userId.intValue()) {
            return AjaxResult.error("非法操作");
        }
        recomDeputyService.updateRdRecomDeputy(recomDeputy);
        return AjaxResult.success();
    }

    /***
     * 获取待办信息接口
     * @return 待办信息
     */
    @PostMapping("/getTodoInfo")
    public AjaxResult getTodoInfo() {
        // 获取当前登录人大代表信息
        Long userId = SecurityUtils.getLoginUser().getUserId();
        TodoInfoVO todoInfo = adviceService.getTodoInfo(userId);
        return AjaxResult.success(todoInfo);
    }

    /***
     * 获取系统参数
     * @param configKey 参数key
     * @return 系统参数
     */
    @GetMapping("/getSysConfig")
    public AjaxResult getSysConfig(String configKey) {
        return AjaxResult.success("OK", configService.selectConfigByKey(configKey));
    }

    /***
     * 建议上报接口
     * @return 上报结果
     */
    @PostMapping("/reportAdvice")
    public AjaxResult reportAdvice(@RequestBody AdviceReportVO adviceReportVO) {
        adviceReportService.reportAdvice(adviceReportVO);
        return AjaxResult.success();
    }

    /***
     * 获取上级机构信息接口
     * @return 级机构信息
     */
    @PostMapping("/getUpperDeptInfo")
    public AjaxResult getUpperDeptInfo() {
        AjaxResult result = AjaxResult.success();

        // 获取当前登录人大代表信息
        Long userId = SecurityUtils.getLoginUser().getUserId();
        // 根据用户ID获取代表信息
        RdDeputy deputy = deputyService.selectRdDeputyById(userId);
        if (deputy == null) {
            return AjaxResult.error("未找到代表信息");
        }
        // 获取当前代表上级机构信息
        SysDept dept = deptService.selectDeptById(deputy.getDeptId());
        result.put("deptInfo", dept.getParentName());

        // 获取上级机构所有代表信息
        List<RdDeputy> deputyList = deputyService.selectRdDeputyListByDeptId(dept.getParentId());
        result.put("deputyList", deputyList);

        return result;
    }

    /***
     * 获取人大动态接口-工作通知
     * @return 工作通知信息
     */
    @PostMapping("/getAllWorkNotice")
    public AjaxResult getAllWorkNotice(@RequestBody SysNotice notice) {
        List<SysNotice> news = noticeService.getAllWorkNotice(notice);
        return AjaxResult.success(news);
    }


    /***
     * 提交工作通知反馈接口
     * @param feedbackInfo2VO 反馈信息
     * @return 提交结果
     */
    @PostMapping("/saveNoticeFeedback")
    public AjaxResult saveNoticeFeedback(@RequestBody FeedbackInfo2VO feedbackInfo2VO) {
        noticeFeedbackService.saveNoticeFeedback(feedbackInfo2VO);
        return AjaxResult.success();
    }


    /***
     * 获取工作通知反馈接口
     * @param notice 工作通知信息
     * @return 反馈信息
     */
    @PostMapping("/getNoticeFeedback")
    public AjaxResult getNoticeFeedback(@RequestBody SysNotice notice) {
        return AjaxResult.success(noticeFeedbackService.getNoticeFeedback(notice));
    }

    /***
     * 获取政策法规文件
     * @param keyword 文件信息
     * @return 文件列表
     */
    @GetMapping("/getFileList")
    public TableDataInfo getFileList(String keyword) {
        RdFile file = new RdFile();
        file.setFileName(keyword);
        startPage();
        List<RdFile> list = fileService.selectRdFileList(file);
        return getDataTable(list);
    }



}
