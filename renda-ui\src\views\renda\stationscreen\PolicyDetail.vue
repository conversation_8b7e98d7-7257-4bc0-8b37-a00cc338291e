<template>
  <div class="policy-detail">
    <app-header :showBackBtn="true" />

    <main class="content" v-loading="loading" element-loading-text="正在加载政策详情...">
      <!-- 政策头部信息 -->
      <section class="policy-header" v-if="!loading && policyInfo.id">
        <div class="policy-meta">
          <div class="policy-tags">
            <span class="policy-tag" :class="getPolicyTypeClass(policyInfo.type)">{{ dict.type.rd_policy_type[policyInfo.type].label }}</span>
          </div>
        </div>

        <h1 class="policy-title">{{ policyInfo.title || '政策标题加载中...' }}</h1>

        <div class="policy-info">
          <div class="info-item">
            <span class="label">发布部门：</span>
            <span class="value">{{ policyInfo.department || '暂无' }}</span>
          </div>
          <div class="info-item">
            <span class="label">发布时间：</span>
            <span class="value">{{ policyInfo.publishTime || '暂无' }}</span>
          </div>
          <div class="info-item">
            <span class="label">实施时间：</span>
            <span class="value">{{ policyInfo.implementTime || '暂无' }}</span>
          </div>
          <div class="info-item">
            <span class="label">有效期：</span>
            <span class="value">{{ policyInfo.validPeriod || '暂无' }}</span>
          </div>
        </div>

        <div class="policy-stats">
          <div class="stat-item">
            <i class="el-icon-view"></i>
            <span>{{ policyInfo.viewCount }}次浏览</span>
          </div>
        </div>
      </section>

      <!-- 政策内容 -->
      <section class="policy-content" v-if="!loading && policyContent.length > 0">
        <!-- 政策正文 -->
        <div class="policy-text">
          <div class="text-section" v-for="section in policyContent" :key="section.id">
            <div class="section-content" v-html="section.content"></div>
          </div>
        </div>
      </section>

      <!-- 空状态 -->
      <section class="empty-content" v-if="!loading && policyContent.length === 0 && policyInfo.id">
        <div class="empty-text">
          <i class="el-icon-document"></i>
          <p>暂无政策内容</p>
        </div>
      </section>

    </main>

    <!-- 返回顶部按钮 -->
    <div class="back-to-top" v-show="showBackToTop" @click="scrollToTop">
      <i class="el-icon-top"></i>
      <span>返回顶部</span>
    </div>

    <!-- 返回按钮 -->
    <div class="back-button" @click="goBack">
      <span>返回</span>
    </div>
  </div>
</template>

<script>
import AppHeader from '@/components/StationScreen/AppHeader.vue'
import { getPolicyDetail } from '@/api/renda/stationscreen'

export default {
  name: 'PolicyDetail',
  components: {
    AppHeader
  },
  dicts: ['rd_policy_type'],
  data() {
    return {
      policyId: null,
      activeTab: 'content',
      showAskDialog: false,
      askForm: {
        title: '',
        content: '',
        contact: ''
      },
      loading: false,
      showBackToTop: false,
      policyInfo: {
        id: null,
        title: '',
        department: '',
        publishTime: '',
        implementTime: '',
        validPeriod: '',
        viewCount: 0,
        type: 0,
      },
      policyContent: [],
    }
  },
  mounted() {
    // 根据路由参数获取政策详情
    this.policyId = this.$route.query.id || this.$route.params.id

    if (this.policyId) {
      this.loadPolicyDetail()
    } else {
      this.$message.error('政策ID不能为空')
      this.$router.back()
    }

    this.$nextTick(() => {
      // 禁用右键菜单
      document.oncontextmenu = function() {
        return false;
      };
    });

    document.addEventListener('keydown', this.handleKeydown)

    // 添加滚动监听
    const content = document.querySelector('.content')
    if (content) {
      content.addEventListener('scroll', this.handleScroll)
    }
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.handleKeydown)

    // 移除滚动监听
    const content = document.querySelector('.content')
    if (content) {
      content.removeEventListener('scroll', this.handleScroll)
    }
  },
  methods: {
    // 加载政策详情
    async loadPolicyDetail() {
      try {
        this.loading = true
        const response = await getPolicyDetail(this.policyId)

        if (response.code === 200) {
          this.policyInfo = response.data.policyInfo
          this.policyContent = response.data.policyContent || []

          // 格式化时间显示
          if (this.policyInfo.publishTime) {
            this.policyInfo.publishTime = this.formatDate(this.policyInfo.publishTime)
          }
          if (this.policyInfo.implementTime) {
            this.policyInfo.implementTime = this.formatDate(this.policyInfo.implementTime)
          }
        } else {
          this.$message.error(response.msg || '加载政策详情失败')
          this.$router.back()
        }
      } catch (error) {
        console.error('加载政策详情失败:', error)
        this.$message.error('加载政策详情失败')
        this.$router.back()
      } finally {
        this.loading = false
      }
    },

    // 根据政策类型值获取样式类
    getPolicyTypeClass(typeValue) {
      if (!typeValue || !this.dict.type.rd_policy_type) return 'normal'

      const typeItem = this.dict.type.rd_policy_type.find(item => item.value == typeValue)
      if (!typeItem) return 'normal'

      // 根据字典标签映射到CSS类名
      const labelToClass = {
        '重要政策': 'important',
        '民生政策': 'normal',
        '法规条例': 'regulation',
        '发展规划': 'planning'
      }

      return labelToClass[typeItem.label] || 'normal'
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}年${month}月${day}日`
    },

    // 返回顶部
    scrollToTop() {
      const content = document.querySelector('.content')
      if (content) {
        content.scrollTo({
          top: 0,
          behavior: 'smooth'
        })
      }
    },

    // 监听滚动事件
    handleScroll() {
      const content = document.querySelector('.content')
      if (content) {
        this.showBackToTop = content.scrollTop > 500
      }
    },

    // 处理键盘事件
    handleKeydown(event) {
      // ESC键返回政策列表
      if (event.key === 'Escape') {
        this.goBack()
      }
    },

    goBack() {
      this.$router.back()
    },
  }
}
</script>

<style lang="scss" scoped>
.policy-detail {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  font-family: 'AL-R' !important;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  margin: 0;
  padding: 0;
}

.content {
  flex: 1;
  padding: 80px 120px;
  max-width: 1800px;
  margin: 0 auto;
  overflow-y: auto;

  /* 隐藏滚动条 */
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

// 政策头部
.policy-header {
  background: #fff;
  border-radius: 24px;
  padding: 60px;
  margin-bottom: 60px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);

  .policy-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;

    .policy-tags {
      display: flex;
      gap: 20px;

      .policy-tag {
        padding: 12px 24px;
        border-radius: 25px;
        font-size: 24px;
        font-weight: 600;

        &.important {
          background: linear-gradient(135deg, #d71718, #b41616);
          color: #fff;
        }

        &.normal {
          background: rgba(52, 152, 219, 0.1);
          color: #3498db;
        }

        &.regulation {
          background: rgba(155, 89, 182, 0.1);
          color: #9b59b6;
        }

        &.planning {
          background: rgba(39, 174, 96, 0.1);
          color: #27ae60;
        }
      }

      .policy-status {
        padding: 12px 24px;
        border-radius: 25px;
        font-size: 24px;
        font-weight: 600;

        &.active {
          background: rgba(39, 174, 96, 0.1);
          color: #27ae60;
        }

        &.inactive {
          background: rgba(231, 76, 60, 0.1);
          color: #e74c3c;
        }

        &.draft {
          background: rgba(241, 196, 15, 0.1);
          color: #f1c40f;
        }
      }
    }

    .policy-actions {
      display: flex;
      gap: 20px;

      :deep(.el-button) {
        font-size: 20px;
        padding: 15px 30px;
        border-radius: 25px;

        &.el-button--primary {
          background: #d71718;
          border-color: #d71718;
        }
      }
    }
  }

  .policy-title {
    font-size: 64px;
    color: #333;
    margin: 0 0 40px;
    font-weight: 500;
    line-height: 1.3;
    font-family: 'AL-BL' !important;
    text-align: center;
  }

  .policy-info {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin-bottom: 40px;
    margin-left: 200px;

    .info-item {
      display: flex;
      align-items: center;
      font-family: 'AL-R' !important;

      .label {
        font-size: 28px;
        color: #666;
        margin-right: 15px;
        min-width: 120px;
      }

      .value {
        font-size: 28px;
        color: #333;
        font-weight: 600;
      }
    }
  }

  .policy-stats {
    display: flex;
    gap: 60px;
    padding-top: 30px;
    border-top: 2px solid #f0f0f0;

    .stat-item {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 22px;
      color: #666;

      i {
        font-size: 24px;
        color: #d71718;
      }
    }
  }
}

// 政策内容
.policy-content {
  background: #fff;
  border-radius: 24px;
  padding: 60px;
  margin-bottom: 60px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);

  .content-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 50px;
    padding-bottom: 30px;
    border-bottom: 2px solid #f0f0f0;

    .section-title {
      font-size: 48px;
      color: #333;
      margin: 0;
      font-weight: 700;
      font-family: 'AL-BL' !important;
    }

    .nav-tabs {
      display: flex;
      gap: 30px;

      .nav-tab {
        padding: 15px 30px;
        border-radius: 25px;
        font-size: 22px;
        background: #f8f9fa;
        color: #666;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          background: rgba(215, 23, 24, 0.1);
          color: #d71718;
        }

        &.active {
          background: #d71718;
          color: #fff;
        }
      }
    }
  }

  // 政策正文
  .policy-text {
    font-family: 'AL-R' !important;

    .text-section {
      margin-bottom: 50px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-heading {
        font-size: 36px;
        color: #333;
        margin: 0 0 30px;
        font-weight: 700;
        font-family: 'AL-BL' !important;
      }

      .section-content {
        font-size: 30px;
        line-height: 1.8;
        color: #555;
        text-align: left;
        text-indent: 2em;

        :deep(p) {
          margin: 0 0 20px;

          &:last-child {
            margin-bottom: 0;
          }
        }

        :deep(h4) {
          font-size: 28px;
          color: #333;
          margin: 30px 0 20px;
          font-weight: 600;
        }

        :deep(ul) {
          margin: 20px 0;
          padding-left: 30px;

          li {
            margin-bottom: 10px;
          }
        }
      }
    }
  }

  // 政策解读
  .policy-interpretation {
    .interpretation-item {
      margin-bottom: 40px;
      padding: 30px;
      background: #f8f9fa;
      border-radius: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .interpretation-title {
        font-size: 28px;
        color: #333;
        margin: 0 0 20px;
        font-weight: 700;
      }

      .interpretation-content {
        font-size: 22px;
        line-height: 1.7;
        color: #555;
        margin: 0;
      }
    }
  }

  // 相关文件
  .related-files {
    .file-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30px;
      margin-bottom: 20px;
      background: #f8f9fa;
      border-radius: 16px;
      transition: all 0.3s;

      &:hover {
        background: #f0f0f0;
      }

      &:last-child {
        margin-bottom: 0;
      }

      .file-info {
        display: flex;
        align-items: center;
        gap: 20px;

        .file-icon {
          width: 60px;
          height: 60px;
          border-radius: 12px;
          background: rgba(215, 23, 24, 0.1);
          display: flex;
          align-items: center;
          justify-content: center;
          color: #d71718;
          font-size: 32px;
        }

        .file-details {
          .file-name {
            font-size: 24px;
            color: #333;
            margin: 0 0 8px;
            font-weight: 600;
          }

          .file-meta {
            font-size: 18px;
            color: #999;
            margin: 0;

            span {
              margin-right: 20px;
            }
          }
        }
      }

      .file-actions {
        display: flex;
        gap: 15px;

        :deep(.el-button) {
          font-size: 18px;
          padding: 10px 20px;
          border-radius: 20px;

          &.el-button--primary {
            background: #d71718;
            border-color: #d71718;
          }
        }
      }
    }
  }

  // 问答互动
  .policy-qa {
    .qa-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 40px;

      h3 {
        font-size: 32px;
        color: #333;
        margin: 0;
        font-weight: 700;
      }

      :deep(.el-button) {
        font-size: 20px;
        padding: 12px 24px;
        border-radius: 20px;
        background: #d71718;
        border-color: #d71718;
      }
    }

    .qa-list {
      .qa-item {
        margin-bottom: 40px;
        padding: 30px;
        background: #f8f9fa;
        border-radius: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        .question {
          margin-bottom: 20px;

          .q-header {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            margin-bottom: 10px;

            .q-label {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              background: #d71718;
              color: #fff;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 20px;
              font-weight: 700;
              flex-shrink: 0;
            }

            .q-title {
              font-size: 24px;
              color: #333;
              margin: 0;
              font-weight: 600;
              line-height: 1.4;
            }
          }

          .q-meta {
            margin-left: 55px;
            font-size: 18px;
            color: #999;

            .q-user {
              margin-right: 20px;
            }
          }
        }

        .answer {
          .a-header {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            margin-bottom: 10px;

            .a-label {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              background: #27ae60;
              color: #fff;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 20px;
              font-weight: 700;
              flex-shrink: 0;
            }

            .a-content {
              font-size: 22px;
              color: #555;
              line-height: 1.6;
            }
          }

          .a-meta {
            margin-left: 55px;
            font-size: 18px;
            color: #999;

            .a-user {
              margin-right: 20px;
              color: #27ae60;
              font-weight: 600;
            }
          }
        }
      }
    }
  }
}

// 相关政策
.related-policies {
  background: #fff;
  border-radius: 24px;
  padding: 60px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);

  .section-title {
    font-size: 48px;
    color: #333;
    margin: 0 0 50px;
    font-weight: 700;
    font-family: 'AL-BL' !important;
  }

  .policies-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;

    .policy-card {
      background: #f8f9fa;
      border-radius: 20px;
      padding: 30px;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid transparent;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
        border-color: rgba(215, 23, 24, 0.1);
        background: #fff;
      }

      .card-header {
        margin-bottom: 20px;

        .policy-tag {
          padding: 8px 16px;
          border-radius: 20px;
          font-size: 18px;
          font-weight: 600;

          &.important {
            background: linear-gradient(135deg, #d71718, #b41616);
            color: #fff;
          }

          &.normal {
            background: rgba(52, 152, 219, 0.1);
            color: #3498db;
          }

          &.planning {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
          }
        }
      }

      .card-title {
        font-size: 24px;
        color: #333;
        margin: 0 0 15px;
        font-weight: 700;
        line-height: 1.4;
        font-family: 'AL-BL' !important;
      }

      .card-summary {
        font-size: 20px;
        color: #666;
        margin: 0 0 20px;
        line-height: 1.5;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .card-meta {
        display: flex;
        justify-content: space-between;
        font-size: 18px;
        color: #999;
      }
    }
  }
}

// 对话框样式
:deep(.el-dialog) {
  .el-dialog__header {
    padding: 30px 30px 0;

    .el-dialog__title {
      font-size: 24px;
      font-weight: 700;
    }
  }

  .el-dialog__body {
    padding: 30px;

    .el-form-item__label {
      font-size: 18px;
      font-weight: 600;
    }

    .el-input__inner,
    .el-textarea__inner {
      font-size: 16px;
    }
  }

  .el-dialog__footer {
    padding: 0 30px 30px;

    .el-button {
      font-size: 16px;
      padding: 12px 24px;
      border-radius: 20px;

      &.el-button--primary {
        background: #d71718;
        border-color: #d71718;
      }
    }
  }
}

// 空状态样式
.empty-content {
  background: #fff;
  border-radius: 24px;
  padding: 120px 60px;
  margin-bottom: 60px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
  text-align: center;

  .empty-text {
    i {
      font-size: 120px;
      color: #ddd;
      margin-bottom: 30px;
    }

    p {
      font-size: 32px;
      color: #999;
      margin: 0;
      font-family: 'AL-R' !important;
    }
  }
}

// 返回顶部按钮
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 220px;
  width: 160px;
  height: 60px;
  border-radius: 30px;
  background: linear-gradient(135deg, #d71718, #b41616);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  box-shadow: 0 8px 25px rgba(215, 23, 24, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1000;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(215, 23, 24, 0.4);
  }

  i {
    font-size: 24px;
  }

  span {
    font-size: 16px;
    font-weight: 500;
    font-family: 'AL-L';
  }
}

// 返回按钮
.back-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 80px;
  height: 80px;
  background: rgba(215, 23, 24, 0.9);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(215, 23, 24, 0.3);
  backdrop-filter: blur(10px);
  z-index: 1000;

  &:hover {
    background: rgba(215, 23, 24, 1);
    transform: scale(1.1);
    box-shadow: 0 8px 30px rgba(215, 23, 24, 0.5);
  }

  .back-icon {
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 8px 12px 8px 0;
    border-color: transparent #fff transparent transparent;
    margin-bottom: 4px;
  }

  span {
    color: #fff;
    font-size: 12px;
    font-weight: 500;
    font-family: 'AL-R';
  }
}

/* 4K分辨率适配 */
@media (min-width: 3000px) {
  .back-to-top {
    bottom: 60px;
    right: 860px;
    width: 220px;
    height: 80px;
    border-radius: 40px;
    gap: 15px;

    i {
      font-size: 32px;
    }

    span {
      font-size: 24px;
    }
  }

  .back-button {
    bottom: 60px;
    right: 60px;
    width: 120px;
    height: 120px;

    .back-icon {
      border-width: 12px 18px 12px 0;
      margin-bottom: 6px;
    }

    span {
      font-size: 24px;
    }
  }
}
</style>
