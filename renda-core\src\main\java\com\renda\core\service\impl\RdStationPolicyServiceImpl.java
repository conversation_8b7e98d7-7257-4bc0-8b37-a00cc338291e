package com.renda.core.service.impl;

import java.util.List;
import com.renda.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.renda.core.mapper.RdStationPolicyMapper;
import com.renda.core.domain.RdStationPolicy;
import com.renda.core.service.IRdStationPolicyService;

/**
 * 联络站政策Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Service
public class RdStationPolicyServiceImpl implements IRdStationPolicyService
{
    @Autowired
    private RdStationPolicyMapper rdStationPolicyMapper;

    /**
     * 查询联络站政策
     *
     * @param id 联络站政策主键
     * @return 联络站政策
     */
    @Override
    public RdStationPolicy selectRdStationPolicyById(Long id)
    {
        return rdStationPolicyMapper.selectRdStationPolicyById(id);
    }

    /**
     * 查询联络站政策列表
     *
     * @param rdStationPolicy 联络站政策
     * @return 联络站政策
     */
    @Override
    public List<RdStationPolicy> selectRdStationPolicyList(RdStationPolicy rdStationPolicy)
    {
        return rdStationPolicyMapper.selectRdStationPolicyList(rdStationPolicy);
    }

    /**
     * 新增联络站政策
     *
     * @param rdStationPolicy 联络站政策
     * @return 结果
     */
    @Override
    public int insertRdStationPolicy(RdStationPolicy rdStationPolicy)
    {
        rdStationPolicy.setCreateTime(DateUtils.getNowDate());
        return rdStationPolicyMapper.insertRdStationPolicy(rdStationPolicy);
    }

    /**
     * 修改联络站政策
     *
     * @param rdStationPolicy 联络站政策
     * @return 结果
     */
    @Override
    public int updateRdStationPolicy(RdStationPolicy rdStationPolicy)
    {
        rdStationPolicy.setUpdateTime(DateUtils.getNowDate());
        return rdStationPolicyMapper.updateRdStationPolicy(rdStationPolicy);
    }

    /**
     * 批量删除联络站政策
     *
     * @param ids 需要删除的联络站政策主键
     * @return 结果
     */
    @Override
    public int deleteRdStationPolicyByIds(Long[] ids)
    {
        return rdStationPolicyMapper.deleteRdStationPolicyByIds(ids);
    }

    /**
     * 删除联络站政策信息
     *
     * @param id 联络站政策主键
     * @return 结果
     */
    @Override
    public int deleteRdStationPolicyById(Long id)
    {
        return rdStationPolicyMapper.deleteRdStationPolicyById(id);
    }
}
